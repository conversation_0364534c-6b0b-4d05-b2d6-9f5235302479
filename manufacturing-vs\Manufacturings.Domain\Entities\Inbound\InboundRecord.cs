using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 入库记录主表
    /// </summary>
    public class InboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InboundSubject { get; set; } = string.Empty;

        /// <summary>
        /// 入库单号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 入库类型
        /// </summary>
        [Required]
        public InboundType InboundType { get; set; }

        /// <summary>
        /// 入库日期
        /// </summary>
        [Required]
        public DateTime InboundDate { get; set; }

        /// <summary>
        /// 入库仓库ID
        /// </summary>
        [Required]
        public int WarehouseId { get; set; }

        /// <summary>
        /// 入库人员ID
        /// </summary>
        public int? InboundPersonnelId { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        public int? RelatedProjectId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [Required]
        public InboundStatus Status { get; set; } = InboundStatus.Draft;
    }
} 