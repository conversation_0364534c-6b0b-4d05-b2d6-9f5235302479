namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 出库类型枚举
    /// </summary>
    public enum OutboundType
    {
        /// <summary>
        /// 内部领料
        /// </summary>
        InternalMaterialCollection = 1,

        /// <summary>
        /// 委外领料
        /// </summary>
        OutsourcedMaterialCollection = 2,

        /// <summary>
        /// 销售出库
        /// </summary>
        SalesOutbound = 3,

        /// <summary>
        /// 采购退货出库
        /// </summary>
        PurchaseReturnOutbound = 4,

        /// <summary>
        /// 其他出库
        /// </summary>
        OtherOutbound = 5
    }

    /// <summary>
    /// 单据状态枚举
    /// </summary>
    public enum DocumentStatus
    {
        /// <summary>
        /// 未完成
        /// </summary>
        Unfinished = 0,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 1,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 2
    }

    /// <summary>
    /// 审批方式枚举
    /// </summary>
    public enum ApprovalMethod
    {
        /// <summary>
        /// 依次审批
        /// </summary>
        Sequential = 1,

        /// <summary>
        /// 会签审批
        /// </summary>
        Joint = 2,

        /// <summary>
        /// 或签审批
        /// </summary>
        AnyOne = 3
    }

    /// <summary>
    /// 节点类型枚举
    /// </summary>
    public enum NodeType
    {
        /// <summary>
        /// 开始
        /// </summary>
        Start = 1,

        /// <summary>
        /// 审批
        /// </summary>
        Approval = 2,

        /// <summary>
        /// 结束
        /// </summary>
        End = 3
    }

    /// <summary>
    /// 操作类型枚举
    /// </summary>
    public enum OperationType
    {
        /// <summary>
        /// 新增
        /// </summary>
        Create = 1,

        /// <summary>
        /// 修改
        /// </summary>
        Update = 2,

        /// <summary>
        /// 删除
        /// </summary>
        Delete = 3,

        /// <summary>
        /// 查看
        /// </summary>
        View = 4,

        /// <summary>
        /// 上传文件
        /// </summary>
        UploadFile = 5,

        /// <summary>
        /// 审批
        /// </summary>
        Approve = 6,

        /// <summary>
        /// 驳回
        /// </summary>
        Reject = 7
    }

    /// <summary>
    /// 单据类型枚举
    /// </summary>
    public enum DocumentType
    {
        /// <summary>
        /// 工序分类
        /// </summary>
        ProcessClassification = 1,

        /// <summary>
        /// 工序
        /// </summary>
        Process = 2,

        /// <summary>
        /// 委外加工
        /// </summary>
        OutsourcingProcessing = 3,

        /// <summary>
        /// 委外工单
        /// </summary>
        OutsourcingWorkOrder = 4,

        /// <summary>
        /// 出库单
        /// </summary>
        OutboundOrder = 5,

        /// <summary>
        /// 审批流程
        /// </summary>
        ApprovalProcess = 6,

        /// <summary>
        /// 工单
        /// </summary>
        WorkOrder = 7,

        /// <summary>
        /// 物料
        /// </summary>
        Material = 8,

        /// <summary>
        /// 供应商
        /// </summary>
        Supplier = 9,

        /// <summary>
        /// 客户
        /// </summary>
        Customer = 10,

        /// <summary>
        /// 项目
        /// </summary>
        Project = 11,

        /// <summary>
        /// 仓库
        /// </summary>
        Warehouse = 12,

        /// <summary>
        /// 员工
        /// </summary>
        Employee = 13
    }

    /// <summary>
    /// 工单状态枚举
    /// </summary>
    public enum WorkOrderStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted = 1,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5
    }

    /// <summary>
    /// 物料类型枚举
    /// </summary>
    public enum MaterialType
    {
        /// <summary>
        /// 原材料
        /// </summary>
        RawMaterial = 1,

        /// <summary>
        /// 半成品
        /// </summary>
        SemiFinished = 2,

        /// <summary>
        /// 成品
        /// </summary>
        Finished = 3,

        /// <summary>
        /// 包装物
        /// </summary>
        Packaging = 4,

        /// <summary>
        /// 低值易耗品
        /// </summary>
        Consumable = 5
    }

    /// <summary>
    /// 供应商类型枚举
    /// </summary>
    public enum SupplierType
    {
        /// <summary>
        /// 生产商
        /// </summary>
        Manufacturer = 1,

        /// <summary>
        /// 代理商
        /// </summary>
        Agent = 2,

        /// <summary>
        /// 贸易商
        /// </summary>
        Trader = 3,

        /// <summary>
        /// 服务商
        /// </summary>
        ServiceProvider = 4
    }

    /// <summary>
    /// 客户类型枚举
    /// </summary>
    public enum CustomerType
    {
        /// <summary>
        /// 个人
        /// </summary>
        Individual = 1,

        /// <summary>
        /// 企业
        /// </summary>
        Enterprise = 2,

        /// <summary>
        /// 政府
        /// </summary>
        Government = 3,

        /// <summary>
        /// 其他
        /// </summary>
        Other = 4
    }

    /// <summary>
    /// 项目状态枚举
    /// </summary>
    public enum ProjectStatus
    {
        /// <summary>
        /// 立项
        /// </summary>
        Initiated = 1,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5
    }

    /// <summary>
    /// 员工状态枚举
    /// </summary>
    public enum EmployeeStatus
    {
        /// <summary>
        /// 在职
        /// </summary>
        Active = 1,

        /// <summary>
        /// 离职
        /// </summary>
        Resigned = 2,

        /// <summary>
        /// 试用期
        /// </summary>
        Probation = 3,

        /// <summary>
        /// 实习
        /// </summary>
        Internship = 4
    }
}

