namespace Manufacturings.Domain.Enums
{
    /// <summary>
    /// 质检工单状态枚举
    /// </summary>
    public enum QualityOrderStatus
    {
        /// <summary>
        /// 待质检
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 质检中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// 质检记录状态枚举
    /// </summary>
    public enum InspectionStatus
    {
        /// <summary>
        /// 待质检
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 质检中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 合格
        /// </summary>
        Qualified = 3,

        /// <summary>
        /// 不合格
        /// </summary>
        Unqualified = 4,

        /// <summary>
        /// 部分合格
        /// </summary>
        PartiallyQualified = 5
    }

    /// <summary>
    /// 项目阶段枚举
    /// </summary>
    public enum ProjectPhase
    {
        /// <summary>
        /// 立项
        /// </summary>
        Initiation = 1,

        /// <summary>
        /// 规划
        /// </summary>
        Planning = 2,

        /// <summary>
        /// 执行
        /// </summary>
        Execution = 3,

        /// <summary>
        /// 监控
        /// </summary>
        Monitoring = 4,

        /// <summary>
        /// 收尾
        /// </summary>
        Closure = 5
    }

    /// <summary>
    /// 项目状态枚举
    /// </summary>
    public enum ProjectStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted = 1,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5
    }
}
