using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售订单主表
    /// </summary>
    public class SalesOrder : BaseEntity
    {
        /// <summary>
        /// 销售订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 销售单号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SalesOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 销售日期
        /// </summary>
        [Required]
        public DateTime SalesDate { get; set; }

        /// <summary>
        /// 客户名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 客户经理ID
        /// </summary>
        public long? CustomerManagerId { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(20)]
        public string? ContactMethod { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 所属部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        public long? AssociatedProjectId { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [StringLength(50)]
        public string? CustomerOrderNumber { get; set; }

        /// <summary>
        /// 交货地址
        /// </summary>
        [StringLength(500)]
        public string? DeliveryAddress { get; set; }

        /// <summary>
        /// 结算方式
        /// </summary>
        [StringLength(100)]
        public string? SettlementMethod { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 订单状态（待确认、已确认、生产中、已完成、已取消等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OrderStatus { get; set; } = "待确认";

        /// <summary>
        /// 订单总金额（含税）
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 订单总数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalQuantity { get; set; }
    }
} 