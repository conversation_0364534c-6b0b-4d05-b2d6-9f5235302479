import { http } from "./axiosHttps";

const readBasicUrl= import.meta.env.VITE_ReadAPP_API_URL
const writeBasicUrl=import.meta.env.VITE_WriteAPP_API_URL;

// 仓库管理API配置 - 使用真实的后端API地址
// 根据后端控制器分析，仓库管理API在ManufacturingsERP.API项目中，默认端口应该是5107
const warehouseApiUrl = 'http://localhost:5107/';  // ManufacturingsERP.API项目端口
//登录
export const login =(params={})=>{
    return http("get",readBasicUrl + `api/Login/Login`,{},params);
}
//#region 用户
//显示
export const getUser =(params={})=>{
    return http("get",readBasicUrl + `api/User/GetUser`,{},params);
}
//添加
export const addUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/AddUser`,data,{});
}
//修改
export const updateUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/UpdateUser`,data,{});
}
//修改
export const updateUserState =(data={})=>{
    return http("post",writeBasicUrl + `api/User/UpdateUserState`,data,{});
}
//删除
export const deleteUser =(data={})=>{
    return http("post",writeBasicUrl + `api/User/DeleteUser`,data,{});
}
//#endregion

//#region 角色
//获取所有角色
export const getALLRole =(params={})=>{
    return http("get",readBasicUrl + `api/Role/GetAllRole`,{},params);
}
//获取角色列表
export const getRole =(params={})=>{
    return http("get",readBasicUrl + `api/Role/GetRole`,{},params);
}
//添加角色
export const addRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/AddRole`,data,{});
}
//修改角色
export const updateRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/UpdateRole`,data,{});
}
//修改角色
export const updateRoleState =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/UpdateRoleState`,data,{});
}
//删除角色
export const deleteRole =(data={})=>{
    return http("post",writeBasicUrl + `api/Role/DeleteRole`,data,{});
}
//#endregion

//#region  权限
//获取所有权限
export const getPermission =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetPermission`,{},params);
}
//获取权限级联
export const getMenu =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetCascadeItem`,{},params);
}
//获取全部权限
export const getAll =(params={})=>{
    return http("get",readBasicUrl + `api/Permission/GetAllPermission`,{},params);
}
//添加权限
export const addPermission=(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/CreatePermission`,data,{});
}
//修改权限
export const updatePermission =(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/UpdatePermission`,data,{});
}
//删除权限
export const deletePermission =(data={})=>{
    return http("post",writeBasicUrl + `api/Permission/Handle`,data,{});
}
//#endregion

//#region 仓库管理
//获取仓库列表 - 使用真实的后端API
export const getWarehouseList = (params = {}) => {
    // 根据WarehouseController.cs分析，正确的API路径是 GET /api/Warehouse/list
    const fullUrl = warehouseApiUrl + `api/Warehouse/list`;
    console.log('调用仓库列表API:', fullUrl, '参数:', params);
    return http("get", fullUrl, {}, params);
}
//添加仓库 - 使用真实的后端API
export const addWarehouse = (data = {}) => {
    // 根据WarehouseController.cs分析，正确的API路径是 POST /api/Warehouse
    return http("post", warehouseApiUrl + `api/Warehouse`, data, {});
}
//修改仓库 - 使用真实的后端API
export const updateWarehouse = (data = {}) => {
    // 根据WarehouseController.cs分析，正确的API路径是 PUT /api/Warehouse
    return http("put", warehouseApiUrl + `api/Warehouse`, data, {});
}
//删除仓库 - 使用真实的后端API
export const deleteWarehouse = (id: number) => {
    // 根据WarehouseController.cs分析，正确的API路径是 DELETE /api/Warehouse/{id}
    return http("delete", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库详情 - 使用真实的后端API
export const getWarehouseById = (id: number) => {
    // 根据WarehouseController.cs分析，正确的API路径是 GET /api/Warehouse/{id}
    return http("get", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库分类列表 - 使用真实的后端API
export const getWarehouseCategoryList = (params = {}) => {
    // 根据WarehouseCategoryController.cs分析，正确的API路径是 GET /api/WarehouseCategory/list
    return http("get", warehouseApiUrl + `api/WarehouseCategory/list`, {}, params);
}
//获取仓库分类（不分页）- 用于下拉选项
export const getAllWarehouseCategories = () => {
    // 根据WarehouseCategoryController.cs分析，正确的API路径是 GET /api/WarehouseCategory
    return http("get", warehouseApiUrl + `api/WarehouseCategory`, {}, {});
}
//获取存储类型列表 - 使用真实的后端API
export const getStorageTypeList = (params = {}) => {
    // 根据StorageTypeController.cs分析，正确的API路径是 GET /api/StorageType/list
    return http("get", warehouseApiUrl + `api/StorageType/list`, {}, params);
}
//获取存储类型（不分页）- 用于下拉选项
export const getAllStorageTypes = () => {
    // 根据StorageTypeController.cs分析，正确的API路径是 GET /api/StorageType
    return http("get", warehouseApiUrl + `api/StorageType`, {}, {});
}
//获取仓库结构列表 - 使用真实的后端API
export const getWarehouseStructureList = (params = {}) => {
    // 根据WarehouseStructureController.cs分析，正确的API路径是 GET /api/WarehouseStructure/list
    return http("get", warehouseApiUrl + `api/WarehouseStructure/list`, {}, params);
}
//获取仓库结构（不分页）- 用于下拉选项
export const getAllWarehouseStructures = () => {
    // 根据WarehouseStructureController.cs分析，正确的API路径是 GET /api/WarehouseStructure
    return http("get", warehouseApiUrl + `api/WarehouseStructure`, {}, {});
}
//获取人员列表 - 使用真实的后端API
export const getPersonList = (params = {}) => {
    // 根据PersonController.cs分析，正确的API路径是 GET /api/Person/list
    return http("get", warehouseApiUrl + `api/Person/list`, {}, params);
}
//获取人员（不分页）- 用于下拉选项
export const getAllPersons = () => {
    // 根据PersonController.cs分析，正确的API路径是 GET /api/Person
    return http("get", warehouseApiUrl + `api/Person`, {}, {});
}
//#endregion

