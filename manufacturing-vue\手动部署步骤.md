# 手动部署步骤

## 第一步：执行数据库脚本
请按照《数据库脚本执行指南.md》执行SQL脚本

## 第二步：确认控制器文件
确认以下文件已存在于后端项目中：
- `D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API\Controllers\WarehouseCategoryController.cs`
- `D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API\Controllers\StorageTypeController.cs`
- `D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API\Controllers\WarehouseStructureController.cs`
- `D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API\Controllers\PersonController.cs`

## 第三步：重启后端服务
1. 如果后端服务正在运行，请停止它
2. 在Visual Studio中重新编译并运行项目
3. 或者使用命令行：
   ```bash
   cd D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API
   dotnet run
   ```

## 第四步：测试新API
服务启动后，测试以下API端点：

### 测试仓库分类API
```
GET http://localhost:5107/api/WarehouseCategory/list
```

### 测试存储类型API
```
GET http://localhost:5107/api/StorageType/list
```

### 测试仓库结构API
```
GET http://localhost:5107/api/WarehouseStructure/list
```

### 测试人员API
```
GET http://localhost:5107/api/Person/list
```

## 第五步：验证前端显示
1. 确保前端开发服务器正在运行
2. 刷新仓库管理页面
3. 检查数据是否显示为真实的数据库内容

## 预期结果
如果一切正常，您应该看到：
- 仓库分类：原材料仓库
- 存储类型：常温存储
- 仓库结构：平面仓库
- 负责人：（如果Person表中有数据）

## 故障排除

### 如果API返回404
- 检查控制器文件是否正确复制
- 检查后端服务是否重新编译
- 检查路由配置是否正确

### 如果API返回500错误
- 检查数据库连接是否正常
- 检查数据库表是否创建成功
- 检查控制器代码是否有语法错误

### 如果前端仍显示默认数据
- 检查API是否返回正确数据
- 检查前端是否正确调用新的API
- 清除浏览器缓存并刷新页面
