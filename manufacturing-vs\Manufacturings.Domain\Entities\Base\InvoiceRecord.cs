using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用发票记录表
    /// </summary>
    public class InvoiceRecord : BaseEntity
    {
        /// <summary>
        /// 发票编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 发票类型（销售发票/采购发票）
        /// </summary>
        [Required]
        public InvoiceType InvoiceType { get; set; }

        /// <summary>
        /// 业务伙伴ID（客户ID或供应商ID）
        /// </summary>
        [Required]
        public long BusinessPartnerId { get; set; }

        /// <summary>
        /// 业务伙伴类型（客户/供应商）
        /// </summary>
        [Required]
        public BusinessPartnerType BusinessPartnerType { get; set; }

        /// <summary>
        /// 发票金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountExcludingTax { get; set; }

        /// <summary>
        /// 发票日期
        /// </summary>
        [Required]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// 开票日期
        /// </summary>
        public DateTime? IssueDate { get; set; }

        /// <summary>
        /// 付款截止日期
        /// </summary>
        public DateTime? PaymentDueDate { get; set; }

        /// <summary>
        /// 发票状态
        /// </summary>
        [Required]
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Issued;

        /// <summary>
        /// 发票备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 发票类型枚举
    /// </summary>
    public enum InvoiceType
    {
        /// <summary>
        /// 销售发票
        /// </summary>
        SalesInvoice = 1,
        /// <summary>
        /// 采购发票
        /// </summary>
        PurchaseInvoice = 2
    }

    /// <summary>
    /// 业务伙伴类型枚举
    /// </summary>
    public enum BusinessPartnerType
    {
        /// <summary>
        /// 客户
        /// </summary>
        Customer = 1,
        /// <summary>
        /// 供应商
        /// </summary>
        Supplier = 2
    }

    /// <summary>
    /// 发票状态枚举
    /// </summary>
    public enum InvoiceStatus
    {
        /// <summary>
        /// 已开票
        /// </summary>
        Issued = 1,
        /// <summary>
        /// 已付款
        /// </summary>
        Paid = 2,
        /// <summary>
        /// 部分付款
        /// </summary>
        PartiallyPaid = 3,
        /// <summary>
        /// 已作废
        /// </summary>
        Cancelled = 4,
        /// <summary>
        /// 逾期
        /// </summary>
        Overdue = 5
    }
} 