# EMS系统实体类模块组织说明

## 概述
本目录包含了EMS系统的所有实体类，按照业务模块进行了重新组织，使项目结构更加清晰和易于维护。

## 模块组织结构

### 📁 Base/ - 基础抽象类
包含所有业务实体的基础抽象类和通用功能表：
- `BusinessPartner.cs` - 业务伙伴基础抽象类
- `BusinessPartnerCategory.cs` - 业务伙伴类别表
- `BusinessPartnerLevel.cs` - 业务伙伴等级表
- `CommonAttachment.cs` - 通用附件表
- `CommonOperationRecord.cs` - 通用操作记录表
- `PaymentRecord.cs` - 通用付款记录表
- `InvoiceRecord.cs` - 通用发票记录表
- `InvoiceDetail.cs` - 通用发票明细表
- `BusinessPartnerFinancial.cs` - 通用财务信息表
- `BusinessPartnerBusiness.cs` - 通用业务信息表
- `BusinessPartnerContact.cs` - 通用联系人表
- `SimplifiedAddress.cs` - 简化地址表

### 📁 BusinessPartner/ - 业务伙伴模块
包含客户和供应商相关的实体类：
- `Customer.cs` - 客户主表
- `Supplier.cs` - 供应商主表
- `CustomerDeliveryAddress.cs` - 客户配送地址
- `CustomerOutboundRecord.cs` - 客户出库记录
- `CustomerSalesRecord.cs` - 客户销售记录
- `SupplierItem.cs` - 供应商物品表
- `SupplierInboundRecord.cs` - 供应商入库记录
- `SupplierPurchaseRecord.cs` - 供应商采购记录

### 📁 Sales/ - 销售模块
包含销售相关的所有实体类：
- `SalesOrder.cs` - 销售订单主表
- `SalesOrderDetail.cs` - 销售订单明细表
- `SalesOutboundRecord.cs` - 销售出库记录
- `SalesOutboundDetail.cs` - 销售出库明细
- `SalesReturnRecord.cs` - 销售退货记录
- `SalesReturnDetail.cs` - 销售退货明细
- `SalesReturnInboundRecord.cs` - 销售退货入库记录
- `SalesProductionRecord.cs` - 销售生产记录
- `SalesProductionDetail.cs` - 销售生产明细
- `SalesPurchaseRecord.cs` - 销售采购记录
- `SalesPurchaseDetail.cs` - 销售采购明细
- `SalesInvoiceRecord.cs` - 销售发票记录
- `SalesPaymentRecord.cs` - 销售收款记录

### 📁 Purchase/ - 采购模块
包含采购相关的所有实体类：
- `PurchaseOrder.cs` - 采购订单主表
- `PurchaseOrderDetail.cs` - 采购订单明细表
- `PurchaseOrderApprovalRecord.cs` - 采购订单审批记录
- `PurchaseOrderInspectionRecord.cs` - 采购订单检验记录
- `PurchaseOrderInspectionDetail.cs` - 采购订单检验明细
- `PurchaseOrderReturnRecord.cs` - 采购订单退货记录
- `PurchaseOrderReturnDetail.cs` - 采购订单退货明细

### 📁 Inbound/ - 入库模块
包含入库和检验相关的所有实体类：
- `InboundRecord.cs` - 入库记录主表
- `InboundDetail.cs` - 入库明细表
- `PurchaseInboundRecord.cs` - 采购入库记录
- `ProductionInboundRecord.cs` - 生产入库记录
- `ProductionReturnMaterialInboundRecord.cs` - 生产退料入库记录
- `OutsourcedProcessingInboundRecord.cs` - 委外加工入库记录
- `OutsourcedWorkOrderInboundRecord.cs` - 委外工单入库记录
- `OtherInboundRecord.cs` - 其他入库记录
- `GoodsArrivalInspection.cs` - 到货检验表
- `GoodsArrivalInspectionDetail.cs` - 到货检验明细表

### 📁 Warehouse/ - 仓库模块
包含仓库管理相关的实体类：
- `Warehouse.cs` - 仓库主表
- `WarehouseCategory.cs` - 仓库类别表
- `WarehouseStructure.cs` - 仓库结构表
- `StorageType.cs` - 存储类型表

### 📁 Inventory/ - 库存模块
包含库存管理相关的实体类：
- `ItemCategory.cs` - 物品类别表
- `ItemAttribute.cs` - 物品属性表
- `ItemType.cs` - 物品类型表

### 📁 Common/ - 通用基础模块
包含系统通用的基础实体类：
- `Person.cs` - 人员表
- `Department.cs` - 部门表
- `Project.cs` - 项目表
- `Address.cs` - 地址表
- `Industry.cs` - 行业表
- `BusinessNature.cs` - 营业性质表
- `PersonnelScale.cs` - 人员规模表
- `Location.cs` - 地区表

## 模块依赖关系

```
Base (基础抽象类)
├── BusinessPartner (业务伙伴)
│   ├── Sales (销售)
│   ├── Purchase (采购)
│   └── Inbound (入库)
├── Warehouse (仓库)
├── Inventory (库存)
└── Common (通用基础)
```

## 命名规范

1. **文件夹命名**：使用PascalCase，如`BusinessPartner`、`Sales`
2. **文件命名**：使用PascalCase，如`Customer.cs`、`SalesOrder.cs`
3. **类命名**：使用PascalCase，如`Customer`、`SalesOrder`
4. **属性命名**：使用PascalCase，如`CustomerName`、`OrderNumber`

## 维护说明

1. **新增实体类**：根据业务模块放入对应文件夹
2. **修改实体类**：保持在同一模块文件夹内
3. **删除实体类**：同时删除对应的文件引用
4. **模块重构**：更新此文档说明

## 注意事项

1. 所有实体类都应该继承自Base模块中的相应基类
2. 保持模块间的低耦合，高内聚
3. 新增模块时，需要更新相应的数据库上下文配置
4. 定期检查模块间的依赖关系，避免循环依赖 