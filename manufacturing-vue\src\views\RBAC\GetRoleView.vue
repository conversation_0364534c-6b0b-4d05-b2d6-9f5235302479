<template>
    <div class="rbac-container">
    <el-container>
      <el-header class="search-header">
        <el-form :inline="true" :model="query" class="search-form">
        <el-form-item label="角色名称">
          <el-input v-model="query.RoleName" placeholder="请输入角色名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" :icon="Search">查询</el-button>
          <el-button type="success" @click="open(null)" :icon="Plus">新增</el-button>
        </el-form-item>
      </el-form>
      </el-header>
      <el-main class="data-main">
           <el-table :data="tableData" class="data-table" :border="true" :stripe="true">
              <el-table-column prop="roleName" label="角色名称" width="120px"/>
              <el-table-column prop="permissionName" label="权限">
                <template v-slot="scoped">
                    <el-tag  type="primary">{{scoped.row.permissionName}}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="roleState" label="是否启用" width="100px" align="center">
                    <template v-slot="scoped">
                        <el-switch
                            v-model="scoped.row.roleState"
                            inline-prompt
                            active-text="启用"
                            inactive-text="禁用"
                            @change="updateState(scoped.row)"
                          />
                    </template>
              </el-table-column>
              <el-table-column type="index" label="序号" width="80px" align="center"/>
              <el-table-column prop="createTime" label="创建时间" width="180px">
                <template v-slot="scoped">
                    <span>{{moment(scoped.row.createTime).format("YYYY-MM-DD HH:mm:ss")}}</span>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="180px" align="center">
                <template v-slot="scoped">
                  <el-button link type="danger" size="small" @click="deleteRoles(scoped.row)" :icon="Delete">删除</el-button>
                  <el-button link type="primary" size="small" @click="open(scoped.row)" :icon="Edit">修改</el-button>
                </template>
              </el-table-column>
            </el-table>
            
             <el-pagination
              class="pagination"
              v-model:current-page="page.PageIndex"
              v-model:page-size="page.PageSize"
              :page-sizes="[3, 5, 8]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              prev-text="上一页"
              next-text="下一页"
              :total="page.totalCount"
            />

            <el-dialog v-model="logic.isopen" :title="logic.title" width="600" destroy-on-close center class="custom-dialog">
              <el-form
                  ref="ruleFormRef"
                  class="custom-form"
                  :model="ruleForm"
                  :rules="rules"
                  label-width="120"
                >
                  <el-form-item label="角色名称" prop="roleName">
                    <el-input v-model="ruleForm.roleName" placeholder="请输入角色名称" />
                  </el-form-item>
                  <el-form-item label="角色描述" prop="description">
                    <el-input v-model="ruleForm.description" placeholder="请输入角色描述" />
                  </el-form-item>
                  <el-form-item label="是否启用" prop="roleState">
                        <el-switch v-model="ruleForm.roleState" active-color="#13ce66" inactive-color="#ff4949" />
                </el-form-item>
                  <el-form-item label="选择权限" prop="permissionId">
                    <div class="permission-selection-header">选择权限</div>
                    <el-checkbox-group v-model="ruleForm.permissionId" class="permission-checkbox-group">
                        <el-checkbox v-for="item in permission" :key="item.id" :value="item.id" name="type" class="permission-checkbox">
                            {{item.permissionName}}
                        </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item class="form-buttons">
                    <el-button type="primary" @click="submitForm(ruleFormRef)" :icon="Check">
                      保存
                    </el-button>
                    <el-button @click="logic.isopen=false" :icon="Close">返回</el-button>
                  </el-form-item>
                </el-form>
            </el-dialog>
      </el-main>
    </el-container>
  </div>
 
</template>

<script lang="ts" setup>
import { getRole,addRole,updateRole,deleteRole, getAll,updateRoleState} from '@/Https/server';
import { onMounted, ref,reactive, watch } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import moment from 'moment';
import { Search, Plus, Edit, Delete, Check, Close } from '@element-plus/icons-vue'
import { useCounterStore } from '@/stores/counter';
const store = useCounterStore();
onMounted(()=>{
    GetRole();
})
//查询条件
const query =reactive({
    RoleName:''
})
const page =reactive({
    PageIndex:1,
    PageSize:3,
    totalCount:0,
    pageCount:0
})
//侦听器
watch(page,()=>
{
  GetRole();

},{deep:true})
//查询方法
const onSubmit = () => {
  GetRole();
}
//加载角色
const tableData = ref<any>([]);
const GetRole=()=>{
    getRole({
            RoleName:query.RoleName,
            PageIndex:page.PageIndex,
            PageSize:page.PageSize
        })
    .then((res:any)=>{
        tableData.value=res.data.pageData;
        page.totalCount = res.data.totalCount;
        page.pageCount = res.data.pageCount;
    })
}
//逻辑参数
const logic = reactive({
   isopen:false,
   isAdd:false,
   title:'',
})

//加载权限
const permission = ref<any>([]);
const GetPermissionList=()=>{
    getAll()
    .then((res:any)=>{
        permission.value=res.data;
    })
}
//#region 增删改
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  "id": 0,
  "roleName": "",
  "description": "",
  "roleState": true,
  "permissionId": [],
  "createId": 0,
  "createTime": ""
})
//数据验证
const rules = reactive<FormRules>({
  roleName: [
    { required: true, message: '角色名称不能为空', trigger: 'blur' },
  ],
   description: [
    { required: true, message: '角色描述不能为空', trigger: 'blur' },
  ],
   permissionId: [
    { required: true, message: '必须选择至少一个权限', trigger: 'blur' },
  ],
})
//提交
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if(logic.isAdd==true)
      {
          addRole(ruleForm)
          .then((res:any)=>{
            if(res.code==200){
              logic.isopen =false;
              GetRole();
              store.getMenus();
              ElMessage.success(res.message);
            }else{
              ElMessage.error(res.message);
            }
          })
      }
      else
      {
       updateRole(ruleForm)
        .then((res:any)=>{
          if(res.code==200){
            logic.isopen =false;
            GetRole();
            store.getMenus();
            ElMessage.success(res.message);
          }else{
            ElMessage.error(res.message);
          }
        })
      }
      
    } else {
      console.log('error submit!', fields)
    }
  })
}
//打开方法
const open =(row:any)=>{
  logic.isopen=true;
  GetPermissionList();
  if(row==null)
  {
      logic.isAdd =true;
      logic.title="新增角色"
      ruleForm.id =0;
      ruleForm.description ='';
      ruleForm.permissionId = [];
      ruleForm.roleName='';
  }
  else
  {
    console.log(row);
      logic.isAdd =false;
      logic.title="修改角色"
      ruleForm.id =row.id;
      ruleForm.description =row.description;
      ruleForm.permissionId = row.permissionId;
      ruleForm.roleName=row.roleName;
      ruleForm.roleState=row.roleState;
      ruleForm.createId = row.createId;
      ruleForm.createTime =row.createTime;
  }
}

const deleteRoles=(row:any)=>{
   ElMessageBox.confirm(
    '确认删除吗?',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      if(row.roleState==true)
      {
        ElMessage.error("角色使用中，无法删除");
        return;
      }
        deleteRole(row).then((res:any)=>{
          if(res.code==200)
          {
            GetRole();
            ElMessage.success(res.message);
          }
          else
          {
            ElMessage.error(res.message);
          }
        })
    })
  
}
//修改状态
const updateState=(row:any)=>{
  updateRoleState(row).then((res:any)=>{
    if(res.code==200)
    {
      GetRole();
      ElMessage.success(res.message);
    }
    else
    {
      GetRole();
      ElMessage.error(res.message);
    }
  })
}

//#endregion
</script>

<style>
/* ==================== 容器布局 ==================== */
.rbac-container {
  padding: 15px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.search-header {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 15px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.data-main {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  min-height: 500px;
}
/* ==================== 容器布局结束 ==================== */

/* ==================== 表格与分页 ==================== */
.data-table {
  margin-bottom: 15px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
/* ==================== 表格与分页结束 ==================== */

/* ==================== 对话框样式 ==================== */
.custom-dialog .el-dialog__header {
  background-color: #f5f7fa;
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.custom-form {
  padding: 20px 0;
}

.form-buttons {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}
/* ==================== 对话框样式结束 ==================== */

/* ==================== 表单样式 ==================== */
/* 维持Element Plus输入框和选择框的宽度 */
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
/* ==================== 表单样式结束 ==================== */

/* ==================== 权限选择相关 ==================== */
.permission-selection-header {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: bold;
}

.permission-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.permission-checkbox {
  width: 180px;
  height: 45px;
  margin: 5px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-sizing: border-box;
}

.permission-checkbox .el-checkbox__label {
  font-size: 14px;
}

.permission-checkbox.is-checked {
  background-color: #ecf5ff;
  border-color: #409eff;
}
/* ==================== 权限选择相关结束 ==================== */

/* ==================== 树形控件 ==================== */
.is-penultimate > .el-tree-node__content {
  color: #626aef;
}
.is-penultimate > .el-tree-node__children > div {
  display: inline-block;
  margin-right: 4px;

  &:not(:first-child) .el-tree-node__content {
    padding-left: 0px !important;
  }
  .el-tree-node__content {
    padding-right: 16px;
  }
}
/* ==================== 树形控件结束 ==================== */
</style>