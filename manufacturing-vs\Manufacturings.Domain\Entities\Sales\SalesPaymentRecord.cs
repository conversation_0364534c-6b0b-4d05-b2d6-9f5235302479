using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售收款记录表
    /// </summary>
    public class SalesPaymentRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 收入单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string IncomeOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 收入主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string IncomeSubject { get; set; } = string.Empty;

        /// <summary>
        /// 收入类型（销售收入、其他收入等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string IncomeType { get; set; } = string.Empty;

        /// <summary>
        /// 收入日期
        /// </summary>
        [Required]
        public DateTime IncomeDate { get; set; }

        /// <summary>
        /// 付款方
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Payer { get; set; } = string.Empty;

        /// <summary>
        /// 收入金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal IncomeAmount { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string BankAccountNumber { get; set; } = string.Empty;

        /// <summary>
        /// 银行名称
        /// </summary>
        [StringLength(100)]
        public string? BankName { get; set; }

        /// <summary>
        /// 收款方式（现金、银行转账、支票、其他等）
        /// </summary>
        [StringLength(100)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 收款状态（待收款、已收款、已确认等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PaymentStatus { get; set; } = "待收款";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 