using System.ComponentModel.DataAnnotations;
using MediatR;
using Manufacturings.Infrastructrue.Error;

namespace ManufacturingsERP.API.Application.DTOs
{
    /// <summary>
    /// 仓库DTO
    /// </summary>
    public class WarehouseDto
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WarehouseNumber { get; set; } = string.Empty;

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 上级仓库ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 上级仓库名称
        /// </summary>
        public string? ParentName { get; set; }

        /// <summary>
        /// 仓库分类ID
        /// </summary>
        public int CategoryId { get; set; }

        /// <summary>
        /// 仓库分类名称
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 存储类型ID
        /// </summary>
        public int? StorageTypeId { get; set; }

        /// <summary>
        /// 存储类型名称
        /// </summary>
        public string? StorageTypeName { get; set; }

        /// <summary>
        /// 仓库结构ID
        /// </summary>
        public int? StructureId { get; set; }

        /// <summary>
        /// 仓库结构名称
        /// </summary>
        public string? StructureName { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? PersonInChargeId { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string? PersonInChargeName { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        public string? Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 创建人
        /// </summary>
        public string CreateName { get; set; } = string.Empty;

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        public string? ModifierName { get; set; }

        /// <summary>
        /// 子仓库列表
        /// </summary>
        public List<WarehouseDto> Children { get; set; } = new();

        /// <summary>
        /// 是否有子仓库
        /// </summary>
        public bool HasChildren { get; set; }
    }

    /// <summary>
    /// 仓库详情DTO
    /// </summary>
    public class WarehouseDetailDto : WarehouseDto
    {
        // 继承自WarehouseDto，包含所有基本信息
    }

    /// <summary>
    /// 创建仓库命令
    /// </summary>
    public class CreateWarehouseCommand : IRequest<APIResult<long>>
    {
        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required(ErrorMessage = "仓库名称不能为空")]
        [StringLength(100, ErrorMessage = "仓库名称长度不能超过100个字符")]
        public string WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 上级仓库ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 仓库分类ID
        /// </summary>
        [Required(ErrorMessage = "仓库分类不能为空")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 存储类型ID
        /// </summary>
        public int? StorageTypeId { get; set; }

        /// <summary>
        /// 仓库结构ID
        /// </summary>
        public int? StructureId { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? PersonInChargeId { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [StringLength(200, ErrorMessage = "仓库地址长度不能超过200个字符")]
        public string? Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;
    }

    /// <summary>
    /// 更新仓库命令
    /// </summary>
    public class UpdateWarehouseCommand : IRequest<APIResult<bool>>
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "仓库ID不能为空")]
        public long Id { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required(ErrorMessage = "仓库名称不能为空")]
        [StringLength(100, ErrorMessage = "仓库名称长度不能超过100个字符")]
        public string WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 上级仓库ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 仓库分类ID
        /// </summary>
        [Required(ErrorMessage = "仓库分类不能为空")]
        public int CategoryId { get; set; }

        /// <summary>
        /// 存储类型ID
        /// </summary>
        public int? StorageTypeId { get; set; }

        /// <summary>
        /// 仓库结构ID
        /// </summary>
        public int? StructureId { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? PersonInChargeId { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [StringLength(200, ErrorMessage = "仓库地址长度不能超过200个字符")]
        public string? Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; }
    }

    /// <summary>
    /// 删除仓库命令
    /// </summary>
    public class DeleteWarehouseCommand : IRequest<APIResult<bool>>
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "仓库ID不能为空")]
        public long Id { get; set; }
    }

    /// <summary>
    /// 根据ID获取仓库查询
    /// </summary>
    public class GetWarehouseByIdQuery : IRequest<APIResult<WarehouseDetailDto>>
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        [Required(ErrorMessage = "仓库ID不能为空")]
        public long Id { get; set; }
    }
} 