﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Write.Application.Command.User;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Controllers.RBAC
{
    /// <summary>
    /// 用户控制器
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class UserController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public UserController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 添加用户信息
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> AddUser(CreateUserCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 修改用户信息
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> UpdateUser(UpdateUserCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 修改用户状态
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> UpdateUserState(UpdateUserStateCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 删除用户信息
        /// </summary>
        /// <param name="command">命令</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> DeleteUser(DeleteUserCommand command)
        {
            return await mediator.Send(command);
        }
    }
}
