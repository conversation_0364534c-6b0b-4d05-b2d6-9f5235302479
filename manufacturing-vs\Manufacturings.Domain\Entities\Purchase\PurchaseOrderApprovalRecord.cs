using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单审批记录表
    /// </summary>
    public class PurchaseOrderApprovalRecord : BaseEntity
    {
        /// <summary>
        /// 采购订单ID
        /// </summary>
        [Required]
        public int PurchaseOrderId { get; set; }

        /// <summary>
        /// 审批节点名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ApprovalNode { get; set; } = string.Empty;

        /// <summary>
        /// 节点序号
        /// </summary>
        [Required]
        public int NodeSequence { get; set; }

        /// <summary>
        /// 办理人员ID
        /// </summary>
        [Required]
        public int ProcessorId { get; set; }

        /// <summary>
        /// 审批状态（待审批、已审批、已驳回等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ApprovalStatus { get; set; } = "待审批";

        /// <summary>
        /// 接收时间
        /// </summary>
        [Required]
        public DateTime ReceiveTime { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmitTime { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        [StringLength(1000)]
        public string? ApprovalComments { get; set; }

        /// <summary>
        /// 驳回原因
        /// </summary>
        [StringLength(1000)]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// 是否驳回到上一级
        /// </summary>
        public bool RejectToPreviousLevel { get; set; } = false;

        /// <summary>
        /// 是否驳回到申请人
        /// </summary>
        public bool RejectToApplicant { get; set; } = false;

        /// <summary>
        /// 审批流程ID
        /// </summary>
        public int? ApprovalProcessId { get; set; }
    }
} 