using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 供应商入库记录表
    /// </summary>
    public class SupplierInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 入库订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InboundOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 入库日期
        /// </summary>
        [Required]
        public DateTime InboundDate { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        [Required]
        [StringLength(100)]
        public string InboundType { get; set; } = string.Empty;

        /// <summary>
        /// 入库总量
        /// </summary>
        [Required]
        public int TotalInboundQuantity { get; set; }

        /// <summary>
        /// 入库金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InboundAmount { get; set; }

        /// <summary>
        /// 入库人员ID
        /// </summary>
        public long? InboundPersonId { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public long SupplierId { get; set; }
    }
} 