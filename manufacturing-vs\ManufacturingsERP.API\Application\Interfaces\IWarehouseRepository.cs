using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Warehouses;

namespace ManufacturingsERP.API.Application.Interfaces
{
    /// <summary>
    /// 仓库仓储接口
    /// </summary>
    public interface IWarehouseRepository : IBaseRepository<Warehouse>
    {
        /// <summary>
        /// 根据仓库编号获取仓库
        /// </summary>
        /// <param name="warehouseNumber">仓库编号</param>
        /// <returns>仓库实体</returns>
        Task<Warehouse?> GetByWarehouseNumberAsync(string warehouseNumber);

        /// <summary>
        /// 检查仓库名称是否已存在
        /// </summary>
        /// <param name="warehouseName">仓库名称</param>
        /// <param name="excludeId">排除的仓库ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsWarehouseNameExistsAsync(string warehouseName, long? excludeId = null);

        /// <summary>
        /// 获取仓库树形结构
        /// </summary>
        /// <returns>仓库树形结构</returns>
        Task<List<WarehouseTreeDto>> GetWarehouseTreeAsync();
    }

    /// <summary>
    /// 仓库树形结构DTO
    /// </summary>
    public class WarehouseTreeDto
    {
        /// <summary>
        /// 仓库ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public string WarehouseNumber { get; set; } = string.Empty;

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 上级仓库ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 子仓库列表
        /// </summary>
        public List<WarehouseTreeDto> Children { get; set; } = new();
    }
} 