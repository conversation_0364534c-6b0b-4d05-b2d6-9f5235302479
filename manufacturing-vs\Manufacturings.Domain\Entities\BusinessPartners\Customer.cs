using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Sales;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 客户主表（继承自BusinessPartner）
    /// </summary>
    public class Customer : BusinessPartner
    {
        /// <summary>
        /// 客户编号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CustomerNumber { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 客户等级ID（使用BusinessPartner的PartnerLevelId）
        /// </summary>
        [NotMapped]
        public long? CustomerLevelId => PartnerLevelId;

        /// <summary>
        /// 客户类别ID（使用BusinessPartner的PartnerCategoryId）
        /// </summary>
        [NotMapped]
        public long? CustomerCategoryId => PartnerCategoryId;

        /// <summary>
        /// 客户类型（个人/企业）
        /// </summary>
        [Required]
        public CustomerType CustomerType { get; set; } = CustomerType.Enterprise;

        /// <summary>
        /// 信用额度
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CreditLimit { get; set; }

        /// <summary>
        /// 已用信用额度
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? UsedCreditLimit { get; set; }

        /// <summary>
        /// 信用等级（A/B/C/D）
        /// </summary>
        [StringLength(1)]
        public string? CreditRating { get; set; }

        /// <summary>
        /// 付款条件（天数）
        /// </summary>
        public int? PaymentTerms { get; set; }

        /// <summary>
        /// 销售代表ID
        /// </summary>
        public long? SalesRepresentativeId { get; set; }

        /// <summary>
        /// 客户来源
        /// </summary>
        [StringLength(50)]
        public string? CustomerSource { get; set; }

        /// <summary>
        /// 客户状态
        /// </summary>
        [Required]
        public CustomerStatus CustomerStatus { get; set; } = CustomerStatus.Active;
    }

    /// <summary>
    /// 客户类型枚举
    /// </summary>
    public enum CustomerType
    {
        /// <summary>
        /// 个人
        /// </summary>
        Individual = 1,
        /// <summary>
        /// 企业
        /// </summary>
        Enterprise = 2
    }

    /// <summary>
    /// 客户状态枚举
    /// </summary>
    public enum CustomerStatus
    {
        /// <summary>
        /// 活跃
        /// </summary>
        Active = 1,
        /// <summary>
        /// 非活跃
        /// </summary>
        Inactive = 2,
        /// <summary>
        /// 黑名单
        /// </summary>
        Blacklisted = 3
    }
} 