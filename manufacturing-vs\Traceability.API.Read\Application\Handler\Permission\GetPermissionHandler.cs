﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using Traceability.API.Read.Application.Command.Permission;
using Traceability.API.Read.Dto;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.Permission
{
    public class GetPermissionHandler : IRequestHandler<GetPermissionCommand, APIResult<APIPageing<PermissionModel>>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepo;
        private readonly ILogger<GetPermissionHandler> _logger;

        public GetPermissionHandler(IBaseRepository<PermissionModel> permissionRepo, ILogger<GetPermissionHandler> logger)
        {
            this.permissionRepo = permissionRepo;
            this._logger = logger;
        }

        public Task<APIResult<APIPageing<PermissionModel>>> Handle(GetPermissionCommand request, CancellationToken cancellationToken)
        {
            APIResult<APIPageing<PermissionModel>> result = new APIResult<APIPageing<PermissionModel>>();
            try
            {
                var permission = permissionRepo.GetAll().AsNoTracking();
                if (!string.IsNullOrEmpty(request.PermissionsName))
                {
                    permission = permission.Where(x => x.PermissionName.Contains(request.PermissionsName));
                }

                //分页
                var query = permission.OrderBy(x => x.OrderNo).Skip((request.PageIndex - 1) * request.PageSize).Take(request.PageSize).ToList();
                var totalCount = permission.Count();
                var pageCount = (int)Math.Ceiling((totalCount * 1.0) / request.PageSize);

                result.Code = ResultCode.Success;
                result.Message = "获取权限成功";
                
                APIPageing<PermissionModel> pageing = new APIPageing<PermissionModel>();
                pageing.TotalCount = totalCount;
                pageing.PageCount = pageCount;
                pageing.PageData = query;

                result.Data = pageing;
                _logger.LogInformation($"获取权限成功");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取权限失败：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "获取权限失败";
                return Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
