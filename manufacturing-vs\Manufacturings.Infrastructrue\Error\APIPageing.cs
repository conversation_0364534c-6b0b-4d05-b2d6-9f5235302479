﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Manufacturings.Infrastructrue.Error
{
    /// <summary>
    /// 分页对象
    /// </summary>
    /// <typeparam name="T">模型</typeparam>
    public class APIPageing<T> : APIResult<T> where T : class
    {
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
        /// <summary>
        /// 总页数
        /// </summary>
        public int PageCount { get; set; }
        /// <summary>
        /// 分页集合
        /// </summary>
        public List<T> PageData { get; set; }
    }
}
