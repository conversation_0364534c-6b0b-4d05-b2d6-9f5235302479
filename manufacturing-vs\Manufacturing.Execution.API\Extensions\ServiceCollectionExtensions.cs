﻿using System.Reflection;
using Yitter.IdGenerator;
using NLog.Web;
using Microsoft.EntityFrameworkCore;
using NLog.Extensions.Logging;
using System.Text;
using Microsoft.OpenApi.Models;
using Manufacturing.Execution.API.Common;
using Manufacturings.Infrastructrue;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Manufacturings.Infrastructure;

namespace Manufacturing.Execution.API.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static WebApplicationBuilder Inject(this WebApplicationBuilder builder)
        {
            builder.Services.AddControllers();
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            builder.Services.AddEndpointsApiExplorer();
            //配置Swagger
            builder.Services.AddSwaggerGen(o =>
            {
                var path = AppDomain.CurrentDomain.BaseDirectory + "Traceability.API.Read.xml";
                o.IncludeXmlComments(path, true);
                // 显示 授权信息
                o.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Description = "添加JWT授权Token：Bearer Token值",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    BearerFormat = "JWT",
                    Scheme = "Bearer"
                });
                o.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        new string[]
                        {

                        }
                    }
                });
            });
            //注册上下文EFCore上下文，开发环境下启用SQL日志记录
            builder.Services.AddDBContextAccessor<MyDbContext>(builder.Configuration.GetConnectionString("MySQL"), builder.Environment.IsDevelopment());
            //注册雪花编号
            YitIdHelper.SetIdGenerator(new IdGeneratorOptions(1));
            //注册AutoMapper
            builder.Services.AddAutoMapper(typeof(MappingProfile));
            //注入日志
            builder.Host.UseNLog();
            //注册仓储
            builder.Services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
            //注册服务
            builder.Services.AddScoped<IIdentifyService, IdentifyService>();
            builder.Services.AddHttpContextAccessor();
            
            //注册consul
            //builder.Configuration.Register();

            // JWT Authentication 配置
            var jwtSettings = builder.Configuration.GetSection("JwtSettings");
            var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]);

            builder.Services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(secretKey),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidateAudience = true,
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero // 默认是5分钟，这里设置为0
                };
            });
            builder.Services.AddAuthorization();

            return builder;
        }
        /// <summary>
        /// 注册EFCore上下文
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="services"></param>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="enablerSqlLog">是否启用SQL日志</param>
        /// <returns></returns>
        public static IServiceCollection AddDBContextAccessor<T>(this IServiceCollection services,string connectionString,bool enablerSqlLog) where T : DbContext
        {
            return services.AddDbContext<MyDbContext>(x =>
            {
                x.UseMySql(connectionString, new MySqlServerVersion("5.7"), action =>
                {
                    action.EnableRetryOnFailure(
                        maxRetryCount: 10, //最大重试次数
                        maxRetryDelay: TimeSpan.FromSeconds(60),//每次重试间隔时间，最大延迟
                        errorNumbersToAdd: null);//需要重试的 SQL Server 错误代码。
                })
                .EnableSensitiveDataLogging();//启用敏感数据日志记录，便于调试，但在生产环境中应谨慎使用以避免泄露敏感信息
                if (enablerSqlLog)
                {
                    x.UseLoggerFactory(LoggerFactory.Create(builder => builder.AddFilter(category: DbLoggerCategory.Database.Name, level: LogLevel.Information).AddConsole()));//控制台日志
                }
            });
        }
    }
}
