using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 到货检验主表
    /// </summary>
    public class GoodsArrivalInspection : BaseEntity
    {
        /// <summary>
        /// 检验主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InspectionSubject { get; set; } = string.Empty;

        /// <summary>
        /// 检验单号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 检验日期
        /// </summary>
        [Required]
        public DateTime InspectionDate { get; set; }

        /// <summary>
        /// 检验人员ID
        /// </summary>
        [Required]
        public int InspectorId { get; set; }

        /// <summary>
        /// 所在部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(20)]
        public string? ContactMethod { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PurchaseOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 采购日期
        /// </summary>
        public DateTime? PurchaseDate { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 采购人员ID
        /// </summary>
        public int? PurchasingAgentId { get; set; }

        /// <summary>
        /// 采购部门ID
        /// </summary>
        public int? PurchasingDepartmentId { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        public int? AssociatedProjectId { get; set; }

        /// <summary>
        /// 检验状态（待检验、检验中、已完成、已驳回等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionStatus { get; set; } = "待检验";

        /// <summary>
        /// 总体检验结果（合格、不合格、部分合格等）
        /// </summary>
        [StringLength(50)]
        public string? OverallInspectionResult { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 最后修改人ID
        /// </summary>
        public int? LastModifiedBy { get; set; }
    }
} 