{"openapi": "3.0.1", "info": {"title": "Traceability.API.Read", "version": "1.0"}, "paths": {"/api/Login/Login": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "登录", "parameters": [{"name": "UserName", "in": "query", "description": "用户名", "style": "form", "schema": {"type": "string"}}, {"name": "PassWord", "in": "query", "description": "密码", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginUserDtoAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginUserDtoAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginUserDtoAPIResult"}}}}}}}, "/api/Login/Refresh": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "刷新Token", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenQueryCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenQueryCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenQueryCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponseDtoAPIResult"}}}}}}}, "/api/Login/Export": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "导出用户信息", "responses": {"200": {"description": "Success"}}}}, "/api/Permission/GetPermission": {"get": {"tags": ["Permission"], "summary": "获取权限列表", "parameters": [{"name": "PermissionsName", "in": "query", "description": "权限名称", "style": "form", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "description": "页码", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "页容量", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionModelAPIPageingAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionModelAPIPageingAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionModelAPIPageingAPIResult"}}}}}}}, "/api/Permission/GetAllPermission": {"get": {"tags": ["Permission"], "summary": "获取权限", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PermissionModelListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PermissionModelListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PermissionModelListAPIResult"}}}}}}}, "/api/Permission/GetCascadeItem": {"get": {"tags": ["Permission"], "summary": "动态菜单权限", "parameters": [{"name": "RoleId", "in": "query", "style": "form", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CascadeItemListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CascadeItemListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CascadeItemListAPIResult"}}}}}}}, "/api/Role/GetRole": {"get": {"tags": ["Role"], "summary": "获取角色及角色权限列表", "parameters": [{"name": "RoleName", "in": "query", "description": "角色名称", "style": "form", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "description": "页码", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "页容量", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleDtoAPIPageingAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleDtoAPIPageingAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleDtoAPIPageingAPIResult"}}}}}}}, "/api/Role/GetAllRole": {"get": {"tags": ["Role"], "summary": "获取所有角色", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RoleModelListAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RoleModelListAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RoleModelListAPIResult"}}}}}}}, "/api/User/GetUser": {"get": {"tags": ["User"], "summary": "获取用户及用户角色", "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "用户姓名", "style": "form", "schema": {"type": "string"}}, {"name": "PageIndex", "in": "query", "description": "页码", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "description": "页容量", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserDtoAPIPageingAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserDtoAPIPageingAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserDtoAPIPageingAPIResult"}}}}}}}}, "components": {"schemas": {"CascadeItem": {"type": "object", "properties": {"value": {"type": "integer", "format": "int64"}, "label": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/CascadeItem"}, "nullable": true}}, "additionalProperties": false}, "CascadeItemListAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CascadeItem"}, "nullable": true}}, "additionalProperties": false}, "LoginUserDto": {"type": "object", "properties": {"id": {"type": "integer", "description": "用户Id", "format": "int64"}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "姓名", "nullable": true}, "userId": {"type": "integer", "description": "用户Id", "format": "int32"}, "roleId": {"type": "string", "description": "角色Id", "nullable": true}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}}, "additionalProperties": false, "description": "登录用户Dto"}, "LoginUserDtoAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/LoginUserDto"}}, "additionalProperties": false}, "PermissionModel": {"required": ["permissionName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifierId": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "permissionName": {"maxLength": 100, "minLength": 0, "type": "string"}, "permissionUrl": {"maxLength": 100, "minLength": 0, "type": "string", "nullable": true}, "orderNo": {"type": "integer", "format": "int32"}, "parentId": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "PermissionModelAPIPageing": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/PermissionModel"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "pageData": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionModel"}, "nullable": true}}, "additionalProperties": false}, "PermissionModelAPIPageingAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/PermissionModelAPIPageing"}}, "additionalProperties": false}, "PermissionModelListAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionModel"}, "nullable": true}}, "additionalProperties": false}, "RefreshTokenQueryCommand": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "授权token", "nullable": true}, "refreshToken": {"type": "string", "description": "刷新token", "nullable": true}}, "additionalProperties": false}, "ResultCode": {"enum": [200, 500], "type": "integer", "format": "int32"}, "RoleDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "roleState": {"type": "boolean", "description": "是否启用"}, "description": {"type": "string", "description": "角色描述", "nullable": true}, "permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "additionalProperties": false}, "RoleDtoAPIPageing": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/RoleDto"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "pageData": {"type": "array", "items": {"$ref": "#/components/schemas/RoleDto"}, "nullable": true}}, "additionalProperties": false}, "RoleDtoAPIPageingAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/RoleDtoAPIPageing"}}, "additionalProperties": false}, "RoleModel": {"required": ["<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "createId": {"type": "integer", "format": "int64"}, "createTime": {"type": "string", "format": "date-time"}, "modifierId": {"type": "integer", "format": "int64", "nullable": true}, "modifyTime": {"type": "string", "format": "date-time", "nullable": true}, "isDeleted": {"type": "boolean"}, "roleName": {"maxLength": 100, "minLength": 0, "type": "string"}, "roleState": {"type": "boolean"}, "description": {"maxLength": 300, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "RoleModelListAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/RoleModel"}, "nullable": true}}, "additionalProperties": false}, "TokenResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "授权token", "nullable": true}, "refreshToken": {"type": "string", "description": "刷新token", "nullable": true}}, "additionalProperties": false}, "TokenResponseDtoAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/TokenResponseDto"}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "姓名", "nullable": true}, "userState": {"type": "boolean", "description": "是否启用"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int64"}, "description": "角色编号", "nullable": true}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "createTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserDtoAPIPageing": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDto"}, "totalCount": {"type": "integer", "format": "int32"}, "pageCount": {"type": "integer", "format": "int32"}, "pageData": {"type": "array", "items": {"$ref": "#/components/schemas/UserDto"}, "nullable": true}}, "additionalProperties": false}, "UserDtoAPIPageingAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserDtoAPIPageing"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "添加JWT授权Token：Bearer Token值", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "<PERSON><PERSON>", "description": "登录授权控制器-读"}, {"name": "Permission", "description": "权限控制器-读"}, {"name": "Role", "description": "角色控制器-读"}, {"name": "User", "description": "用户控制器-读"}]}