﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Traceability.API.Write.Application.Command.User;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Write.Application.Handler.User
{
    public class UpdateUserStateCommandHandler : IRequestHandler<UpdateUserStateCommand, APIResult<object>>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserModel> _userRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> _userRoleRepository;
        private readonly IIdentifyService identifyService;

        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;
        private readonly ILogger<UpdateUserCommandHandler> _logger;

        public UpdateUserStateCommandHandler(IMapper mapper, ILogger<UpdateUserCommandHandler> logger, IBaseRepository<UserModel> userRepository, IBaseRepository<UserRoleModel> userRoleRepository,IIdentifyService identifyService)
        {
            _mapper = mapper;
            _logger = logger;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
            this.identifyService = identifyService;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public async Task<APIResult<object>> Handle(UpdateUserStateCommand request, CancellationToken cancellationToken)
        {
            APIResult<object> result = new APIResult<object>();
            result.Code = ResultCode.Success;
            result.Message = "用户状态修改成功";
            /* 为什么使用执行策略？
             * 原因是：
                MySQL 配置了重试策略，但您直接在代码中开启了事务
                当使用重试执行策略时，不能直接创建事务，而必须通过策略来包装事务操作
                如果直接创建事务，在重试时可能会导致数据不一致或事务状态混乱
             * 使用异步是为了不阻塞线程
             */
            //创建执行策略
            var strategy = _userRepository.Context.Database.CreateExecutionStrategy();

            //在执行策略中使用事务
            await strategy.ExecuteAsync(async () =>
            {
                using (var tr = await _userRepository.Context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        //修改用户
                        var user = _userRepository.GetAll().FirstOrDefault(x => x.Id == request.Id);
                        
                        user.ModifierId = Convert.ToInt64(identifyService.UserId);
                        user.ModifyTime = DateTime.Now;
                        user.UserState = !user.UserState;
                        await _userRepository.UpdateAsync(user);

                        //修改角色权限
                        var userRole = _userRoleRepository.GetAll().Where(x => x.RoleId == user.Id).ToList();
                        foreach (var item in userRole)
                        {
                            item.IsDeleted = !item.IsDeleted;
                        }
                        _logger.LogInformation($"用户状态修改成功！");
                        await tr.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        result.Code = ResultCode.Fail;
                        result.Message = "用户状态修改失败";
                        _logger.LogError($"用户状态修改时时出现异常:{ex.Message}");
                        //回滚
                        await tr.RollbackAsync();
                    }
                }
            });
            return result;
        }
    }
}
