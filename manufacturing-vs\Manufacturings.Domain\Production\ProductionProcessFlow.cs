using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 生产工单与工艺流程中间表
    /// </summary>
    public class ProductionProcessFlow : BaseEntity
    {
        /// <summary>
        /// 生产工单ID
        /// </summary>
        public long? order_id { get; set; }

        /// <summary>
        /// 工艺ID
        /// </summary>
        public long? process_flow_id { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public string? responsible_person { get; set; }

        /// <summary>
        /// 负责部门
        /// </summary>
        public string? department { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? status { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime? planned_start_time { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime? planned_end_time { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? actual_start_time { get; set; }

        /// <summary>
        /// 实际结束时间
        /// </summary>
        public DateTime? actual_end_time { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? notes { get; set; }

        /// <summary>
        /// 计划产量
        /// </summary>
        public decimal? PlannedQuantity { get; set; }

        /// <summary>
        /// 完工数量
        /// </summary>
        public decimal? CompletedQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public decimal? QualifiedQuantity { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        public decimal? QualificationRate { get; set; }
    }
}
