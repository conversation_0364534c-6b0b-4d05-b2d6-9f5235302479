2025-06-20 17:24:12.2074 ERROR 添加角色时出现异常:The configured execution strategy 'MySqlRetryingExecutionStrategy' does not support user-initiated transactions. Use the execution strategy returned by 'DbContext.Database.CreateExecutionStrategy()' to execute all the operations in the transaction as a retriable unit.
2025-06-20 19:43:54.1102 ERROR 添加角色时出现异常:The configured execution strategy 'MySqlRetryingExecutionStrategy' does not support user-initiated transactions. Use the execution strategy returned by 'DbContext.Database.CreateExecutionStrategy()' to execute all the operations in the transaction as a retriable unit.
2025-06-20 19:43:57.4355 ERROR 添加角色时出现异常:The configured execution strategy 'MySqlRetryingExecutionStrategy' does not support user-initiated transactions. Use the execution strategy returned by 'DbContext.Database.CreateExecutionStrategy()' to execute all the operations in the transaction as a retriable unit.
2025-06-20 19:44:13.9912 ERROR 添加角色时出现异常:The configured execution strategy 'MySqlRetryingExecutionStrategy' does not support user-initiated transactions. Use the execution strategy returned by 'DbContext.Database.CreateExecutionStrategy()' to execute all the operations in the transaction as a retriable unit.
2025-06-20 19:57:41.5364 INFO 角色及权限添加成功！
2025-06-20 20:04:51.8061 INFO 角色及权限添加成功！
2025-06-20 21:50:11.7630 INFO 角色及权限添加成功！
2025-06-20 21:52:43.3874 INFO 角色及权限添加成功！
2025-06-20 21:53:39.7282 INFO 角色及权限添加成功！
