﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Read.Application.Command.Permission;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Controllers.RBAC
{
    /// <summary>
    /// 权限控制器-读
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class PermissionController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public PermissionController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 获取权限列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<APIPageing<PermissionModel>>> GetPermission([FromQuery] GetPermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 获取权限
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<PermissionModel>>> GetAllPermission()
        {
            GetAllPermissionCommand command = new GetAllPermissionCommand();
            return mediator.Send(command);
        }
        /// <summary>
        /// 动态菜单权限
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public Task<APIResult<List<CascadeItem>>> GetCascadeItem([FromQuery] GetMenuPermissionCommand request)
        {
            return mediator.Send(request);
        }
    }
}
