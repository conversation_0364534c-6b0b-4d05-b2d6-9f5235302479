using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.BOM
{
    /// <summary>
    /// 工艺流程-工序关联表
    /// </summary>
    public class ProcessOperation : BaseEntity
    {
        /// <summary>
        /// 关联工艺流程ID
        /// </summary>
        public long? ProcessId { get; set; }

        /// <summary>
        /// 关联工序ID
        /// </summary>
        public long? OperationId { get; set; }

        /// <summary>
        /// 工序执行顺序
        /// </summary>
        public int? Sequence { get; set; }
    }
}
