# Three模块 读操作补充总结

## 概述
本文档总结了Three模块读操作方面的补充内容，包括新增的DTO、Command、CommandHandler和API端点。

## 新增的读操作

### 1. 工单管理 (WorkOrder)
- **DTO**: `WorkOrderDTO`, `WorkOrderDetailDTO`, `WorkOrderQueryDTO`
- **Command**: `WorkOrderShowCommand`, `WorkOrderDetailCommand`
- **CommandHandler**: `WorkOrderShowCommandHandler`, `WorkOrderDetailCommandHandler`
- **API端点**:
  - `GetWorkOrderList` - 查询工单列表
  - `GetWorkOrderDetail` - 查询工单详情
  - `GetTestWorkOrderData` - 获取工单测试数据

### 2. 物料分类管理 (MaterialCategory)
- **DTO**: `MaterialCategoryDTO`, `MaterialCategoryQueryDTO`
- **Command**: `MaterialCategoryShowCommand`
- **CommandHandler**: `MaterialCategoryShowCommandHandler`
- **API端点**:
  - `GetMaterialCategoryList` - 查询物料分类列表
  - `GetTestMaterialCategoryData` - 获取物料分类测试数据

### 3. 物料管理 (Material)
- **DTO**: `MaterialDTO`, `MaterialQueryDTO`
- **Command**: `MaterialShowCommand`
- **CommandHandler**: `MaterialShowCommandHandler`
- **API端点**:
  - `GetMaterialList` - 查询物料列表
  - `GetTestMaterialData` - 获取物料测试数据

### 4. 库存管理 (Inventory)
- **DTO**: `InventoryDTO`, `InventoryQueryDTO`
- **Command**: `InventoryShowCommand`
- **CommandHandler**: `InventoryShowCommandHandler`
- **API端点**:
  - `GetInventoryList` - 查询库存列表
  - `GetTestInventoryData` - 获取库存测试数据

## 查询功能特性

### 1. 工单查询功能
- 工单编号模糊查询
- 工单名称模糊查询
- 项目ID精确查询
- 工单类型精确查询
- 状态精确查询
- 负责人模糊查询
- 部门模糊查询
- 开始日期范围查询
- 结束日期范围查询
- 分页查询

### 2. 物料分类查询功能
- 分类编码模糊查询
- 分类名称模糊查询
- 父级分类ID精确查询
- 启用状态精确查询
- 分页查询
- 支持层级结构显示

### 3. 物料查询功能
- 物料编码模糊查询
- 物料名称模糊查询
- 规格模糊查询
- 分类ID精确查询
- 物料类型精确查询
- 供应商编码模糊查询
- 启用状态精确查询
- 分页查询
- 关联分类和供应商信息

### 4. 库存查询功能
- 物料编码模糊查询
- 物料名称模糊查询
- 规格模糊查询
- 仓库ID精确查询
- 库位ID精确查询
- 数量范围查询
- 批次号模糊查询
- 过期日期范围查询
- 低库存查询（可用数量 < 安全库存）
- 分页查询
- 关联仓库和库位信息

## 数据关联查询

### 1. 工单关联查询
- 工单主信息 + 工单明细
- 关联项目信息
- 关联物料信息（明细中）

### 2. 物料关联查询
- 关联物料分类信息
- 关联供应商信息

### 3. 库存关联查询
- 关联物料信息
- 关联仓库信息
- 关联库位信息

### 4. 物料分类关联查询
- 支持父子层级关系
- 自动填充父级分类名称

## 测试数据

为每个新增的实体提供了完整的测试数据，包括：
- 工单测试数据（2条记录）
- 物料分类测试数据（3条记录）
- 物料测试数据（2条记录）
- 库存测试数据（2条记录）

## 技术特点

### 1. 统一的错误处理
- 使用 `ApiEnum.Success` 和 `ApiEnum.Fail`
- 详细的错误信息返回
- 异常捕获和处理

### 2. 分页查询
- 支持页码和页大小设置
- 返回总数和分页信息
- 默认分页参数

### 3. 软删除支持
- 所有查询都过滤已删除记录
- 使用 `!x.IsDeleted` 条件

### 4. 排序规则
- 工单：按创建时间倒序
- 物料分类：按排序顺序，再按创建时间
- 物料：按创建时间倒序
- 库存：按创建时间倒序

## 已实现的读操作总结

### 核心业务实体（8个）
1. ✅ ProcessClassification（工序分类）
2. ✅ Process（工序）
3. ✅ OutboundOrder（出库单）
4. ✅ WorkOrder（工单）
5. ✅ MaterialCategory（物料分类）
6. ✅ Material（物料）
7. ✅ Inventory（库存）

### 待实现的读操作（13个）
1. [ ] Supplier（供应商）
2. [ ] Customer（客户）
3. [ ] Project（项目）
4. [ ] Warehouse（仓库）
5. [ ] StorageLocation（库位）
6. [ ] Department（部门）
7. [ ] Employee（员工）
8. [ ] OutsourcingProcessing（外协加工单）
9. [ ] OutsourcingWorkOrder（外协工单）
10. [ ] ApprovalProcess（审批流程）
11. [ ] ApprovalNode（审批节点）
12. [ ] OperationRecord（操作记录）
13. [ ] Attachment（附件）

## 建议的下一步工作

1. **继续实现剩余实体的读操作**：特别是基础数据管理相关的实体
2. **添加更多查询功能**：如统计查询、报表查询等
3. **优化查询性能**：添加索引、优化查询语句
4. **添加缓存机制**：对常用数据进行缓存
5. **完善数据验证**：添加查询参数的验证逻辑

## 文件清单

### 新增DTO文件
- `B.S.SmartCity/B.S.BataBase.Domain/DTO/Three/WorkOrderDTO.cs`
- `B.S.SmartCity/B.S.BataBase.Domain/DTO/Three/MaterialDTO.cs`
- `B.S.SmartCity/B.S.BataBase.Domain/DTO/Three/InventoryDTO.cs`

### 新增Command文件
- `B.S.SmartCity/B.S.BaseData.Api.Read/Command/Three/WorkOrderShowCommand.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/Command/Three/WorkOrderDetailCommand.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/Command/Three/MaterialCategoryShowCommand.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/Command/Three/MaterialShowCommand.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/Command/Three/InventoryShowCommand.cs`

### 新增CommandHandler文件
- `B.S.SmartCity/B.S.BaseData.Api.Read/CommandHandler/Three/WorkOrderShowCommandHandler.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/CommandHandler/Three/WorkOrderDetailCommandHandler.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/CommandHandler/Three/MaterialCategoryShowCommandHandler.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/CommandHandler/Three/MaterialShowCommandHandler.cs`
- `B.S.SmartCity/B.S.BaseData.Api.Read/CommandHandler/Three/InventoryShowCommandHandler.cs`

### 更新的控制器文件
- `B.S.SmartCity/B.S.BaseData.Api.Read/Controllers/ThreeController.cs`

