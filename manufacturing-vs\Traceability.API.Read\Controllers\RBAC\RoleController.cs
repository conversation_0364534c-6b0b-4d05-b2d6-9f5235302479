﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Read.Application.Command.Role;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Controllers.RBAC
{
    /// <summary>
    /// 角色控制器-读
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class RoleController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public RoleController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// 获取角色及角色权限列表
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<APIResult<APIPageing<RoleDto>>> GetRole([FromQuery] RoleQueryCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 获取所有角色
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<APIResult<List<RoleModel>>> GetAllRole()
        {
            RoleAllQueryCommand command = new RoleAllQueryCommand();
            return await mediator.Send(command);
        }
    }
}
