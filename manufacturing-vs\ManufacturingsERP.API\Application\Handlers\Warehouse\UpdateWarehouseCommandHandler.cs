using MediatR;
using ManufacturingsERP.API.Application.DTOs;
using ManufacturingsERP.API.Application.Interfaces;
using Manufacturings.Infrastructrue.Error;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Infrastructrue;

namespace ManufacturingsERP.API.Application.Handlers.Warehouse
{
    /// <summary>
    /// 更新仓库命令处理器
    /// </summary>
    public class UpdateWarehouseCommandHandler : IRequestHandler<UpdateWarehouseCommand, APIResult<bool>>
    {
        private readonly IWarehouseRepository _warehouseRepository;
        private readonly IBaseRepository<WarehouseCategory> _categoryRepository;
        private readonly IBaseRepository<StorageType> _storageTypeRepository;
        private readonly IBaseRepository<WarehouseStructure> _structureRepository;
        private readonly IBaseRepository<Person> _personRepository;

        public UpdateWarehouseCommandHandler(
            IWarehouseRepository warehouseRepository,
            IBaseRepository<WarehouseCategory> categoryRepository,
            IBaseRepository<StorageType> storageTypeRepository,
            IBaseRepository<WarehouseStructure> structureRepository,
            IBaseRepository<Person> personRepository)
        {
            _warehouseRepository = warehouseRepository;
            _categoryRepository = categoryRepository;
            _storageTypeRepository = storageTypeRepository;
            _structureRepository = structureRepository;
            _personRepository = personRepository;
        }

        public async Task<APIResult<bool>> Handle(UpdateWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取要更新的仓库
                var warehouse = await _warehouseRepository.GetModel(request.Id);
                if (warehouse == null)
                {
                    return new APIResult<bool>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库不存在",
                        Data = false
                    };
                }

                // 验证仓库分类是否存在
                var category = await _categoryRepository.GetModel(request.CategoryId);
                if (category == null)
                {
                    return new APIResult<bool>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库分类不存在",
                        Data = false
                    };
                }

                // 验证上级仓库是否存在
                if (request.ParentId.HasValue)
                {
                    var parentWarehouse = await _warehouseRepository.GetModel(request.ParentId.Value);
                    if (parentWarehouse == null)
                    {
                        return new APIResult<bool>
                        {
                            Code = ResultCode.Fail,
                            Message = "上级仓库不存在",
                            Data = false
                        };
                    }

                    // 检查是否形成循环引用
                    if (await HasCircularReference(request.Id, request.ParentId.Value))
                    {
                        return new APIResult<bool>
                        {
                            Code = ResultCode.Fail,
                            Message = "不能选择自己或子仓库作为上级仓库",
                            Data = false
                        };
                    }
                }

                // 验证存储类型是否存在
                if (request.StorageTypeId.HasValue)
                {
                    var storageType = await _storageTypeRepository.GetModel(request.StorageTypeId.Value);
                    if (storageType == null)
                    {
                        return new APIResult<bool>
                        {
                            Code = ResultCode.Fail,
                            Message = "存储类型不存在",
                            Data = false
                        };
                    }
                }

                // 验证仓库结构是否存在
                if (request.StructureId.HasValue)
                {
                    var structure = await _structureRepository.GetModel(request.StructureId.Value);
                    if (structure == null)
                    {
                        return new APIResult<bool>
                        {
                            Code = ResultCode.Fail,
                            Message = "仓库结构不存在",
                            Data = false
                        };
                    }
                }

                // 验证负责人是否存在
                if (request.PersonInChargeId.HasValue)
                {
                    var person = await _personRepository.GetModel(request.PersonInChargeId.Value);
                    if (person == null)
                    {
                        return new APIResult<bool>
                        {
                            Code = ResultCode.Fail,
                            Message = "负责人不存在",
                            Data = false
                        };
                    }
                }

                // 检查仓库名称是否已存在（排除当前仓库）
                if (await _warehouseRepository.IsWarehouseNameExistsAsync(request.WarehouseName, request.Id))
                {
                    return new APIResult<bool>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库名称已存在",
                        Data = false
                    };
                }

                // 更新仓库信息
                warehouse.WarehouseName = request.WarehouseName;
                warehouse.ParentId = request.ParentId;
                warehouse.CategoryId = request.CategoryId;
                warehouse.StorageTypeId = request.StorageTypeId;
                warehouse.StructureId = request.StructureId;
                warehouse.PersonInChargeId = request.PersonInChargeId;
                warehouse.Address = request.Address;
                warehouse.Remarks = request.Remarks;
                warehouse.IsEnabled = request.IsEnabled;
                warehouse.IsSystemNumber = request.IsSystemNumber;
                warehouse.ModifyTime = DateTime.Now;
                warehouse.ModifierName = "系统"; // 这里应该从当前用户上下文获取

                // 保存到数据库
                await _warehouseRepository.UpdateAsync(warehouse);

                return new APIResult<bool>
                {
                    Code = ResultCode.Success,
                    Message = "仓库更新成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                return new APIResult<bool>
                {
                    Code = ResultCode.Fail,
                    Message = $"更新仓库失败: {ex.Message}",
                    Data = false
                };
            }
        }

        /// <summary>
        /// 检查是否形成循环引用
        /// </summary>
        private async Task<bool> HasCircularReference(long warehouseId, int parentId)
        {
            if (warehouseId == parentId)
                return true;

            var currentParentId = (int?)parentId;
            var visitedIds = new HashSet<long> { warehouseId };

            while (currentParentId.HasValue)
            {
                if (visitedIds.Contains(currentParentId.Value))
                    return true;

                visitedIds.Add(currentParentId.Value);
                var parent = await _warehouseRepository.GetModel(currentParentId.Value);
                if (parent == null)
                    break;

                currentParentId = parent.ParentId;
            }

            return false;
        }
    }
} 