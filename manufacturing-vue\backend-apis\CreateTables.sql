-- 创建仓库分类表 (MySQL版本)
CREATE TABLE IF NOT EXISTS WarehouseCategory (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    CategoryName VARCHAR(100) NOT NULL UNIQUE,
    CategoryCode VARCHAR(20) NOT NULL DEFAULT '',
    Description VARCHAR(500) NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    CreateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CreateUser VARCHAR(50) NOT NULL DEFAULT '',
    ModificationTime DATETIME NULL,
    ModifierName VARCHAR(50) NULL DEFAULT ''
);

-- 创建存储类型表
CREATE TABLE StorageType (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    TypeName NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(500) NULL,
    IsEnabled BIT NOT NULL DEFAULT 1,
    CreateTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreateUser NVARCHAR(50) NOT NULL DEFAULT '',
    ModificationTime DATETIME2 NULL,
    ModifierName NVARCHAR(50) NULL DEFAULT ''
);

-- 创建仓库结构表
CREATE TABLE WarehouseStructure (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    StructureName NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(500) NULL,
    IsEnabled BIT NOT NULL DEFAULT 1,
    CreateTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreateUser NVARCHAR(50) NOT NULL DEFAULT '',
    ModificationTime DATETIME2 NULL,
    ModifierName NVARCHAR(50) NULL DEFAULT ''
);

-- 创建人员表
CREATE TABLE Person (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    PersonName NVARCHAR(100) NOT NULL,
    Department NVARCHAR(100) NULL DEFAULT '',
    Position NVARCHAR(100) NULL DEFAULT '',
    Phone NVARCHAR(20) NULL DEFAULT '',
    Email NVARCHAR(100) NULL DEFAULT '',
    IsEnabled BIT NOT NULL DEFAULT 1,
    CreateTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreateUser NVARCHAR(50) NOT NULL DEFAULT '',
    ModificationTime DATETIME2 NULL,
    ModifierName NVARCHAR(50) NULL DEFAULT ''
);

-- 插入种子数据
INSERT INTO WarehouseCategory (CategoryName, Description, CreateUser) VALUES
('原材料仓库', '存放生产原材料', 'system'),
('成品仓库', '存放成品', 'system'),
('半成品仓库', '存放半成品', 'system'),
('工具仓库', '存放工具设备', 'system'),
('备件仓库', '存放备用零件', 'system');

INSERT INTO StorageType (TypeName, Description, CreateUser) VALUES
('常温存储', '常温环境存储', 'system'),
('冷藏存储', '2-8℃冷藏存储', 'system'),
('冷冻存储', '-18℃以下冷冻存储', 'system'),
('恒温存储', '恒定温度存储', 'system'),
('防潮存储', '防潮环境存储', 'system');

INSERT INTO WarehouseStructure (StructureName, Description, CreateUser) VALUES
('平面仓库', '平面布局仓库', 'system'),
('立体仓库', '立体货架仓库', 'system'),
('货架仓库', '货架式仓库', 'system'),
('自动化仓库', '自动化立体仓库', 'system'),
('露天仓库', '露天存储区域', 'system');

INSERT INTO Person (PersonName, Department, Position, Phone, Email, CreateUser) VALUES
('张三', '仓储部', '仓库主管', '13800138001', '<EMAIL>', 'system'),
('李四', '仓储部', '仓库管理员', '13800138002', '<EMAIL>', 'system'),
('王五', '仓储部', '仓库管理员', '13800138003', '<EMAIL>', 'system'),
('赵六', '仓储部', '仓库操作员', '13800138004', '<EMAIL>', 'system'),
('钱七', '仓储部', '仓库操作员', '13800138005', '<EMAIL>', 'system');

-- 创建索引
CREATE INDEX IX_WarehouseCategory_CategoryName ON WarehouseCategory(CategoryName);
CREATE INDEX IX_StorageType_TypeName ON StorageType(TypeName);
CREATE INDEX IX_WarehouseStructure_StructureName ON WarehouseStructure(StructureName);
CREATE INDEX IX_Person_PersonName ON Person(PersonName);
CREATE INDEX IX_Person_Department ON Person(Department);
