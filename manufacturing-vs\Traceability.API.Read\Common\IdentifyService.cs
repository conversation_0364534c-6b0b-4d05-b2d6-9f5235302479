﻿using System.Security.Claims;

namespace Traceability.API.Read.Common
{
    public class IdentifyService: IIdentifyService
    {
        /// <summary>
        /// HttpContextAccessor
        /// </summary>
        private readonly IHttpContextAccessor _contextAccessor;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="contextAccessor">HttpContextAccessor</param>
        public IdentifyService(IHttpContextAccessor contextAccessor)
        {
            _contextAccessor = contextAccessor;
            this.UserId = _contextAccessor.HttpContext.User.FindFirstValue("UserId");
            this.UserName = _contextAccessor.HttpContext.User.FindFirstValue("Nickname");
        }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string UserName { get; set; }

    }
    /// <summary>
    /// 定义身份服务的相关方法接口
    /// </summary>
    public interface IIdentifyService
    { 
       
        public string UserId { get; set; }
        
        public string UserName { get; set; }
    }
}
