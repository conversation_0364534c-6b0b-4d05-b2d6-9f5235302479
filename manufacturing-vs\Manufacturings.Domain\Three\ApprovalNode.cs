using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 审批节点实体
    /// </summary>
    [Table("ApprovalNode")]
    public class ApprovalNode : BaseEntity
    {
        /// <summary>
        /// 审批流程ID
        /// </summary>
        public long ApprovalProcessId { get; set; }

        /// <summary>
        /// 节点名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string NodeName { get; set; } = string.Empty;

        /// <summary>
        /// 节点类型 (开始/审批/结束)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string NodeType { get; set; } = string.Empty;

        /// <summary>
        /// 审批人
        /// </summary>
        [StringLength(500)]
        public string Approvers { get; set; } = string.Empty;

        /// <summary>
        /// 审批方式 (依次审批/会签审批/或签审批)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ApprovalMethod { get; set; } = string.Empty;

        /// <summary>
        /// 节点顺序
        /// </summary>
        public int NodeOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; } = string.Empty;

    }
}

