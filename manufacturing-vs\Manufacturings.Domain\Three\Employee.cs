using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 员工实体
    /// </summary>
    [Table("Employee")]
    public class Employee : BaseEntity
    {
        /// <summary>
        /// 员工编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EmployeeCode { get; set; }

        /// <summary>
        /// 员工姓名
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EmployeeName { get; set; }

        /// <summary>
        /// 英文姓名
        /// </summary>
        [StringLength(100)]
        public string EnglishName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [StringLength(10)]
        public string Gender { get; set; }

        /// <summary>
        /// 出生日期
        /// </summary>
        public DateTime? BirthDate { get; set; }

        /// <summary>
        /// 身份证号
        /// </summary>
        [StringLength(18)]
        public string IdCard { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string Phone { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(100)]
        public string Email { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [StringLength(200)]
        public string Address { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public long DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [StringLength(100)]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [StringLength(50)]
        public string Position { get; set; }

        /// <summary>
        /// 职级
        /// </summary>
        [StringLength(50)]
        public string JobLevel { get; set; }

        /// <summary>
        /// 员工状态
        /// </summary>
        [StringLength(20)]
        public string EmployeeStatus { get; set; } = "在职";

        /// <summary>
        /// 入职日期
        /// </summary>
        public DateTime? HireDate { get; set; }

        /// <summary>
        /// 转正日期
        /// </summary>
        public DateTime? RegularDate { get; set; }

        /// <summary>
        /// 离职日期
        /// </summary>
        public DateTime? LeaveDate { get; set; }

        /// <summary>
        /// 学历
        /// </summary>
        [StringLength(50)]
        public string Education { get; set; }

        /// <summary>
        /// 专业
        /// </summary>
        [StringLength(100)]
        public string Major { get; set; }

        /// <summary>
        /// 毕业院校
        /// </summary>
        [StringLength(100)]
        public string University { get; set; }

        /// <summary>
        /// 紧急联系人
        /// </summary>
        [StringLength(50)]
        public string EmergencyContact { get; set; }

        /// <summary>
        /// 紧急联系电话
        /// </summary>
        [StringLength(20)]
        public string EmergencyPhone { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; }
    }
}
