using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.item
{
    /// <summary>
    /// 分层定价表
    /// </summary>
    public class TieredPricing : BaseEntity
    {
        /// <summary>
        /// 物品表ID
        /// </summary>
        public long? ItemId { get; set; }

        /// <summary>
        /// 客户等级
        /// </summary>
        public string? Customerlevel { get; set; }

        /// <summary>
        /// 折扣%
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// 对应销售价
        /// </summary>
        public decimal? CorrespondingPrice { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UPDATED_TIME { get; set; }
    }
}
