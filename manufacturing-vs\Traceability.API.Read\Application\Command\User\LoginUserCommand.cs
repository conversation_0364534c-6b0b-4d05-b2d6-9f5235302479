﻿using MediatR;
using Traceability.API.Read.Dto.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Application.Command.User
{
    /// <summary>
    /// 
    /// </summary>
    public class LoginUserCommand:IRequest<APIResult<LoginUserDto>>
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 密码
        /// </summary>
        public string PassWord { get; set; }
    }
}
