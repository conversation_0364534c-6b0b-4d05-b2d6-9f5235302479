{"Version": 1, "Hash": "IVmKqahbKNW7PerHNnJdbdktFk7HpbMTIVgDCswXkYA=", "Source": "Traceability.API.Write", "BasePath": "_content/Traceability.API.Write", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Traceability.API.Write\\wwwroot", "Source": "Traceability.API.Write", "ContentRoot": "D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.API.Write\\wwwroot\\", "BasePath": "_content/Traceability.API.Write", "Pattern": "**"}], "Assets": []}