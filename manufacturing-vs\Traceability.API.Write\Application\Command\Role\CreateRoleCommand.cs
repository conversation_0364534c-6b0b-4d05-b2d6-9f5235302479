﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.Role
{
    /// <summary>
    /// 添加角色
    /// </summary>
    public class CreateRoleCommand:IRequest<APIResult<object>>
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool RoleState { get; set; }
        /// <summary>
        /// 权限Id
        /// </summary>
        public List<int> PermissionId { get; set; }
    }
}
