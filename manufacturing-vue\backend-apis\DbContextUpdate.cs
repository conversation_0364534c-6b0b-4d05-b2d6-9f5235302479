// 在您的 ApplicationDbContext 类中添加以下 DbSet 属性

using Microsoft.EntityFrameworkCore;
using ManufacturingsERP.API.Models;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    // 现有的 DbSet...
    public DbSet<Warehouse> Warehouses { get; set; }

    // 新增的 DbSet
    public DbSet<WarehouseCategory> WarehouseCategories { get; set; }
    public DbSet<StorageType> StorageTypes { get; set; }
    public DbSet<WarehouseStructure> WarehouseStructures { get; set; }
    public DbSet<Person> Persons { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置实体关系和约束
        modelBuilder.Entity<WarehouseCategory>(entity =>
        {
            entity.HasIndex(e => e.CategoryName).IsUnique();
        });

        modelBuilder.Entity<StorageType>(entity =>
        {
            entity.HasIndex(e => e.TypeName).IsUnique();
        });

        modelBuilder.Entity<WarehouseStructure>(entity =>
        {
            entity.HasIndex(e => e.StructureName).IsUnique();
        });

        modelBuilder.Entity<Person>(entity =>
        {
            entity.HasIndex(e => e.PersonName);
        });

        // 种子数据
        modelBuilder.Entity<WarehouseCategory>().HasData(
            new WarehouseCategory { Id = 1, CategoryName = "原材料仓库", Description = "存放生产原材料", CreateUser = "system" },
            new WarehouseCategory { Id = 2, CategoryName = "成品仓库", Description = "存放成品", CreateUser = "system" },
            new WarehouseCategory { Id = 3, CategoryName = "半成品仓库", Description = "存放半成品", CreateUser = "system" },
            new WarehouseCategory { Id = 4, CategoryName = "工具仓库", Description = "存放工具设备", CreateUser = "system" },
            new WarehouseCategory { Id = 5, CategoryName = "备件仓库", Description = "存放备用零件", CreateUser = "system" }
        );

        modelBuilder.Entity<StorageType>().HasData(
            new StorageType { Id = 1, TypeName = "常温存储", Description = "常温环境存储", CreateUser = "system" },
            new StorageType { Id = 2, TypeName = "冷藏存储", Description = "2-8℃冷藏存储", CreateUser = "system" },
            new StorageType { Id = 3, TypeName = "冷冻存储", Description = "-18℃以下冷冻存储", CreateUser = "system" },
            new StorageType { Id = 4, TypeName = "恒温存储", Description = "恒定温度存储", CreateUser = "system" },
            new StorageType { Id = 5, TypeName = "防潮存储", Description = "防潮环境存储", CreateUser = "system" }
        );

        modelBuilder.Entity<WarehouseStructure>().HasData(
            new WarehouseStructure { Id = 1, StructureName = "平面仓库", Description = "平面布局仓库", CreateUser = "system" },
            new WarehouseStructure { Id = 2, StructureName = "立体仓库", Description = "立体货架仓库", CreateUser = "system" },
            new WarehouseStructure { Id = 3, StructureName = "货架仓库", Description = "货架式仓库", CreateUser = "system" },
            new WarehouseStructure { Id = 4, StructureName = "自动化仓库", Description = "自动化立体仓库", CreateUser = "system" },
            new WarehouseStructure { Id = 5, StructureName = "露天仓库", Description = "露天存储区域", CreateUser = "system" }
        );

        modelBuilder.Entity<Person>().HasData(
            new Person { Id = 1, PersonName = "张三", Department = "仓储部", Position = "仓库主管", Phone = "13800138001", Email = "<EMAIL>", CreateUser = "system" },
            new Person { Id = 2, PersonName = "李四", Department = "仓储部", Position = "仓库管理员", Phone = "13800138002", Email = "<EMAIL>", CreateUser = "system" },
            new Person { Id = 3, PersonName = "王五", Department = "仓储部", Position = "仓库管理员", Phone = "13800138003", Email = "<EMAIL>", CreateUser = "system" },
            new Person { Id = 4, PersonName = "赵六", Department = "仓储部", Position = "仓库操作员", Phone = "13800138004", Email = "<EMAIL>", CreateUser = "system" },
            new Person { Id = 5, PersonName = "钱七", Department = "仓储部", Position = "仓库操作员", Phone = "13800138005", Email = "<EMAIL>", CreateUser = "system" }
        );
    }
}
