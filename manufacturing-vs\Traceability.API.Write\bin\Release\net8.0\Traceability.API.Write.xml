<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.Write</name>
    </assembly>
    <members>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.ProductId">
            <summary>
            关联产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.Species">
            <summary>
            品种
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.Source">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.BreedingDays">
            <summary>
            饲养天数
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.FeedBrand">
            <summary>
            饲料品牌
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.ResponsiblePerson">
            <summary>
            饲养负责人
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.OutboundDate">
            <summary>
            出栏日期(年月日)
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.QuarantineCertificates">
            <summary>
            动物检疫证明
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand.CompletionItems">
            <summary>
            完成项数量
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.Id">
            <summary>
            Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.BusinessInfoId">
            <summary>
            业务信息ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.ModuleType">
            <summary>
            模块类型
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.CompanyInfo">
            <summary>
            企业信息
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand.CompanyImages">
            <summary>
            企业图片
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.CreateHealthcareCommand.BreedingInfoId">
            <summary>
            关联养殖ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.CreateHealthcareCommand.BreedingDays">
            <summary>
            饲养天数
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.CreateHealthcareCommand.HealthcareMethod">
            <summary>
            保健方式
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.CreateHealthcareCommand.AdministrationMethod">
            <summary>
            保健方法
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand.BreedingInfoId">
            <summary>
            关联养殖ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand.BreedingDays">
            <summary>
            饲养天数
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand.HealthcareMethod">
            <summary>
            保健方式
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand.AdministrationMethod">
            <summary>
            保健方法
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand.BreedingInfoId">
            <summary>
            关联养殖ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand.BreedingDays">
            <summary>
            饲养天数
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand.HealthcareMethod">
            <summary>
            保健方式
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand.AdministrationMethod">
            <summary>
            保健方法
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateAnimalImgCommand.QuarantineCertificates">
            <summary>
            动物检疫证明
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand.BusinessInfoId">
            <summary>
            业务信息ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand.ModuleType">
            <summary>
            模块类型
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand.CompanyImages">
            <summary>
            企业图片
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateProcessingImgCommand.SafetyInspectionReports">
            <summary>
            食品安全检验报告
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateProductionMaterialImgCommand.MaterialType">
            <summary>
            材料类型
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateProductionMaterialImgCommand.Evidence">
            <summary>
            证明材料
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Images.CreateSlaughterCommand.QualificationDocuments">
            <summary>
            屠宰相关资质
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand.ProductId">
            <summary>
            关联产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand.ProductionTeam">
            <summary>
            班组
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand.ResponsiblePerson">
            <summary>
            负责人
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand.SafetyInspectionReports">
            <summary>
            食品安全检验报告
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand.CompletionItems">
            <summary>
            完成项数
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand.ProcessingId">
            <summary>
            加工信息ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand.MaterialType">
            <summary>
            材料类型
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand.Evidence">
            <summary>
            证明材料
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.GoodId">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.ShelfLife">
            <summary>
            保质期(天)
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.ProductionLicenseNo">
            <summary>
            生产许可证号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.ManufacturerName">
            <summary>
            生产企业
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.InspectionReports">
            <summary>
            产品质检报告
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.CreateProductCommand.ProductImage">
            <summary>
            封面图片
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.GoodId">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ShelfLife">
            <summary>
            保质期(天)
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ProductionLicenseNo">
            <summary>
            生产许可证号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ManufacturerName">
            <summary>
            生产企业
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.InspectionReports">
            <summary>
            产品质检报告
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ProductImage">
            <summary>
            封面图片
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.CreateName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ModifierName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.DeleteProductCommand.ModifyTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.Id">
            <summary>
            
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.GoodId">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.ShelfLife">
            <summary>
            保质期(天)
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.ProductionLicenseNo">
            <summary>
            生产许可证号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.ManufacturerName">
            <summary>
            生产企业
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.InspectionReports">
            <summary>
            产品质检报告
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.ProductImage">
            <summary>
            封面图片
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.CreateName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Product.UpdateProductCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Application.Command.Role.CreateRoleCommand">
            <summary>
            添加角色
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.PermissionId">
            <summary>
            权限Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.PermissionId">
            <summary>
            权限Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.ProductId">
            <summary>
            关联产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.BatchNumber">
            <summary>
            屠宰批次
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.ArrivalTime">
            <summary>
            到厂时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.ArrivalWeight">
            <summary>
            到厂均重(公斤)
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.SlaughterTime">
            <summary>
            屠宰时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.QualificationDocuments">
            <summary>
            相关资质凭证
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand.CompletionItems">
            <summary>
            完成项数量
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.UserState">
            <summary>
            
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler">
            <summary>
            更新养殖信息命令处理器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.breedingRepository">
            <summary>
            养殖信息仓储接口
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.mapper">
            <summary>
            AutoMapper映射工具
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.identifyService">
            <summary>
            身份认证服务
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.BreedingInfoModel},AutoMapper.IMapper,Traceability.API.Write.Common.IIdentifyService,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler})">
            <summary>
            构造函数，注入依赖项
            </summary>
            <param name="breedingRepository">养殖信息仓储</param>
            <param name="mapper">AutoMapper映射工具</param>
            <param name="identifyService">身份认证服务</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Breeding.UpdateCultivationCommandHandler.Handle(Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand,System.Threading.CancellationToken)">
            <summary>
            处理更新养殖信息的请求
            </summary>
            <param name="request">更新养殖信息命令</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="T:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler">
            <summary>
            更新企业信息命令处理器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.enterpriseRepository">
            <summary>
            企业信息仓储接口
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.mapper">
            <summary>
            AutoMapper映射工具
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.identifyService">
            <summary>
            身份认证服务
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.breedingRepository">
            <summary>
            养殖信息仓储接口
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.slaughterRepository">
            <summary>
            养殖信息仓储接口
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.EnterpriseInfoModel},AutoMapper.IMapper,Traceability.API.Write.Common.IIdentifyService,Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.BreedingInfoModel},Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.SlaughterInfoModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProcessingInfoModel})">
            <summary>
            构造函数，注入依赖项
            </summary>
            <param name="enterpriseRepository">企业信息仓储</param>
            <param name="mapper">AutoMapper映射工具</param>
            <param name="identifyService">身份认证服务</param>
            <param name="breedingRepository">养殖信息仓储</param>
            <param name="logger">日志服务</param>
            <param name="slaughterRepository"></param>
            <param name="processingRepository"></param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.Handle(Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand,System.Threading.CancellationToken)">
            <summary>
            处理更新企业信息的请求
            </summary>
            <param name="request">更新企业信息命令</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.AddBreeding(Traceability.Domain.Entites.EnterpriseInfoModel)">
            <summary>
            设置养殖企业信息
            </summary>
            <param name="model">企业Model</param>
            <returns>返回</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.AddSlaughter(Traceability.Domain.Entites.EnterpriseInfoModel)">
            <summary>
            设置屠宰企业信息
            </summary>
            <param name="model">企业Model</param>
            <returns>返回</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Enterprise.UpdateEnterpriseCommandHandler.AddProcessing(Traceability.Domain.Entites.EnterpriseInfoModel)">
            <summary>
            设置深加工企业信息
            </summary>
            <param name="model">企业Model</param>
            <returns>返回</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateAnimalImgCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateAnimalImgCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateEnterpriseCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateProcessingImgCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateProcessingImgCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateProductImgCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateProductImgCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateProductionMaterialImgCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateProductionMaterialImgCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateProductionMaterialImgCommandHandler.CheckAllMaterialsUploaded(System.Int64)">
            <summary>
            检查是否所有四种材料都已添加并上传了证明材料
            </summary>
            <param name="processingId">加工ID</param>
            <returns>如果所有材料都已上传返回true，否则返回false</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Images.CreateSlaughterCommandHandler.Handle(Traceability.API.Write.Application.Command.Images.CreateSlaughterCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler">
            <summary>
            更新深加工信息命令处理器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.processingRepository">
            <summary>
            深加工信息仓储接口
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.mapper">
            <summary>
            AutoMapper映射工具
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.identifyService">
            <summary>
            身份认证服务
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProcessingInfoModel},AutoMapper.IMapper,Traceability.API.Write.Common.IIdentifyService,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler})">
            <summary>
            构造函数，注入依赖项
            </summary>
            <param name="processingRepository">深加工信息仓储接口</param>
            <param name="mapper">AutoMapper映射工具</param>
            <param name="identifyService">身份认证服务</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Processing.UpdateProcessingCommandHandler.Handle(Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand,System.Threading.CancellationToken)">
            <summary>
            处理更新深加工信息的请求
            </summary>
            <param name="request">更新深加工信息命令</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Processing.UpdateProductionMaterialCommandHandler.Handle(Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand,System.Threading.CancellationToken)">
            <summary>
            处理更新供应商信息的请求
            </summary>
            <param name="request">更新供应商信息命令</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler._productRepository">
            <summary>
            产品仓储接口，用于产品信息的数据库操作
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler._mapper">
            <summary>
            对象映射器，用于DTO与实体模型之间的转换
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler._logger">
            <summary>
            日志记录器，用于记录处理过程中的日志信息
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler._identifyService">
            <summary>
            身份服务，用于获取当前用户信息
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProductInfoModel},AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler},Traceability.API.Write.Common.IIdentifyService)">
            <summary>
            创建产品命令处理器的构造函数
            </summary>
            <param name="productRepository">产品仓储接口</param>
            <param name="mapper">对象映射器</param>
            <param name="logger">日志记录器</param>
            <param name="identifyService">身份服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.CreateProductCommandHandler.Handle(Traceability.API.Write.Application.Command.Product.CreateProductCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler._productRepository">
            <summary>
            产品仓储接口，用于产品信息的数据库操作
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler._mapper">
            <summary>
            对象映射器，用于DTO与实体模型之间的转换
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler._logger">
            <summary>
            日志记录器，用于记录处理过程中的日志信息
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler._identifyService">
            <summary>
            身份服务，用于获取当前用户信息
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProductInfoModel},AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler},Traceability.API.Write.Common.IIdentifyService)">
            <summary>
            创建产品命令处理器的构造函数
            </summary>
            <param name="productRepository">产品仓储接口</param>
            <param name="mapper">对象映射器</param>
            <param name="logger">日志记录器</param>
            <param name="identifyService">身份服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.DeleteProductCommandHandler.Handle(Traceability.API.Write.Application.Command.Product.DeleteProductCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler._productRepository">
            <summary>
            产品仓储接口，用于产品信息的数据库操作
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler._mapper">
            <summary>
            对象映射器，用于DTO与实体模型之间的转换
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler._logger">
            <summary>
            日志记录器，用于记录处理过程中的日志信息
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler._identifyService">
            <summary>
            身份服务，用于获取当前用户信息
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProductInfoModel},AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler},Traceability.API.Write.Common.IIdentifyService)">
            <summary>
            创建产品命令处理器的构造函数
            </summary>
            <param name="productRepository">产品仓储接口</param>
            <param name="mapper">对象映射器</param>
            <param name="logger">日志记录器</param>
            <param name="identifyService">身份服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.RangDeleteProductCommandHandler.Handle(Traceability.API.Write.Application.Command.Product.RangDeleteProductCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler._productRepository">
            <summary>
            产品仓储接口，用于产品信息的数据库操作
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler._mapper">
            <summary>
            对象映射器，用于DTO与实体模型之间的转换
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler._logger">
            <summary>
            日志记录器，用于记录处理过程中的日志信息
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler._identifyService">
            <summary>
            身份服务，用于获取当前用户信息
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.ProductInfoModel},AutoMapper.IMapper,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler},Traceability.API.Write.Common.IIdentifyService)">
            <summary>
            创建产品命令处理器的构造函数
            </summary>
            <param name="productRepository">产品仓储接口</param>
            <param name="mapper">对象映射器</param>
            <param name="logger">日志记录器</param>
            <param name="identifyService">身份服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Product.UpdateProductCommandHandler.Handle(Traceability.API.Write.Application.Command.Product.UpdateProductCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.CreateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.DeleteRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.UpdateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._logger">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler">
            <summary>
            更新屠宰信息命令处理器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.slaughterRepository">
            <summary>
            屠宰信息仓储接口
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.mapper">
            <summary>
            AutoMapper映射工具
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.identifyService">
            <summary>
            身份认证服务
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.Entites.SlaughterInfoModel},AutoMapper.IMapper,Traceability.API.Write.Common.IIdentifyService,Microsoft.Extensions.Logging.ILogger{Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler})">
            <summary>
            构造函数，注入依赖项
            </summary>
            <param name="slaughterRepository">屠宰信息仓储</param>
            <param name="mapper">AutoMapper映射工具</param>
            <param name="identifyService">身份认证服务</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Slaughter.UpdateSlaughterCommandHandler.Handle(Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand,System.Threading.CancellationToken)">
            <summary>
            处理更新屠宰信息的请求
            </summary>
            <param name="request">更新屠宰信息命令</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>处理结果</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.CreateUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.DeleteUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.UpdateUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler.Handle(Traceability.API.Write.Application.Command.User.UpdateUserStateCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="P:Traceability.API.Write.Common.IIdentifyService.UserId">
            <summary>
            定义身份服务的相关方法
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.JwtSettings">
            <summary>
            JWT配置类
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.SecretKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.Issuer">
            <summary>
            发布者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.Audience">
            <summary>
            接收者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.AccessTokenExpirationMinutes">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.RefreshExpireTime">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.MappingProfile">
            <summary>
            AutoMapper映射配置类
            用于定义Command对象到实体模型的映射规则
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Common.MappingProfile.#ctor">
            <summary>
            构造函数，配置所有的映射关系
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.RefreshToken">
            <summary>
            续期的Token
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Token">
            <summary>
            生成一个唯一Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Expires">
            <summary>
            续期Token的过期时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Created">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Controllers.BreedingController">
            <summary>
            养殖信息控制器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.BreedingController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.UpdateBreeding(Traceability.API.Write.Application.Command.Breeding.UpdateCultivationCommand)">
            <summary>
            设置养殖信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.AddHealthcare(Traceability.API.Write.Application.Command.Healthcare.CreateHealthcareCommand)">
            <summary>
            添加保健信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.UpdateHealthcare(Traceability.API.Write.Application.Command.Healthcare.UpdateHealthcareCommand)">
            <summary>
            修改保健信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.DeleteHealthcare(Traceability.API.Write.Application.Command.Healthcare.DeleteHealthcareCommand)">
            <summary>
            删除保健信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.BreedingController.UploadAnimalImg(Traceability.API.Write.Application.Command.Images.CreateAnimalImgCommand)">
            <summary>
            编辑动检证
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.EnterpriseController">
            <summary>
            企业公共控制器-写
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.EnterpriseController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.EnterpriseController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.EnterpriseController.SetUpEnterprise(Traceability.API.Write.Application.Command.Enterprise.UpdateEnterpriseCommand)">
            <summary>
            设置企业信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.EnterpriseController.EnterpriseImg(Traceability.API.Write.Application.Command.Images.CreateEnterpriseCommand)">
            <summary>
            设置企业图片
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.HealthCheckController">
            <summary>
            API控制器-健康检查
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.HealthCheckController.Check">
            <summary>
            检测
            </summary>
            <returns>返回信息</returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.ProcessingController">
            <summary>
            深加工控制器-（写）
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.ProcessingController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProcessingController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProcessingController.UpdateProcessing(Traceability.API.Write.Application.Command.Processing.UpdateProcessingCommand)">
            <summary>
            设置深加工信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProcessingController.UploadProcessingImg(Traceability.API.Write.Application.Command.Images.CreateProcessingImgCommand)">
            <summary>
            编辑食品安全检验报告
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProcessingController.UpdateProductionMaterial(Traceability.API.Write.Application.Command.Processing.UpdateProductionMaterialCommand)">
            <summary>
            设置供应商信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProcessingController.UpdateEvidenceImg(Traceability.API.Write.Application.Command.Images.CreateProductionMaterialImgCommand)">
            <summary>
            设置材料图片信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.ProductController">
            <summary>
            溯源控制器-写
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.ProductController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.AddProduct(Traceability.API.Write.Application.Command.Product.CreateProductCommand)">
            <summary>
            添加产品生产信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.UpdateProduct(Traceability.API.Write.Application.Command.Product.UpdateProductCommand)">
            <summary>
            修改产品生产信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.DeleteProduct(Traceability.API.Write.Application.Command.Product.DeleteProductCommand)">
            <summary>
            删除产品生产信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.RangDeleteProduct(Traceability.API.Write.Application.Command.Product.RangDeleteProductCommand)">
            <summary>
            批量删除产品生产信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.ProductController.UploadProduct(Traceability.API.Write.Application.Command.Images.CreateProductImgCommand)">
            <summary>
            编辑产品质检报告
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.PermissionController">
            <summary>
            权限管理
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.PermissionController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.CreatePermission(Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand)">
            <summary>
            权限添加接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.UpdatePermission(Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand)">
            <summary>
            权限编辑接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.Handle(Traceability.API.Write.Application.Command.Permission.DelPermissionCommand)">
            <summary>
            权限删除接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.RoleController">
            <summary>
            角色权限
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.RoleController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.AddRole(Traceability.API.Write.Application.Command.Role.CreateRoleCommand)">
            <summary>
            添加角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.UpdateRole(Traceability.API.Write.Application.Command.Role.UpdateRoleCommand)">
            <summary>
            修改角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.UpdateRoleState(Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand)">
            <summary>
            修改角色状态
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.DeleteRole(Traceability.API.Write.Application.Command.Role.DeleteRoleCommand)">
            <summary>
            删除角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.UserController">
            <summary>
            用户控制器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.UserController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.AddUser(Traceability.API.Write.Application.Command.User.CreateUserCommand)">
            <summary>
            添加用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.UpdateUser(Traceability.API.Write.Application.Command.User.UpdateUserCommand)">
            <summary>
            修改用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.UpdateUserState(Traceability.API.Write.Application.Command.User.UpdateUserStateCommand)">
            <summary>
            修改用户状态
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.DeleteUser(Traceability.API.Write.Application.Command.User.DeleteUserCommand)">
            <summary>
            删除用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.SlaughterController">
            <summary>
            屠宰控制器-（写）
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.SlaughterController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.SlaughterController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.SlaughterController.UpdateSlaughter(Traceability.API.Write.Application.Command.Slaughter.UpdateSlaughterCommand)">
            <summary>
            设置屠宰信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.SlaughterController.UploadSlaughterImg(Traceability.API.Write.Application.Command.Images.CreateSlaughterCommand)">
            <summary>
            编辑屠宰相关资质
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Write.Extensions.ServiceCollectionExtensions">
            <summary>
            服务注册扩展类
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Extensions.ServiceCollectionExtensions.AddDBContextAccessor``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Boolean)">
            <summary>
            注册EFCore上下文
            </summary>
            <typeparam name="T"></typeparam>
            <param name="services"></param>
            <param name="connectionString">连接字符串</param>
            <param name="enablerSqlLog">是否启用SQL日志</param>
            <returns></returns>
        </member>
    </members>
</doc>
