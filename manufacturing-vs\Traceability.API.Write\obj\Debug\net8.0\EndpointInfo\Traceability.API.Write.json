{"openapi": "3.0.1", "info": {"title": "Traceability.API.Write", "version": "1.0"}, "paths": {"/api/Permission/CreatePermission": {"post": {"tags": ["Permission"], "summary": "权限添加接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePermissionCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Permission/UpdatePermission": {"post": {"tags": ["Permission"], "summary": "权限编辑接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePermissionCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Permission/Handle": {"post": {"tags": ["Permission"], "summary": "权限删除接口", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DelPermissionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DelPermissionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DelPermissionCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Role/AddRole": {"post": {"tags": ["Role"], "summary": "添加角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateRoleCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Role/UpdateRole": {"post": {"tags": ["Role"], "summary": "修改角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Role/UpdateRoleState": {"post": {"tags": ["Role"], "summary": "修改角色状态", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleStateCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRoleStateCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRoleStateCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/Role/DeleteRole": {"post": {"tags": ["Role"], "summary": "删除角色", "requestBody": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteRoleCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/User/AddUser": {"post": {"tags": ["User"], "summary": "添加用户信息", "requestBody": {"description": "命令", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/User/UpdateUser": {"post": {"tags": ["User"], "summary": "修改用户信息", "requestBody": {"description": "命令", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/User/UpdateUserState": {"post": {"tags": ["User"], "summary": "修改用户状态", "requestBody": {"description": "命令", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStateCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStateCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStateCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}, "/api/User/DeleteUser": {"post": {"tags": ["User"], "summary": "删除用户信息", "requestBody": {"description": "命令", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteUserCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectAPIResult"}}}}}}}}, "components": {"schemas": {"CreatePermissionCommand": {"type": "object", "properties": {"permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionUrl": {"type": "string", "description": "权限URL", "nullable": true}, "orderNo": {"type": "integer", "description": "权限序号", "format": "int32"}, "parentId": {"type": "integer", "description": "父级编号", "format": "int32", "nullable": true}}, "additionalProperties": false}, "CreateRoleCommand": {"type": "object", "properties": {"roleName": {"type": "string", "description": "角色名称", "nullable": true}, "description": {"type": "string", "description": "角色描述", "nullable": true}, "roleState": {"type": "boolean", "description": "是否启用"}, "permissionId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "权限Id", "nullable": true}}, "additionalProperties": false, "description": "添加角色"}, "CreateUserCommand": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "姓名", "nullable": true}, "userState": {"type": "boolean"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "DelPermissionCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionUrl": {"type": "string", "description": "权限URL", "nullable": true}, "orderNo": {"type": "integer", "description": "权限序号", "format": "int32"}, "parentId": {"type": "integer", "description": "父级编号", "format": "int32", "nullable": true}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "additionalProperties": false}, "DeleteRoleCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "description": {"type": "string", "description": "角色描述", "nullable": true}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "roleState": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false}, "DeleteUserCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "姓名", "nullable": true}, "userState": {"type": "boolean", "description": ""}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "角色Id", "nullable": true}}, "additionalProperties": false}, "ObjectAPIResult": {"type": "object", "properties": {"code": {"$ref": "#/components/schemas/ResultCode"}, "message": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "ResultCode": {"enum": [200, 500], "type": "integer", "format": "int32"}, "UpdatePermissionCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "permissionName": {"type": "string", "description": "权限名称", "nullable": true}, "permissionUrl": {"type": "string", "description": "权限URL", "nullable": true}, "orderNo": {"type": "integer", "description": "权限序号", "format": "int32"}, "parentId": {"type": "integer", "description": "父级编号", "format": "int32", "nullable": true}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "additionalProperties": false}, "UpdateRoleCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "description": "角色名称", "nullable": true}, "description": {"type": "string", "description": "角色描述", "nullable": true}, "roleState": {"type": "boolean", "description": "是否启用"}, "permissionId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "权限Id", "nullable": true}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "additionalProperties": false}, "UpdateRoleStateCommand": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键Id", "format": "int64"}, "roleState": {"type": "boolean", "description": "是否启用"}}, "additionalProperties": false}, "UpdateUserCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string", "description": "用户名", "nullable": true}, "password": {"type": "string", "description": "密码", "nullable": true}, "name": {"type": "string", "description": "姓名", "nullable": true}, "createId": {"type": "integer", "description": "创建人Id", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "userState": {"type": "boolean"}, "roleId": {"type": "array", "items": {"type": "integer", "format": "int32"}, "description": "角色Id", "nullable": true}}, "additionalProperties": false}, "UpdateUserStateCommand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userState": {"type": "boolean"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "添加JWT授权Token：Bearer Token值", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}], "tags": [{"name": "Permission", "description": "权限管理"}, {"name": "Role", "description": "角色权限"}, {"name": "User", "description": "用户控制器"}]}