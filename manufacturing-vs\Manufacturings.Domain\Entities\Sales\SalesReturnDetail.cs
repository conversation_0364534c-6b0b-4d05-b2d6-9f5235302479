using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售退货明细表
    /// </summary>
    public class SalesReturnDetail : BaseEntity
    {
        /// <summary>
        /// 销售退货记录ID
        /// </summary>
        [Required]
        public long SalesReturnRecordId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 退货数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReturnQuantity { get; set; }

        /// <summary>
        /// 退货单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReturnUnitPrice { get; set; }

        /// <summary>
        /// 退货金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReturnAmount { get; set; }

        /// <summary>
        /// 退货原因
        /// </summary>
        [StringLength(500)]
        public string? ReturnReason { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [StringLength(100)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 有效期
        /// </summary>
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 