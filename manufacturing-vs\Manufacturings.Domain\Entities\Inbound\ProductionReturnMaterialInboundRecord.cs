using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 生产退料入库记录表
    /// </summary>
    public class ProductionReturnMaterialInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 生产单号
        /// </summary>
        [StringLength(50)]
        public string? ProductionOrderNumber { get; set; }

        /// <summary>
        /// 生产线ID
        /// </summary>
        public int? ProductionLineId { get; set; }

        /// <summary>
        /// 退料原因
        /// </summary>
        [StringLength(500)]
        public string? ReturnReason { get; set; }

        /// <summary>
        /// 退料类型
        /// </summary>
        public MaterialReturnType? MaterialReturnType { get; set; }

        /// <summary>
        /// 退料人员ID
        /// </summary>
        public int? ReturnPersonnelId { get; set; }

        /// <summary>
        /// 退料日期
        /// </summary>
        public DateTime? ReturnDate { get; set; }

        /// <summary>
        /// 是否可重复使用
        /// </summary>
        public bool IsReusable { get; set; } = false;

        /// <summary>
        /// 退料备注
        /// </summary>
        [StringLength(500)]
        public string? ReturnRemarks { get; set; }
    }
} 