using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.item
{
    /// <summary>
    /// 物品表
    /// </summary>
    public class Items : BaseEntity
    {
        /// <summary>
        /// 物品编码
        /// </summary>
        public string? ItemCode { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string? ItemName { get; set; }

        /// <summary>
        /// 物品类型
        /// </summary>
        public string? ItemType { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        public string? unit { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string? brand { get; set; }

        /// <summary>
        /// 物品分类
        /// </summary>
        public string? Itemclassification { get; set; }

        /// <summary>
        /// 物品有效期
        /// </summary>
        public DateTime? Itemexpiration { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public long? state { get; set; }

        /// <summary>
        /// 采购单价
        /// </summary>
        public decimal? Purchaseprice { get; set; }

        /// <summary>
        /// 条码
        /// </summary>
        public string? Barcode { get; set; }

        /// <summary>
        /// 制造商型号
        /// </summary>
        public string? ManufacturerModel { get; set; }

        /// <summary>
        /// 物品属性(下拉框)
        /// </summary>
        public string? Itemattributes { get; set; }

        /// <summary>
        /// 库存上下限
        /// </summary>
        public string? Upper { get; set; }

        /// <summary>
        /// 报警天数
        /// </summary>
        public int? Number { get; set; }

        /// <summary>
        /// 销售单价
        /// </summary>
        public decimal? SalePrice { get; set; }

        /// <summary>
        /// 图片
        /// </summary>
        public string? Itemsimg { get; set; }

        /// <summary>
        /// 辅助单位
        /// </summary>
        public string? AuxiliaryUnits { get; set; }

        /// <summary>
        /// 总库存
        /// </summary>
        public decimal? Totalinventory { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Itemsremark { get; set; }
    }
}
