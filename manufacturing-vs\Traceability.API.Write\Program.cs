using IGeekFan.AspNetCore.Knife4jUI;
using Microsoft.Extensions.FileProviders;
using NLog.Web;
using Traceability.API.Write.Extensions;

var builder = WebApplication.CreateBuilder(args).Inject();


var app = builder.Build();

//// Configure the HTTP request pipeline.
//if (app.Environment.IsDevelopment())
//{
//    app.UseSwagger();
//    app.UseSwaggerUI();
//}

app.UseSwagger();
//自定义Swagger
app.UseKnife4UI();

//跨域
app.UseCors(x => x.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());


app.UseAuthorization();

app.MapControllers();


app.Run();
