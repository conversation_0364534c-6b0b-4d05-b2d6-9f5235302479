﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Write.Application.Command.Role;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Controllers.RBAC
{
    /// <summary>
    /// 角色权限
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class RoleController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public RoleController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 添加角色
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> AddRole(CreateRoleCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 修改角色
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> UpdateRole(UpdateRoleCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 修改角色状态
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> UpdateRoleState(UpdateRoleStateCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 删除角色
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<APIResult<object>> DeleteRole(DeleteRoleCommand command)
        {
            return await mediator.Send(command);
        }
    }
}
