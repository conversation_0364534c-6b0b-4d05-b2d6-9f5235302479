using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单明细表
    /// </summary>
    public class PurchaseOrderDetail : BaseEntity
    {
        /// <summary>
        /// 采购订单ID
        /// </summary>
        [Required]
        public int PurchaseOrderId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        [StringLength(100)]
        public string? Brand { get; set; }

        /// <summary>
        /// 采购数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseQuantity { get; set; }

        /// <summary>
        /// 主单位数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal MainUnitQuantity { get; set; }

        /// <summary>
        /// 税率（百分比）
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 0;

        /// <summary>
        /// 含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPriceIncludingTax { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPriceExcludingTax { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountIncludingTax { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountExcludingTax { get; set; }
    }
} 