﻿using AutoMapper;
using Traceability.API.Write.Application.Command.Permission;
using Traceability.API.Write.Application.Command.Role;
using Traceability.API.Write.Application.Command.User;
using Traceability.Domain.RBAC;

namespace Traceability.API.Write.Common
{
    /// <summary>
    /// AutoMapper映射配置类
    /// 用于定义Command对象到实体模型的映射规则
    /// </summary>
    public class MappingProfile : Profile
    {
        /// <summary>
        /// 构造函数，配置所有的映射关系
        /// </summary>
        public MappingProfile()
        {
            //权限映射配置
            CreateMap<CreatePermissionCommand, PermissionModel>();
            CreateMap<UpdatePermissionCommand, PermissionModel>();
            CreateMap<DelPermissionCommand, PermissionModel>();
            //角色映射配置
            CreateMap<CreateRoleCommand, RoleModel>();
            CreateMap<UpdateRoleCommand, RoleModel>();
            CreateMap<DeleteRoleCommand, RoleModel>();
            //用户映射配置
            CreateMap<CreateUserCommand, UserModel>();
            CreateMap<UpdateUserCommand, UserModel>();
            CreateMap<DeleteUserCommand, UserModel>();

        }
    }
}
