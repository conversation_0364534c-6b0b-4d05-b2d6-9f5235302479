using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.BOM
{
    /// <summary>
    /// 工艺流程表
    /// </summary>
    public class ProcessFlow : BaseEntity
    {
        /// <summary>
        /// 工艺流程编号
        /// </summary>
        public string? ProcessCode { get; set; }

        /// <summary>
        /// 工艺流程名称
        /// </summary>
        public string? ProcessName { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public long? ResponsibleId { get; set; }

        /// <summary>
        /// 所属部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? ProcessStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? ProcessRemark { get; set; }
    }
}
