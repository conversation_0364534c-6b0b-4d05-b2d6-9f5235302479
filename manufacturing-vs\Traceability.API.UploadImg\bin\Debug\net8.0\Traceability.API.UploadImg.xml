<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.UploadImg</name>
    </assembly>
    <members>
        <member name="M:Traceability.API.UploadImg.Controllers.FileUploadController.UploadFile(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            上传单个文件
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.UploadImg.Controllers.FileUploadController.UploadFiles(Microsoft.AspNetCore.Http.IFormFileCollection)">
            <summary>
            上传多个文件
            </summary>
            <param name="formFiles">要上传的文件集合</param>
            <returns>返回上传成功的文件名列表</returns>
        </member>
    </members>
</doc>
