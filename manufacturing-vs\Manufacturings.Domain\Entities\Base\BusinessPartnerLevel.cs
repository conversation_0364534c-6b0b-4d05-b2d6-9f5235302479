using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 业务伙伴等级表（合并客户和供应商等级）
    /// </summary>
    public class BusinessPartnerLevel : BaseEntity
    {
        /// <summary>
        /// 等级名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LevelName { get; set; } = string.Empty;

        /// <summary>
        /// 等级描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 伙伴类型（Customer/Supplier/Both）
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PartnerType { get; set; } = "Both";

        /// <summary>
        /// 等级数值（用于排序）
        /// </summary>
        public int LevelValue { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 