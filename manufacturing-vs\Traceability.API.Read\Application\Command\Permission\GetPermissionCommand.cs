﻿using MediatR;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Application.Command.Permission
{
    public class GetPermissionCommand : IRequest<APIResult<APIPageing<PermissionModel>>>
    {
        /// <summary>
        /// 权限名称
        /// </summary>
        public string? PermissionsName { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页容量
        /// </summary>
        public int PageSize { get; set; }
    }
}
