using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 权限表
    /// </summary>
    [Table("Permission")]
    public class PermissionModel : BaseEntity
    {
        /// <summary>
        /// 权限名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PermissionName { get; set; }
        
        /// <summary>
        /// 权限URL
        /// </summary>
        [StringLength(100)]
        public string PermissionUrl { get; set; }
        
        /// <summary>
        /// 权限序号
        /// </summary>
        public int OrderNo { get; set; }
        
        /// <summary>
        /// 父级编号
        /// </summary>
        public int? ParentId { get; set; }
    }
} 