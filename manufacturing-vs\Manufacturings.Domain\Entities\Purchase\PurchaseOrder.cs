using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单主表
    /// </summary>
    public class PurchaseOrder:BaseEntity
    {

        /// <summary>
        /// 订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 采购单号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PurchaseOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 关联销售订单
        /// </summary>
        [StringLength(50)]
        public string? RelatedSalesOrder { get; set; }

        /// <summary>
        /// 是否不绑定销售订单
        /// </summary>
        public bool DoNotBindSalesOrder { get; set; } = false;

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 采购部门ID
        /// </summary>
        public int? PurchasingDepartmentId { get; set; }

        /// <summary>
        /// 关联项目ID
        /// </summary>
        public int? RelatedProjectId { get; set; }

        /// <summary>
        /// 交付地址
        /// </summary>
        [StringLength(500)]
        public string? DeliveryAddress { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 关联请购单号
        /// </summary>
        [StringLength(50)]
        public string? RelatedPurchaseRequest { get; set; }

        /// <summary>
        /// 是否不绑定请购单号
        /// </summary>
        public bool DoNotBindPurchaseRequest { get; set; } = false;

        /// <summary>
        /// 采购日期
        /// </summary>
        [Required]
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 采购人员ID
        /// </summary>
        public int? PurchaserId { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(20)]
        public string? ContactMethod { get; set; }

        /// <summary>
        /// 结算方式
        /// </summary>
        [StringLength(100)]
        public string? SettlementMethod { get; set; }

        /// <summary>
        /// 订单状态（未提交、待审批、审批中、已完成、已驳回等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OrderStatus { get; set; } = "未提交";

        /// <summary>
        /// 审批流程ID
        /// </summary>
        public int? ApprovalProcessId { get; set; }

        /// <summary>
        /// 当前审批节点
        /// </summary>
        [StringLength(100)]
        public string? CurrentApprovalNode { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Required]
        public int CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改人ID
        /// </summary>
        public int? LastModifiedBy { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }
    }
} 