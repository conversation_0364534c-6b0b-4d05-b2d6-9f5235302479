using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 生产工单物品表
    /// </summary>
    public class ProductionOrderItem : BaseEntity
    {
        /// <summary>
        /// 物品ID
        /// </summary>
        public long? ItemId { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime? planned_start_time { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime? planned_end_time { get; set; }

        /// <summary>
        /// 计划产量
        /// </summary>
        public decimal? planned_quantity { get; set; }
    }
}
