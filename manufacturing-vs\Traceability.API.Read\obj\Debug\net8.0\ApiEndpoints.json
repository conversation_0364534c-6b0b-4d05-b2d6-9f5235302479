[{"ContainingType": "Traceability.API.Read.Controllers.RBAC.LoginController", "Method": "Export", "RelativePath": "api/Login/Export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.LoginController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Login/Login", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "UserName", "Type": "System.String", "IsRequired": false}, {"Name": "PassWord", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[Traceability.API.Read.Dto.RBAC.LoginUserDto, Traceability.API.Read, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.LoginController", "Method": "Refresh", "RelativePath": "api/Login/Refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Read.Application.Command.User.RefreshTokenQueryCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[Traceability.API.Read.Dto.RBAC.TokenResponseDto, Traceability.API.Read, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.PermissionController", "Method": "GetAllPermission", "RelativePath": "api/Permission/GetAllPermission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Collections.Generic.List`1[[Traceability.Domain.RBAC.PermissionModel, Traceability.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.PermissionController", "Method": "GetCascadeItem", "RelativePath": "api/Permission/GetCascadeItem", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RoleId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Collections.Generic.List`1[[Traceability.API.Read.Dto.RBAC.CascadeItem, Traceability.API.Read, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.PermissionController", "Method": "GetPermission", "RelativePath": "api/Permission/GetPermission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PermissionsName", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[Traceability.ErrorCount.APIPageing`1[[Traceability.Domain.RBAC.PermissionModel, Traceability.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], Traceability.ErrorCount, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.RoleController", "Method": "GetAllRole", "RelativePath": "api/Role/GetAllRole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Collections.Generic.List`1[[Traceability.Domain.RBAC.RoleModel, Traceability.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.RoleController", "Method": "GetRole", "RelativePath": "api/Role/GetRole", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "RoleName", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[Traceability.ErrorCount.APIPageing`1[[Traceability.API.Read.Dto.RBAC.RoleDto, Traceability.API.Read, Version=*******, Culture=neutral, PublicKeyToken=null]], Traceability.ErrorCount, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Read.Controllers.RBAC.UserController", "Method": "GetUser", "RelativePath": "api/User/GetUser", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "PageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[Traceability.ErrorCount.APIPageing`1[[Traceability.API.Read.Dto.RBAC.UserDto, Traceability.API.Read, Version=*******, Culture=neutral, PublicKeyToken=null]], Traceability.ErrorCount, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]