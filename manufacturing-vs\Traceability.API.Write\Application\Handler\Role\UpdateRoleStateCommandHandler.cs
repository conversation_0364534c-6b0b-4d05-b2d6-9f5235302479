﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Write.Application.Command.Role;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Write.Application.Handler.Role
{
    public class UpdateRoleStateCommandHandler : IRequestHandler<UpdateRoleStateCommand, APIResult<object>>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<RoleModel> _roleRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<RolePermissionModel> _rolePermissionRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;
        /// <summary>
        /// 
        /// </summary>
        private readonly ILogger<UpdateRoleCommandHandler> _logger;
        private readonly IIdentifyService identifyService;

        public UpdateRoleStateCommandHandler(IBaseRepository<RoleModel> roleRepository, IBaseRepository<RolePermissionModel> rolePermissionRepository, I<PERSON>apper mapper, ILogger<UpdateRoleCommandHandler> logger,IIdentifyService identifyService)
        {
            _roleRepository = roleRepository;
            _rolePermissionRepository = rolePermissionRepository;
            _mapper = mapper;
            _logger = logger;
            this.identifyService = identifyService;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public async Task<APIResult<object>> Handle(UpdateRoleStateCommand request, CancellationToken cancellationToken)
        {
            APIResult<object> result = new APIResult<object>();
            result.Code = ResultCode.Success;
            result.Message = "状态修改成功";
            /* 为什么使用执行策略？
             * 原因是：
                MySQL 配置了重试策略，但您直接在代码中开启了事务
                当使用重试执行策略时，不能直接创建事务，而必须通过策略来包装事务操作
                如果直接创建事务，在重试时可能会导致数据不一致或事务状态混乱
             * 使用异步是为了不阻塞线程
             */
            //创建执行策略
            var strategy = _roleRepository.Context.Database.CreateExecutionStrategy();
            //使用实行策略执行事务操作
            await strategy.ExecuteAsync(async () =>
            {
                //在执行策略内部开启事务
                using (var tr = await _roleRepository.Context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        //修改角色
                        var role = _roleRepository.GetAll().FirstOrDefault(x=>x.Id==request.Id);
                        role.RoleState =!role.RoleState;
                        role.ModifierId = Convert.ToInt64(identifyService.UserId);
                        role.ModifyTime = DateTime.Now;
                        await _roleRepository.UpdateAsync(role);

                        //修改角色权限
                        var rolePermission = _rolePermissionRepository.GetAll().Where(x => x.RoleId == role.Id).ToList();
                        foreach (var item in rolePermission)
                        {
                            item.IsDeleted = !item.IsDeleted;
                        }
                        _logger.LogInformation($"角色状态修改成功！");
                        //提交
                        await tr.CommitAsync();
                    }
                    catch (Exception ex)
                    {

                        result.Code = ResultCode.Fail;
                        result.Message = "角色状态修改失败";
                        _logger.LogError($"修改角色状态时出现异常:{ex.Message}");
                        //回滚
                        await tr.RollbackAsync();
                    }
                }
            });
            return result;
        }
    }
}
