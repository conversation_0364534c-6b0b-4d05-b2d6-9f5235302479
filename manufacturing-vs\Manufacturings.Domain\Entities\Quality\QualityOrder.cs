using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Quality
{
    /// <summary>
    /// 质检工单表
    /// </summary>
    [Table("QualityOrder")]
    public class QualityOrder : BaseEntity
    {
        /// <summary>
        /// 质检工单编号
        /// </summary>
        [StringLength(50)]
        public string OrderCode { get; set; }

        /// <summary>
        /// 来源工单编号
        /// </summary>
        [StringLength(50)]
        public string SourceOrderCode { get; set; }

        /// <summary>
        /// 生产物品ID
        /// </summary>
        public long? ProductId { get; set; }

        /// <summary>
        /// 质检负责人ID
        /// </summary>
        public long? InspectorId { get; set; }

        /// <summary>
        /// 质检部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 质检工单主题
        /// </summary>
        [StringLength(200)]
        public string Theme { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [StringLength(50)]
        public string QualityOrderStatus { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string QualityOrderNote { get; set; }

        /// <summary>
        /// 工序数量
        /// </summary>
        public int? OperationCount { get; set; }
    }
}
