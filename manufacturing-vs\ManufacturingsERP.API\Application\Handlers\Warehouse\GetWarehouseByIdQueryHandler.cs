using MediatR;
using ManufacturingsERP.API.Application.DTOs;
using ManufacturingsERP.API.Application.Interfaces;
using Manufacturings.Infrastructrue.Error;
using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain.Entities.Common;

namespace ManufacturingsERP.API.Application.Handlers.Warehouse
{
    /// <summary>
    /// 根据ID获取仓库详情查询处理器
    /// </summary>
    public class GetWarehouseByIdQueryHandler : IRequestHandler<GetWarehouseByIdQuery, APIResult<WarehouseDetailDto>>
    {
        private readonly IWarehouseRepository _warehouseRepository;
        private readonly IBaseRepository<WarehouseCategory> _categoryRepository;
        private readonly IBaseRepository<StorageType> _storageTypeRepository;
        private readonly IBaseRepository<WarehouseStructure> _structureRepository;
        private readonly IBaseRepository<Person> _personRepository;

        public GetWarehouseByIdQueryHandler(
            IWarehouseRepository warehouseRepository,
            IBaseRepository<WarehouseCategory> categoryRepository,
            IBaseRepository<StorageType> storageTypeRepository,
            IBaseRepository<WarehouseStructure> structureRepository,
            IBaseRepository<Person> personRepository)
        {
            _warehouseRepository = warehouseRepository;
            _categoryRepository = categoryRepository;
            _storageTypeRepository = storageTypeRepository;
            _structureRepository = structureRepository;
            _personRepository = personRepository;
        }

        public async Task<APIResult<WarehouseDetailDto>> Handle(GetWarehouseByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取仓库
                var warehouse = await _warehouseRepository.GetModel(request.Id);
                if (warehouse == null || warehouse.IsDeleted)
                {
                    return new APIResult<WarehouseDetailDto>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库不存在",
                        Data = null
                    };
                }

                // 获取关联数据
                var category = await _categoryRepository.GetModel(warehouse.CategoryId);
                var storageType = warehouse.StorageTypeId.HasValue ? await _storageTypeRepository.GetModel(warehouse.StorageTypeId.Value) : null;
                var structure = warehouse.StructureId.HasValue ? await _structureRepository.GetModel(warehouse.StructureId.Value) : null;
                var person = warehouse.PersonInChargeId.HasValue ? await _personRepository.GetModel(warehouse.PersonInChargeId.Value) : null;
                var parentWarehouse = warehouse.ParentId.HasValue ? await _warehouseRepository.GetModel(warehouse.ParentId.Value) : null;

                // 转换为DTO
                var warehouseDto = new WarehouseDetailDto
                {
                    Id = warehouse.Id,
                    WarehouseNumber = warehouse.WarehouseNumber,
                    WarehouseName = warehouse.WarehouseName,
                    ParentId = warehouse.ParentId,
                    ParentName = parentWarehouse?.WarehouseName,
                    CategoryId = warehouse.CategoryId,
                    CategoryName = category?.CategoryName ?? "",
                    StorageTypeId = warehouse.StorageTypeId,
                    StorageTypeName = storageType?.TypeName,
                    StructureId = warehouse.StructureId,
                    StructureName = structure?.StructureName,
                    PersonInChargeId = warehouse.PersonInChargeId,
                    PersonInChargeName = person?.Name,
                    Address = warehouse.Address,
                    Remarks = warehouse.Remarks,
                    IsEnabled = warehouse.IsEnabled,
                    IsSystemNumber = warehouse.IsSystemNumber,
                    CreateTime = warehouse.CreateTime,
                    CreateName = warehouse.CreateName,
                    ModifyTime = warehouse.ModifyTime,
                    ModifierName = warehouse.ModifierName
                };

                return new APIResult<WarehouseDetailDto>
                {
                    Code = ResultCode.Success,
                    Message = "获取仓库详情成功",
                    Data = warehouseDto
                };
            }
            catch (Exception ex)
            {
                return new APIResult<WarehouseDetailDto>
                {
                    Code = ResultCode.Fail,
                    Message = $"获取仓库详情失败: {ex.Message}",
                    Data = null
                };
            }
        }
    }
} 