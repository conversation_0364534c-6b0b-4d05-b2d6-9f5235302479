[{"ContainingType": "I_FileUpload.Controllers.FileUploadController", "Method": "ListBuckets", "RelativePath": "api/FileUpload/ListBuckets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "I_FileUpload.Controllers.FileUploadController", "Method": "UploadFile", "RelativePath": "api/FileUpload/UploadFile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "I_FileUpload.Controllers.FileUploadController", "Method": "UploadFiles", "RelativePath": "api/FileUpload/UploadFiles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "formFiles", "Type": "Microsoft.AspNetCore.Http.IFormFileCollection", "IsRequired": false}], "ReturnTypes": []}]