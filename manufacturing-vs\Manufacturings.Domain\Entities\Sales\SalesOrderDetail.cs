using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售订单明细表
    /// </summary>
    public class SalesOrderDetail : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 销售数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal SalesQuantity { get; set; }

        /// <summary>
        /// 主单位数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal MainUnitQuantity { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 0;

        /// <summary>
        /// 含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPriceIncludingTax { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountIncludingTax { get; set; }

        /// <summary>
        /// 不含税单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPriceExcludingTax { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountExcludingTax { get; set; }

        /// <summary>
        /// 折扣率
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal DiscountRate { get; set; } = 100;

        /// <summary>
        /// 折扣金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 