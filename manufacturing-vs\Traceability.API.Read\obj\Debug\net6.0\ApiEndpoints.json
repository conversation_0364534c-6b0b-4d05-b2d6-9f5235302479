[{"ContainingType": "Traceability.API.Read.Controllers.PermissionController", "Method": "GetPermission", "RelativePath": "api/Permission/GetPermission", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PermissionsName", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Collections.Generic.List`1[[Traceability.Domain.RBAC.PermissionModel, Traceability.Domain, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]