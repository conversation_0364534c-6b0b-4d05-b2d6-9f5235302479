﻿using MediatR;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.User
{
    public class DeleteUserCommand:IRequest<APIResult<object>>
    {
        public long Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool UserState { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public long CreateId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
        /// <summary>
        /// 角色Id
        /// </summary>

        public List<int> RoleId { get; set; }
    }
}
