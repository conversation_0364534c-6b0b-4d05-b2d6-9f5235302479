﻿using System.Security.Claims;

namespace Traceability.API.Write.Common
{
    public class IdentifyService : IIdentifyService
    {
        private readonly IHttpContextAccessor _contextAccessor;
        public IdentifyService(IHttpContextAccessor contextAccessor)
        {
            _contextAccessor = contextAccessor;
            UserId = _contextAccessor.HttpContext.User.FindFirstValue("UserId");
            UserName = _contextAccessor.HttpContext.User.FindFirstValue("Nickname");
        }


        public string UserId { get; set; }

        public string UserName { get; set; }

    }
    public interface IIdentifyService
    {
        /// <summary>
        /// 定义身份服务的相关方法
        /// </summary>
        public string UserId { get; set; }

        public string UserName { get; set; }
    }
}
