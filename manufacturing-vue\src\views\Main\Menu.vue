<template>
  <div class="menu-container">
    <!-- Header Navigation -->
    <el-container>
      <el-header class="top-header">
        <div class="logo-container">
          <span class="company-name">ERP&MES</span>
        </div>

        <div class="header-nav">
          <el-menu mode="horizontal" class="horizontal-menu" background-color="transparent" text-color="#fff" active-text-color="#fff" :ellipsis="false" :default-active="activeTopMenu">
            <el-menu-item 
              v-for="menu in topMenus" 
              :key="menu.index" 
              :index="menu.index"
              @click="handleTopMenuClick(menu)"
            >
              {{ menu.label }}
            </el-menu-item>
          </el-menu>
        </div>

        <div class="header-right">
          <el-button link class="header-icon">
            <el-icon><Bell /></el-icon>
          </el-button>
          
          <!-- 用户下拉菜单 -->
          <el-dropdown @command="handleUserCommand" trigger="click">
            <div class="user-dropdown-trigger">
              <el-icon><User /></el-icon>
              <span>{{ store.userInfo.Nickname || store.userInfo.UserName || 'Admin' }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><Document /></el-icon>
                  <span>基本资料</span>
                </el-dropdown-item>
                <el-dropdown-item command="password">
                  <el-icon><Lock /></el-icon>
                  <span>修改密码</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  <span>退出系统</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          
          <el-button link class="header-icon">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </el-header>
      <el-container class="main-content">
        <!-- Left Sidebar -->
        <el-aside width="250px" class="sidebar">
          <el-menu
              :default-active="activeMainMenu"
              class="el-menu-vertical"
              background-color="#2c3e50"
              text-color="#ecf0f1"
              active-text-color="#3498db"
              unique-opened
          >
            <!-- 动态渲染左侧菜单项 -->
            <template v-for="menuItem in currentLeftMenus" :key="menuItem.value || menuItem.id">
              <!-- 如果有子菜单，使用子菜单组件 -->
              <el-sub-menu v-if="menuItem.children && menuItem.children.length > 0" :index="menuItem.value?.toString() || menuItem.id?.toString()">
                <template #title>
                  <el-icon v-if="menuItem.icon"><component :is="menuItem.icon" /></el-icon>
                  <span>{{ menuItem.label }}</span>
                </template>
                <el-menu-item 
                  v-for="child in menuItem.children" 
                  :key="child.value || child.id"
                  :index="child.value?.toString() || child.id?.toString()"
                  @click="handleLeftMenuClick(child)"
                >
                  <el-icon v-if="child.icon"><component :is="child.icon" /></el-icon>
                  <span>{{ child.label }}</span>
                </el-menu-item>
              </el-sub-menu>
              <!-- 如果没有子菜单，使用普通菜单项 -->
              <el-menu-item 
                v-else 
                :index="menuItem.value?.toString() || menuItem.id?.toString()"
                @click="handleLeftMenuClick(menuItem)"
              >
                <el-icon v-if="menuItem.icon"><component :is="menuItem.icon" /></el-icon>
                <span>{{ menuItem.label }}</span>
              </el-menu-item>
            </template>
          </el-menu>
        </el-aside>
        
        <el-main class="content-section">
          <!-- 标签页导航 -->
          <el-tabs 
            v-model="store.activeTab" 
            type="card" 
            closable 
            @tab-remove="store.removeTab"
            @tab-click="handleTabClick"
            class="content-tabs"
          >
            <el-tab-pane
              v-for="item in store.tabs"
              :key="item.name"
              :label="item.title"
              :name="item.name"
              :closable="item.closable"
            >
            </el-tab-pane>
          </el-tabs>
          
          <!-- 路由视图 -->
          <div class="content-container" v-if="store.activeTab">
            <router-view />
          </div>
          
          <!-- 工作台内容 -->
          <div class="workbench-content" v-else>
            <div class="page-title">工作台</div>
            
            <!-- 核心指标区域 -->
            <div class="core-indicators">
              <div class="indicators-header">
                <h3>核心指标</h3>
                <span class="date">{{ currentDate }}</span>
              </div>
              <div class="indicators-grid">
                <div class="indicator-item">
                  <div class="indicator-icon">¥</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日销售收入(元)</div>
                    <div class="indicator-value">5000.00</div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-icon">📄</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日销售订单量(笔)</div>
                    <div class="indicator-value">100</div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-icon">💰</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日回款(元)</div>
                    <div class="indicator-value">5000.00</div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-icon">🛒</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日采购支出(元)</div>
                    <div class="indicator-value">5000.00</div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-icon">📋</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日采购订单(笔)</div>
                    <div class="indicator-value">5000.00</div>
                  </div>
                </div>
                <div class="indicator-item">
                  <div class="indicator-icon">💳</div>
                  <div class="indicator-content">
                    <div class="indicator-label">今日已付款</div>
                    <div class="indicator-value">5000.00</div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 统计图表区域 -->
            <div class="statistics-section">
              <div class="statistics-tabs">
                <el-tabs v-model="activeStatisticsTab" class="statistics-tabs-content">
                  <el-tab-pane label="销售统计" name="sales">
                    <div class="chart-container">
                      <div class="chart-header">
                        <div class="chart-legend">
                          <span class="legend-item">
                            <span class="legend-color bar-color"></span>
                            订单额
                          </span>
                          <span class="legend-item">
                            <span class="legend-color line-color"></span>
                            订单数
                          </span>
                        </div>
                        <div class="chart-controls">
                          <el-button size="small" :type="timeRange === 'week' ? 'primary' : 'default'" @click="timeRange = 'week'">最近1周</el-button>
                          <el-button size="small" :type="timeRange === 'month' ? 'primary' : 'default'" @click="timeRange = 'month'">最近30天</el-button>
                        </div>
                      </div>
                      <div class="chart-placeholder">
                        <div class="chart-y-axis">
                          <span>600</span>
                          <span>500</span>
                          <span>400</span>
                          <span>300</span>
                          <span>200</span>
                          <span>100</span>
                          <span>0</span>
                        </div>
                        <div class="chart-content">
                          <div class="chart-bars">
                            <div class="bar" style="height: 60%"></div>
                            <div class="bar" style="height: 80%"></div>
                            <div class="bar" style="height: 40%"></div>
                            <div class="bar" style="height: 90%"></div>
                            <div class="bar" style="height: 70%"></div>
                            <div class="bar" style="height: 50%"></div>
                            <div class="bar" style="height: 85%"></div>
                          </div>
                          <div class="chart-line">
                            <div class="line-point" style="left: 7%; top: 40%"></div>
                            <div class="line-point" style="left: 21%; top: 30%"></div>
                            <div class="line-point" style="left: 35%; top: 60%"></div>
                            <div class="line-point" style="left: 49%; top: 20%"></div>
                            <div class="line-point" style="left: 63%; top: 50%"></div>
                            <div class="line-point" style="left: 77%; top: 35%"></div>
                            <div class="line-point" style="left: 91%; top: 45%"></div>
                          </div>
                        </div>
                        <div class="chart-x-axis">
                          <span>周一</span>
                          <span>周二</span>
                          <span>周三</span>
                          <span>周四</span>
                          <span>周五</span>
                          <span>周六</span>
                          <span>周日</span>
                        </div>
                      </div>
                    </div>
                  </el-tab-pane>
                  <el-tab-pane label="采购统计" name="procurement">
                    <div class="chart-placeholder">
                      <p>采购统计图表</p>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </div>
          
          <!-- 当没有激活标签页时显示空白 -->
          <div class="empty-content" v-else></div>
        </el-main>
        
        <!-- Right Sidebar -->
        <el-aside width="60px" class="right-sidebar">
          <div class="right-toolbar">
            <div class="toolbar-item">
              <el-icon><Search /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><Star /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><User /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><Box /></el-icon>
            </div>
            <div class="toolbar-item">
              <el-icon><Document /></el-icon>
              <span class="toolbar-text">译</span>
            </div>
            <div class="toolbar-item expand-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </el-aside>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { useCounterStore } from '@/stores/counter';
import { getMenu } from '@/Https/server';
const store = useCounterStore();
import { useRouter } from 'vue-router'
const router = useRouter();
import { onMounted, ref, computed } from 'vue'

// 菜单状态
const activeMainMenu = ref('');
const activeTopMenu = ref('production'); // 默认选中"生产"菜单
const activeStatisticsTab = ref('sales');
const timeRange = ref('month');

// 顶部导航菜单数据 - 使用store中的菜单数据
const topMenus = computed(() => {
  // 将store中的菜单数据转换为顶部导航需要的格式
  return store.menus.map((item: any) => ({
    index: item.value?.toString() || item.id?.toString() || '',
    label: item.label || item.title || '',
    url: item.url || item.path || ''
  }));
});

// 当前左侧菜单数据 - 根据选中的顶部菜单动态获取子菜单
const currentLeftMenus = computed(() => {
  // 根据当前选中的顶部菜单，获取对应的子菜单
  const selectedTopMenu = store.menus.find((item: any) => 
    (item.value?.toString() || item.id?.toString()) === activeTopMenu.value
  );
  
  // 如果找到了选中的顶部菜单且有子菜单，返回子菜单
  if (selectedTopMenu && selectedTopMenu.children && selectedTopMenu.children.length > 0) {
    return selectedTopMenu.children;
  }
  
  // 如果没有找到或没有子菜单，返回空数组
  return [];
});

// 获取当前日期
const currentDate = ref(new Date().toISOString().split('T')[0]);

// 根据菜单项获取对应的图标
const getIconForMenu = (item: any) => {
  // 根据菜单名称或类型返回合适的图标
  const iconMap: Record<string, string> = {
    '溯源管理': 'Location',
    '溯源产品管理': 'Document',
    '用户管理': 'User',
    '角色管理': 'UserFilled',
    '权限管理': 'Setting',
  };
  
  return iconMap[item.label] || 'Document';
};

// 获取主菜单图标
const getIconForMainMenu = (item: any) => {
  const iconMap: Record<string, string> = {
    'RBAC管理': 'Setting',
    '溯源管理': 'Location',
  };
  
  return iconMap[item.label] || 'Menu';
};

onMounted(() => {
  // 检查用户是否已登录
  if (!store.userInfo.Nickname || !store.userInfo.roleId) {
    // 用户未登录，跳转到登录页
    router.push('/');
    return;
  }
  
  // 清除之前的标签页状态
  store.clearTabsState();
  
  // 加载菜单数据
  store.getMenus();
  
  // 添加默认标签页 - 工作台
  const defaultTab = {
    title: '工作台',
    label: '工作台',
    url: '/workbench',
    closable: false
  };
  
  store.addTab(defaultTab);
})

//退出功能
const out = () => {
  // 清除用户信息
  store.userInfo.Nickname = '';
  store.userInfo.roleId = '';
  store.userInfo.roleName = '';
  
  // 清除标签页状态
  store.clearTabsState();
  
  // 清除本地存储中的token
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  
  // 清除Pinia持久化存储
  localStorage.removeItem('counter');
  
  // 跳转到登录页
  router.push({path:"/"});
}

import {
  Document,
  Menu as IconMenu,
  Location,
  Setting,
  Lock,
  User,
  UserFilled,
  SwitchButton,
  Lightning,
  Search,
  Bell,
  Star,
  Tools,
  Clock,
  Folder,
  ChatDotRound,
  Box,
  ArrowRight,
  ArrowDown
} from '@element-plus/icons-vue'

//控制菜单是否展开
const isCollapse = ref(false)

// 添加标签页点击事件处理函数
const handleTabClick = (tab: any) => {
  // 处理标签页点击事件，使用store中的方法
  store.handleTabClick(tab.props.name);
};

// 处理用户下拉菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      // 处理基本资料
      console.log('查看基本资料');
      break;
    case 'password':
      // 处理修改密码
      console.log('修改密码');
      break;
    case 'logout':
      // 处理退出系统
      out();
      break;
    default:
      break;
  }
};

// 处理顶部菜单点击
const handleTopMenuClick = (menu: any) => {
  activeTopMenu.value = menu.index;
  console.log('点击了顶部菜单:', menu.label);
  // 重置左侧菜单的选中状态
  activeMainMenu.value = '';
  // 这里可以添加具体的菜单处理逻辑
  // 比如跳转页面、显示对应内容等
};

// 处理左侧菜单点击
const handleLeftMenuClick = (menu: any) => {
  activeMainMenu.value = menu.value?.toString() || menu.id?.toString() || '';
  console.log('点击了左侧菜单:', menu.label);
  
  // 添加到标签页
  const tabItem = {
    title: menu.label,
    label: menu.label,
    url: menu.url || menu.path || '',
    closable: true
  };
  
  store.addTab(tabItem);
  
  // 执行路由跳转
  const routePath = menu.url || menu.path || '';
  if (routePath) {
    router.push(routePath);
  }
};
</script>

<style scoped>
/* ==================== 整体布局 ==================== */
.menu-container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* ==================== 顶部导航栏 ==================== */
.top-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1000;
  min-width: 1200px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.company-name {
  color: white;
  font-size: 20px;
  font-weight: 600;
}

.header-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  min-width: 0;
  overflow: visible;
}

.horizontal-menu {
  border-bottom: none;
  background-color: transparent !important;
  flex-wrap: nowrap;
  overflow: visible;
}

.horizontal-menu :deep(.el-menu-item) {
  color: #fff !important;
  border-bottom: none;
  height: 60px;
  line-height: 60px;
  font-size: 14px;
  font-weight: 500;
  margin: 0 5px;
  border-radius: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

.horizontal-menu :deep(.el-menu-item:hover) {
  background-color: rgba(255,255,255,0.1) !important;
  color: #fff !important;
}

.horizontal-menu :deep(.el-menu-item.is-active) {
  background-color: rgba(255,255,255,0.2) !important;
  color: #fff !important;
  border-bottom: 2px solid #fff;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}



.header-icon {
  color: #fff;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.header-icon:hover {
  background-color: rgba(255,255,255,0.1);
  transform: translateY(-1px);
}

.header-icon .el-icon {
  font-size: 18px;
}

.header-icon span {
  font-size: 14px;
  font-weight: 500;
}

/* 用户下拉菜单样式 */
.user-dropdown-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
}

.user-dropdown-trigger:hover {
  background-color: rgba(255,255,255,0.1);
  transform: translateY(-1px);
}

.user-dropdown-trigger .el-icon {
  font-size: 18px;
}

.user-dropdown-trigger span {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu) {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border: 1px solid #e8e8e8;
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s ease;
}

:deep(.el-dropdown-menu__item:hover) {
  background-color: #f5f7fa;
  color: #409eff;
}

:deep(.el-dropdown-menu__item .el-icon) {
  font-size: 16px;
  margin-right: 0;
}

/* ==================== 主内容区域 ==================== */
.main-content {
  flex: 1;
  overflow: hidden;
}

/* ==================== 侧边栏 ==================== */
.sidebar {
  background-color: #2c3e50;
  height: calc(100vh - 60px);
  overflow: hidden;
  border-right: 1px solid #34495e;
}

.el-menu-vertical {
  height: 100%;
  overflow-y: auto;
  border-right: none;
  background-color: #2c3e50 !important;
}

.el-menu-vertical :deep(.el-menu-item) {
  background-color: #2c3e50 !important;
  color: #ecf0f1 !important;
  border-bottom: 1px solid #34495e;
  height: 50px;
  line-height: 50px;
  margin: 0;
}

.el-menu-vertical :deep(.el-menu-item:hover) {
  background-color: #34495e !important;
  color: #fff !important;
}

.el-menu-vertical :deep(.el-menu-item.is-active) {
  background-color: #34495e !important;
  color: #fff !important;
  border-left: 4px solid #3498db;
}

.active-menu-item {
  background-color: #34495e !important;
  color: #fff !important;
  border-left: 4px solid #3498db;
}

.el-menu-vertical :deep(.el-icon) {
  margin-right: 10px;
  font-size: 16px;
}

/* ==================== 内容区域 ==================== */
.content-section {
  padding: 20px;
  background-color: #f5f7fa;
  height: calc(100vh - 60px);
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.content-tabs {
  margin-bottom: 20px;
}

.content-tabs :deep(.el-tabs__header) {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 4px;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e8e8e8;
}

.content-tabs :deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.content-tabs :deep(.el-tabs__item:hover) {
  color: #1890ff;
  background-color: #f0f9ff;
}

.content-tabs :deep(.el-tabs__item.is-active) {
  color: #1890ff;
  font-weight: 600;
}

/* ==================== 内容容器 ==================== */
.content-container {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: auto;
  border: 1px solid #e8e8e8;
}

/* ==================== 工作台内容 ==================== */
.workbench-content {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  overflow: auto;
  border: 1px solid #e8e8e8;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

/* ==================== 核心指标 ==================== */
.core-indicators {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.indicators-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.indicators-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.date {
  color: #7f8c8d;
  font-size: 14px;
}

.indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.indicator-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.indicator-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  border-color: #1890ff;
}

.indicator-icon {
  font-size: 24px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  border-radius: 8px;
  color: #1976d2;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
  flex-shrink: 0;
}

.indicator-content {
  flex: 1;
}

.indicator-label {
  color: #7f8c8d;
  font-size: 12px;
  margin-bottom: 5px;
}

.indicator-value {
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

/* ==================== 统计图表 ==================== */
.statistics-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.statistics-tabs-content :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

.chart-container {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e8e8e8;
  position: relative;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #7f8c8d;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.bar-color {
  background-color: #1890ff;
}

.line-color {
  background-color: #52c41a;
}

.chart-controls {
  display: flex;
  gap: 10px;
}

.chart-placeholder {
  position: relative;
  height: 300px;
  background-color: #fafafa;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  border: 1px solid #e8e8e8;
  background-image: 
    linear-gradient(90deg, transparent 98%, #e8e8e8 98%),
    linear-gradient(0deg, transparent 98%, #e8e8e8 98%);
  background-size: 14.28% 100%, 100% 16.66%;
}

.chart-y-axis {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px 0;
  color: #7f8c8d;
  font-size: 12px;
  width: 40px;
}

.chart-content {
  position: relative;
  flex: 1;
  margin-left: 40px;
  margin-right: 20px;
  margin-top: 20px;
  margin-bottom: 40px;
}

.chart-bars {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: space-around;
  padding: 0 20px;
}

.bar {
  width: 30px;
  background: linear-gradient(180deg, #1890ff, #096dd9);
  border-radius: 2px 2px 0 0;
  min-height: 20px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.bar:hover {
  transform: scaleY(1.05);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

.chart-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
}

.chart-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #52c41a 0%, #52c41a 100%);
  clip-path: polygon(7% 40%, 21% 30%, 35% 60%, 49% 20%, 63% 50%, 77% 35%, 91% 45%);
}

.line-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #52c41a, #389e0d);
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 2px #52c41a, 0 2px 4px rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;
}

.line-point:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 3px #52c41a, 0 4px 8px rgba(82, 196, 26, 0.4);
}

.chart-x-axis {
  display: flex;
  justify-content: space-around;
  padding: 0 40px 0 40px;
  color: #7f8c8d;
  font-size: 12px;
}

/* ==================== 标签页 ==================== */
:deep(.el-tabs__item) {
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

/* 标签内容容器 */
.tab-content-container {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  overflow: auto;
  height: calc(100% - 60px);
}

/* 空白内容区域 */
.empty-content {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  height: calc(100% - 60px);
}

:deep(.el-tabs__content) {
  flex: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  overflow: auto;
}

/* ==================== 右侧工具栏 ==================== */
.right-sidebar {
  background-color: #f5f5f5;
  border-left: 1px solid #e8e8e8;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.right-toolbar {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  height: 100%;
}

.toolbar-item {
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.toolbar-item:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

.toolbar-item .el-icon {
  font-size: 18px;
  margin-bottom: 2px;
}

.toolbar-text {
  font-size: 10px;
  color: #666;
}

.expand-arrow {
  margin-top: auto;
  margin-bottom: 20px;
}

.expand-arrow:hover {
  background-color: #d9d9d9;
}

/* ==================== 通用样式 ==================== */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}
</style>