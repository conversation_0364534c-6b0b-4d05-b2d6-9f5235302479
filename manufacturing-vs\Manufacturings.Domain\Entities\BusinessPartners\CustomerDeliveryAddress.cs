using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 客户交货地址表
    /// </summary>
    public class CustomerDeliveryAddress : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        public long CustomerId { get; set; }

        /// <summary>
        /// 地址ID
        /// </summary>
        [Required]
        public long AddressId { get; set; }

        /// <summary>
        /// 地址类型（主要/次要）
        /// </summary>
        [StringLength(20)]
        public string? AddressType { get; set; }

        /// <summary>
        /// 是否默认地址
        /// </summary>
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;
    }
} 