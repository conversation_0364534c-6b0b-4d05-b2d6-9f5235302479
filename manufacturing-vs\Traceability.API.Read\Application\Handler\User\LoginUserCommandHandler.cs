﻿using AutoMapper;
using MD5Hash;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Common;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.User
{
    public class LoginUserCommandHandler : IRequestHandler<LoginUserCommand, APIResult<LoginUserDto>>
    {
        /// <summary>
        /// 用户仓储
        /// </summary>
        private readonly IBaseRepository<UserModel> userRepository;
        /// <summary>
        /// 用户角色仓储
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> userRoleRepository;
        /// <summary>
        /// 映射
        /// </summary>
        private readonly IMapper mapper;
        /// <summary>
        /// 角色仓储
        /// </summary>
        private readonly IBaseRepository<RoleModel> roleRepository;
        /// <summary>
        /// Token服务类
        /// </summary>
        private readonly TokenServices _tokenServices;
        /// <summary>
        /// 日志服务
        /// </summary>
        private readonly ILogger<LoginUserCommandHandler> _logger;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="userRepository">用户仓储</param>
        /// <param name="userRoleRepository">用户角色仓储</param>
        /// <param name="mapper">映射</param>
        /// <param name="roleRepository">角色仓储</param>
        /// <param name="tokenServices">角色仓储</param>
        /// <param name="logger">日志服务</param>
        public LoginUserCommandHandler(
            IBaseRepository<UserModel> userRepository, 
            IBaseRepository<UserRoleModel> userRoleRepository, 
            IMapper mapper, 
            IBaseRepository<RoleModel> roleRepository,
            TokenServices tokenServices,
            ILogger<LoginUserCommandHandler> logger)
        {
            this.userRepository = userRepository;
            this.userRoleRepository = userRoleRepository;
            this.mapper = mapper;
            this.roleRepository = roleRepository;
            _tokenServices = tokenServices;
            _logger = logger;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public Task<APIResult<LoginUserDto>> Handle(LoginUserCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"开始处理用户登录请求，用户名：{request.UserName}");
            APIResult<LoginUserDto> result = new APIResult<LoginUserDto>();
            
            try
            {
                result.Code = ResultCode.Success;
                result.Message = "登录成功";
                // 使用LINQ的Join功能在Handler中合并查询
                var query = from user in userRepository.GetAll().AsNoTracking()
                            where user.Username == request.UserName
                            select new
                            {
                                User = user,
                                Roles = (from ur in userRoleRepository.GetAll()
                                         join role in roleRepository.GetAll() on ur.RoleId equals role.Id
                                         where ur.UserId == user.Id
                                         select new { RoleId = role.Id, RoleName = role.RoleName }).ToList()
                            };

                var list = query.FirstOrDefault();

                if (list == null)
                {
                    _logger.LogWarning($"登录失败：用户名不存在，用户名：{request.UserName}");
                    result.Code = ResultCode.Fail;
                    result.Message = "账号不存在";
                    return Task.FromResult(result);
                }
                var pwd = request.PassWord.GetMD5();
                if (list.User.Password != pwd)
                {
                    _logger.LogWarning($"登录失败：密码错误，用户名：{request.UserName}");
                    result.Code = ResultCode.Fail;
                    result.Message = "密码错误";
                    return Task.FromResult(result);
                }
                if (list.User.UserState == false)
                {
                    _logger.LogWarning($"登录失败：账号已禁用，用户名：{request.UserName}");
                    result.Code = ResultCode.Fail;
                    result.Message = "账号已禁用";
                    return Task.FromResult(result);
                }

                var userDto = mapper.Map<LoginUserDto>(list.User);
                userDto.RoleId = string.Join(",", list.Roles.Select(r => r.RoleId));
                userDto.RoleName = string.Join(",", list.Roles.Select(r => r.RoleName));

                //生成访问Token
                result.Token = _tokenServices.GenerateJwtToken(userDto);
                //获取刷新Token
                result.RefreshToken = _tokenServices.GenRefreshToken(userDto.Id);
                result.Data = userDto;
                
                _logger.LogInformation($"用户登录成功，用户名：{request.UserName}，角色：{userDto.RoleName}");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户登录处理过程中出现异常：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "登录失败，服务器异常";
                return Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
