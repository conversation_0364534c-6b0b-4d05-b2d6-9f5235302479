using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Quality
{
    /// <summary>
    /// 质检记录表
    /// </summary>
    [Table("QualityInspectionRecord")]
    public class QualityInspectionRecord : BaseEntity
    {
        /// <summary>
        /// 关联报工记录ID
        /// </summary>
        public long? ReportId { get; set; }

        /// <summary>
        /// 质检人员ID
        /// </summary>
        public long? InspectorId { get; set; }

        /// <summary>
        /// 质检执行时间
        /// </summary>
        public DateTime? InspectionTime { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int? QualifiedQty { get; set; }

        /// <summary>
        /// 不良数量
        /// </summary>
        public int? DefectQty { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [StringLength(50)]
        public string InspectionStatus { get; set; }

        /// <summary>
        /// 合格率
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal? PassRate { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的报工记录
        /// </summary>
        [ForeignKey("ReportId")]
        public virtual WorkReport WorkReport { get; set; }
    }
}
