<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.Write</name>
    </assembly>
    <members>
        <member name="P:Traceability.API.Write.Application.Command.CreatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.CreatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.CreatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.CreatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.CreatePermissionCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.DelPermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.DelPermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.DelPermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.DelPermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.UpdatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.UpdatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.UpdatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.UpdatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.UpdatePermissionCommand.UpdateId">
            <summary>
            修改人Id
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Controllers.PermissionController">
            <summary>
            权限管理
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.PermissionController.CreatePermission(Traceability.API.Write.Application.Command.CreatePermissionCommand)">
            <summary>
            权限添加接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.PermissionController.UpdatePermission(Traceability.API.Write.Application.Command.UpdatePermissionCommand)">
            <summary>
            权限编辑接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.PermissionController.Handle(Traceability.API.Write.Application.Command.DelPermissionCommand)">
            <summary>
            权限删除接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.MappingProfile">
            <summary>
            
            </summary>
        </member>
    </members>
</doc>
