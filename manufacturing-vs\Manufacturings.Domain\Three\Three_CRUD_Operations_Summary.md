# Three模块 CRUD操作实现总结

## 概述
本文档总结了Three模块中已实现的CRUD（创建、读取、更新、删除）操作。

## 已实现的实体CRUD操作

### 1. ProcessClassification（工艺分类）
- ✅ **创建**: `ProcessClassificationAddCommand` + `ProcessClassificationAddCommandHandler`
- ✅ **读取**: 已在读操作中实现
- ✅ **更新**: `ProcessClassificationUpdateCommand` + `ProcessClassificationUpdateCommandHandler`
- ✅ **删除**: `ProcessClassificationDeleteCommand` + `ProcessClassificationDeleteCommandHandler`
- ✅ **批量删除**: `ProcessClassificationBatchDeleteCommand` + `ProcessClassificationBatchDeleteCommandHandler`

### 2. Process（工艺）
- ✅ **创建**: `ProcessAddCommand` + `ProcessAddCommandHandler`
- ✅ **读取**: 已在读操作中实现
- ✅ **更新**: `ProcessUpdateCommand` + `ProcessUpdateCommandHandler`
- ✅ **删除**: `ProcessDeleteCommand` + `ProcessDeleteCommandHandler`
- ✅ **批量删除**: `ProcessBatchDeleteCommand` + `ProcessBatchDeleteCommandHandler`

### 3. OutboundOrder（出库单）
- ✅ **创建**: `OutboundOrderAddCommand` + `OutboundOrderAddCommandHandler`
- ✅ **读取**: 已在读操作中实现
- ✅ **更新**: `OutboundOrderUpdateCommand` + `OutboundOrderUpdateCommandHandler`
- ✅ **删除**: `OutboundOrderDeleteCommand` + `OutboundOrderDeleteCommandHandler`
- ✅ **批量删除**: `OutboundOrderBatchDeleteCommand` + `OutboundOrderBatchDeleteCommandHandler`

### 4. WorkOrder（工单）
- ✅ **创建**: `WorkOrderAddCommand` + `WorkOrderAddCommandHandler`
- ✅ **读取**: 已在读操作中实现
- ✅ **更新**: `WorkOrderUpdateCommand` + `WorkOrderUpdateCommandHandler`
- ✅ **删除**: `WorkOrderDeleteCommand` + `WorkOrderDeleteCommandHandler`
- ✅ **批量删除**: `WorkOrderBatchDeleteCommand` + `WorkOrderBatchDeleteCommandHandler`

## API端点

### 工艺分类管理
- `POST /api/Three/ProcessClassificationAdd` - 新增工艺分类
- `PUT /api/Three/ProcessClassificationUpdate` - 修改工艺分类
- `DELETE /api/Three/ProcessClassificationDelete` - 删除工艺分类
- `POST /api/Three/ProcessClassificationBatchDelete` - 批量删除工艺分类

### 工艺管理
- `POST /api/Three/ProcessAdd` - 新增工艺
- `PUT /api/Three/ProcessUpdate` - 修改工艺
- `DELETE /api/Three/ProcessDelete` - 删除工艺
- `POST /api/Three/ProcessBatchDelete` - 批量删除工艺

### 出库单管理
- `POST /api/Three/OutboundOrderAdd` - 新增出库单
- `PUT /api/Three/OutboundOrderUpdate` - 修改出库单
- `DELETE /api/Three/OutboundOrderDelete` - 删除出库单
- `POST /api/Three/OutboundOrderBatchDelete` - 批量删除出库单

### 工单管理
- `POST /api/Three/WorkOrderAdd` - 新增工单
- `PUT /api/Three/WorkOrderUpdate` - 修改工单
- `DELETE /api/Three/WorkOrderDelete` - 删除工单
- `POST /api/Three/WorkOrderBatchDelete` - 批量删除工单

## 技术特点

### 1. 架构模式
- **CQRS模式**: 读写分离，使用MediatR实现命令查询职责分离
- **Repository模式**: 数据访问层抽象
- **Command模式**: 命令对象封装业务操作

### 2. 数据一致性
- **软删除**: 所有删除操作都使用软删除（IsDeleted = true）
- **事务处理**: 主从表操作在同一事务中处理
- **级联删除**: 删除主表时自动删除相关明细表

### 3. 审计功能
- **创建信息**: CreateName, CreateTime
- **更新信息**: UpdateName, UpdateTime
- **用户上下文**: 通过IIdContextCemil获取当前用户信息

### 4. 错误处理
- **统一返回格式**: APIResult<T>
- **状态码**: ApiEnum.Success/ApiEnum.Fail
- **错误消息**: 中文友好的错误提示

## 待实现的实体

以下实体需要继续实现CRUD操作：

### 1. 基础数据管理
- [ ] MaterialCategory（物料分类）
- [ ] Material（物料）
- [ ] Inventory（库存）
- [ ] Supplier（供应商）
- [ ] Customer（客户）
- [ ] Project（项目）
- [ ] Warehouse（仓库）
- [ ] StorageLocation（库位）
- [ ] Department（部门）
- [ ] Employee（员工）

### 2. 业务单据
- [ ] OutsourcingProcessing（外协加工单）
- [ ] OutsourcingWorkOrder（外协工单）

### 3. 系统管理
- [ ] ApprovalProcess（审批流程）
- [ ] ApprovalNode（审批节点）
- [ ] OperationRecord（操作记录）
- [ ] Attachment（附件）

## 文件结构

```
B.S.SmartCity/B.S.BaseData.Api.Write/
├── Applicantion/
│   ├── Command/ThreeCommand/
│   │   ├── ProcessClassificationAddCommand.cs
│   │   ├── ProcessClassificationUpdateCommand.cs
│   │   ├── ProcessClassificationDeleteCommand.cs
│   │   ├── ProcessClassificationBatchDeleteCommand.cs
│   │   ├── ProcessAddCommand.cs
│   │   ├── ProcessUpdateCommand.cs
│   │   ├── ProcessDeleteCommand.cs
│   │   ├── ProcessBatchDeleteCommand.cs
│   │   ├── OutboundOrderAddCommand.cs
│   │   ├── OutboundOrderUpdateCommand.cs
│   │   ├── OutboundOrderDeleteCommand.cs
│   │   ├── OutboundOrderBatchDeleteCommand.cs
│   │   ├── WorkOrderAddCommand.cs
│   │   ├── WorkOrderUpdateCommand.cs
│   │   ├── WorkOrderDeleteCommand.cs
│   │   └── WorkOrderBatchDeleteCommand.cs
│   └── CommandHandler/ThreeCommandHandler/
│       ├── ProcessClassificationAddCommandHandler.cs
│       ├── ProcessClassificationUpdateCommandHandler.cs
│       ├── ProcessClassificationDeleteCommandHandler.cs
│       ├── ProcessClassificationBatchDeleteCommandHandler.cs
│       ├── ProcessAddCommandHandler.cs
│       ├── ProcessUpdateCommandHandler.cs
│       ├── ProcessDeleteCommandHandler.cs
│       ├── ProcessBatchDeleteCommandHandler.cs
│       ├── OutboundOrderAddCommandHandler.cs
│       ├── OutboundOrderUpdateCommandHandler.cs
│       ├── OutboundOrderDeleteCommandHandler.cs
│       ├── OutboundOrderBatchDeleteCommandHandler.cs
│       ├── WorkOrderAddCommandHandler.cs
│       ├── WorkOrderUpdateCommandHandler.cs
│       ├── WorkOrderDeleteCommandHandler.cs
│       └── WorkOrderBatchDeleteCommandHandler.cs
└── Controllers/
    └── ThreeController.cs
```

## 使用示例

### 新增工艺分类
```json
POST /api/Three/ProcessClassificationAdd
{
    "classificationName": "机加工",
    "classificationCode": "JJG",
    "description": "机械加工工艺",
    "parentId": null,
    "sortOrder": 1,
    "isEnabled": true
}
```

### 新增出库单
```json
POST /api/Three/OutboundOrderAdd
{
    "orderNumber": "CK20241201001",
    "outboundType": 1,
    "outboundDate": "2024-12-01",
    "warehouseId": 1,
    "applicant": "张三",
    "department": "生产部",
    "remarks": "生产领料",
    "status": 1,
    "details": [
        {
            "materialId": 1,
            "quantity": 100,
            "unit": "个",
            "batchNumber": "*********",
            "remarks": "标准件"
        }
    ]
}
```

## 注意事项

1. **ID生成**: 使用YitIdHelper.NextId()生成雪花ID
2. **时间处理**: 创建和更新时间自动设置
3. **用户信息**: 通过IIdContextCemil获取当前用户
4. **数据验证**: 在CommandHandler中进行业务逻辑验证
5. **异常处理**: 统一的错误返回格式

## 下一步计划

1. 继续实现剩余实体的CRUD操作
2. 添加数据验证和业务规则
3. 实现更复杂的查询功能
4. 添加单元测试
5. 完善API文档

