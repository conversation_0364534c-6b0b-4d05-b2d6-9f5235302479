using MediatR;
using ManufacturingsERP.API.Application.DTOs;
using ManufacturingsERP.API.Application.Interfaces;
using Manufacturings.Infrastructrue.Error;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Infrastructrue;

namespace ManufacturingsERP.API.Application.Handlers.Warehouse
{
    /// <summary>
    /// 创建仓库命令处理器
    /// </summary>
    public class CreateWarehouseCommandHandler : IRequestHandler<CreateWarehouseCommand, APIResult<long>>
    {
        private readonly IWarehouseRepository _warehouseRepository;
        private readonly IBaseRepository<WarehouseCategory> _categoryRepository;
        private readonly IBaseRepository<StorageType> _storageTypeRepository;
        private readonly IBaseRepository<WarehouseStructure> _structureRepository;
        private readonly IBaseRepository<Person> _personRepository;

        public CreateWarehouseCommandHandler(
            IWarehouseRepository warehouseRepository,
            IBaseRepository<WarehouseCategory> categoryRepository,
            IBaseRepository<StorageType> storageTypeRepository,
            IBaseRepository<WarehouseStructure> structureRepository,
            IBaseRepository<Person> personRepository)
        {
            _warehouseRepository = warehouseRepository;
            _categoryRepository = categoryRepository;
            _storageTypeRepository = storageTypeRepository;
            _structureRepository = structureRepository;
            _personRepository = personRepository;
        }

        public async Task<APIResult<long>> Handle(CreateWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 验证仓库分类是否存在
                var category = await _categoryRepository.GetModel(request.CategoryId);
                if (category == null)
                {
                    return new APIResult<long>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库分类不存在",
                        Data = 0
                    };
                }

                // 验证上级仓库是否存在
                if (request.ParentId.HasValue)
                {
                    var parentWarehouse = await _warehouseRepository.GetModel(request.ParentId.Value);
                    if (parentWarehouse == null)
                    {
                        return new APIResult<long>
                        {
                            Code = ResultCode.Fail,
                            Message = "上级仓库不存在",
                            Data = 0
                        };
                    }
                }

                // 验证存储类型是否存在
                if (request.StorageTypeId.HasValue)
                {
                    var storageType = await _storageTypeRepository.GetModel(request.StorageTypeId.Value);
                    if (storageType == null)
                    {
                        return new APIResult<long>
                        {
                            Code = ResultCode.Fail,
                            Message = "存储类型不存在",
                            Data = 0
                        };
                    }
                }

                // 验证仓库结构是否存在
                if (request.StructureId.HasValue)
                {
                    var structure = await _structureRepository.GetModel(request.StructureId.Value);
                    if (structure == null)
                    {
                        return new APIResult<long>
                        {
                            Code = ResultCode.Fail,
                            Message = "仓库结构不存在",
                            Data = 0
                        };
                    }
                }

                // 验证负责人是否存在
                if (request.PersonInChargeId.HasValue)
                {
                    var person = await _personRepository.GetModel(request.PersonInChargeId.Value);
                    if (person == null)
                    {
                        return new APIResult<long>
                        {
                            Code = ResultCode.Fail,
                            Message = "负责人不存在",
                            Data = 0
                        };
                    }
                }

                // 检查仓库名称是否已存在
                if (await _warehouseRepository.IsWarehouseNameExistsAsync(request.WarehouseName))
                {
                    return new APIResult<long>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库名称已存在",
                        Data = 0
                    };
                }

                // 生成仓库编号
                string warehouseNumber;
                if (request.IsSystemNumber)
                {
                    warehouseNumber = GenerateWarehouseNumber();
                }
                else
                {
                    // 如果用户自定义编号，检查是否已存在
                    var existingWarehouse = await _warehouseRepository.GetByWarehouseNumberAsync(request.WarehouseName);
                    if (existingWarehouse != null)
                    {
                        return new APIResult<long>
                        {
                            Code = ResultCode.Fail,
                            Message = "仓库编号已存在",
                            Data = 0
                        };
                    }
                    warehouseNumber = request.WarehouseName;
                }

                // 创建仓库实体
                var warehouse = new Manufacturings.Domain.Entities.Warehouses.Warehouse
                {
                    WarehouseNumber = warehouseNumber,
                    WarehouseName = request.WarehouseName,
                    ParentId = request.ParentId,
                    CategoryId = request.CategoryId,
                    StorageTypeId = request.StorageTypeId,
                    StructureId = request.StructureId,
                    PersonInChargeId = request.PersonInChargeId,
                    Address = request.Address,
                    Remarks = request.Remarks,
                    IsEnabled = request.IsEnabled,
                    IsSystemNumber = request.IsSystemNumber,
                    CreateTime = DateTime.Now,
                    CreateName = "系统", // 这里应该从当前用户上下文获取
                    IsDeleted = false
                };

                // 保存到数据库
                var result = await _warehouseRepository.AddAsync(warehouse);

                return new APIResult<long>
                {
                    Code = ResultCode.Success,
                    Message = "仓库创建成功",
                    Data = warehouse.Id
                };
            }
            catch (Exception ex)
            {
                return new APIResult<long>
                {
                    Code = ResultCode.Fail,
                    Message = $"创建仓库失败: {ex.Message}",
                    Data = 0
                };
            }
        }

        /// <summary>
        /// 生成仓库编号
        /// </summary>
        private string GenerateWarehouseNumber()
        {
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var random = new Random().Next(1000, 9999);
            return $"CKBH{timestamp}{random}";
        }
    }
} 