-- 完整的仓库管理数据字典脚本 (基于数据库表结构)
-- 适用于MySQL数据库

-- ===================================
-- 1. 仓库分类表 (WarehouseCategory)
-- ===================================

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS WarehouseCategory (
    Id BIGINT AUTO_INCREMENT PRIMARY KEY,
    CategoryName VARCHAR(50) NOT NULL,
    CategoryCode VARCHAR(20) NOT NULL,
    Description VARCHAR(200) NULL,
    SortOrder INT(11) NOT NULL DEFAULT 0,
    IsEnabled TINYINT(1) NOT NULL DEFAULT 1,
    CreateName LONGTEXT NULL,
    CreateTime DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    ModifierName LONGTEXT NULL,
    ModifyTime DATETIME(6) NULL,
    IsDeleted TINYINT(1) NOT NULL DEFAULT 0
);

-- 插入仓库分类数据
INSERT IGNORE INTO WarehouseCategory (Id, CategoryName, CategoryCode, Description, SortOrder, IsEnabled, CreateName, IsDeleted) VALUES
(1, '原材料仓库', 'RAW_MATERIAL', '存放生产所需的原材料', 1, 1, 'system', 0),
(2, '成品仓库', 'FINISHED_GOODS', '存放已完成的成品', 2, 1, 'system', 0),
(3, '半成品仓库', 'SEMI_FINISHED', '存放生产过程中的半成品', 3, 1, 'system', 0),
(4, '工具仓库', 'TOOLS', '存放生产工具和设备', 4, 1, 'system', 0),
(5, '备件仓库', 'SPARE_PARTS', '存放设备备用零件', 5, 1, 'system', 0),
(6, '包装材料仓库', 'PACKAGING', '存放包装用材料', 6, 1, 'system', 0),
(7, '退货仓库', 'RETURNS', '存放退回的商品', 7, 1, 'system', 0),
(8, '废料仓库', 'WASTE', '存放废弃物料', 8, 1, 'system', 0);

-- ===================================
-- 2. 存储类型表 (StorageType)
-- ===================================

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS StorageType (
    Id BIGINT AUTO_INCREMENT PRIMARY KEY,
    TypeName VARCHAR(50) NOT NULL,
    TypeCode VARCHAR(20) NOT NULL,
    Description VARCHAR(200) NULL,
    SortOrder INT(11) NOT NULL DEFAULT 0,
    IsEnabled TINYINT(1) NOT NULL DEFAULT 1,
    CreateName LONGTEXT NULL,
    CreateTime DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    ModifierName LONGTEXT NULL,
    ModifyTime DATETIME(6) NULL,
    IsDeleted TINYINT(1) NOT NULL DEFAULT 0
);

-- 插入存储类型数据
INSERT IGNORE INTO StorageType (Id, TypeName, TypeCode, Description, SortOrder, IsEnabled, CreateName, IsDeleted) VALUES
(1, '常温存储', 'NORMAL_TEMP', '常温环境下存储，温度15-25℃', 1, 1, 'system', 0),
(2, '冷藏存储', 'REFRIGERATED', '冷藏环境存储，温度2-8℃', 2, 1, 'system', 0),
(3, '冷冻存储', 'FROZEN', '冷冻环境存储，温度-18℃以下', 3, 1, 'system', 0),
(4, '恒温存储', 'CONSTANT_TEMP', '恒定温度存储，精确控温', 4, 1, 'system', 0),
(5, '防潮存储', 'MOISTURE_PROOF', '防潮环境存储，湿度控制', 5, 1, 'system', 0),
(6, '防爆存储', 'EXPLOSION_PROOF', '防爆环境存储，用于危险品', 6, 1, 'system', 0),
(7, '无菌存储', 'STERILE', '无菌环境存储，医疗用品专用', 7, 1, 'system', 0),
(8, '通风存储', 'VENTILATED', '通风良好的存储环境', 8, 1, 'system', 0);

-- ===================================
-- 3. 仓库结构表 (WarehouseStructure)
-- ===================================

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS WarehouseStructure (
    Id BIGINT AUTO_INCREMENT PRIMARY KEY,
    StructureName VARCHAR(50) NOT NULL,
    StructureCode VARCHAR(20) NOT NULL,
    Description VARCHAR(200) NULL,
    SortOrder INT(11) NOT NULL DEFAULT 0,
    IsEnabled TINYINT(1) NOT NULL DEFAULT 1,
    CreateName LONGTEXT NULL,
    CreateTime DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
    ModifierName LONGTEXT NULL,
    ModifyTime DATETIME(6) NULL,
    IsDeleted TINYINT(1) NOT NULL DEFAULT 0
);

-- 插入仓库结构数据
INSERT IGNORE INTO WarehouseStructure (Id, StructureName, StructureCode, Description, SortOrder, IsEnabled, CreateName, IsDeleted) VALUES
(1, '平面仓库', 'FLAT_WAREHOUSE', '单层平面布局的仓库结构', 1, 1, 'system', 0),
(2, '立体仓库', 'VERTICAL_WAREHOUSE', '多层立体货架仓库结构', 2, 1, 'system', 0),
(3, '货架仓库', 'RACK_WAREHOUSE', '以货架为主的仓库结构', 3, 1, 'system', 0),
(4, '自动化仓库', 'AUTOMATED_WAREHOUSE', '自动化立体仓库系统', 4, 1, 'system', 0),
(5, '露天仓库', 'OUTDOOR_WAREHOUSE', '露天存储区域', 5, 1, 'system', 0),
(6, '地下仓库', 'UNDERGROUND_WAREHOUSE', '地下存储空间', 6, 1, 'system', 0),
(7, '移动仓库', 'MOBILE_WAREHOUSE', '可移动的仓储设施', 7, 1, 'system', 0),
(8, '智能仓库', 'SMART_WAREHOUSE', '智能化管理的仓库', 8, 1, 'system', 0);

-- ===================================
-- 4. 创建索引以提高查询性能
-- ===================================

-- 仓库分类表索引
CREATE INDEX IF NOT EXISTS idx_warehousecategory_name ON WarehouseCategory(CategoryName);
CREATE INDEX IF NOT EXISTS idx_warehousecategory_code ON WarehouseCategory(CategoryCode);
CREATE INDEX IF NOT EXISTS idx_warehousecategory_enabled ON WarehouseCategory(IsEnabled);
CREATE INDEX IF NOT EXISTS idx_warehousecategory_deleted ON WarehouseCategory(IsDeleted);

-- 存储类型表索引
CREATE INDEX IF NOT EXISTS idx_storagetype_name ON StorageType(TypeName);
CREATE INDEX IF NOT EXISTS idx_storagetype_code ON StorageType(TypeCode);
CREATE INDEX IF NOT EXISTS idx_storagetype_enabled ON StorageType(IsEnabled);
CREATE INDEX IF NOT EXISTS idx_storagetype_deleted ON StorageType(IsDeleted);

-- 仓库结构表索引
CREATE INDEX IF NOT EXISTS idx_warehousestructure_name ON WarehouseStructure(StructureName);
CREATE INDEX IF NOT EXISTS idx_warehousestructure_code ON WarehouseStructure(StructureCode);
CREATE INDEX IF NOT EXISTS idx_warehousestructure_enabled ON WarehouseStructure(IsEnabled);
CREATE INDEX IF NOT EXISTS idx_warehousestructure_deleted ON WarehouseStructure(IsDeleted);

-- ===================================
-- 5. 验证数据插入结果
-- ===================================

-- 查询各表的数据统计
SELECT 
    'WarehouseCategory' as TableName, 
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN IsDeleted = 0 THEN 1 END) as ActiveRecords,
    COUNT(CASE WHEN IsEnabled = 1 AND IsDeleted = 0 THEN 1 END) as EnabledRecords
FROM WarehouseCategory

UNION ALL

SELECT 
    'StorageType' as TableName, 
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN IsDeleted = 0 THEN 1 END) as ActiveRecords,
    COUNT(CASE WHEN IsEnabled = 1 AND IsDeleted = 0 THEN 1 END) as EnabledRecords
FROM StorageType

UNION ALL

SELECT 
    'WarehouseStructure' as TableName, 
    COUNT(*) as TotalRecords,
    COUNT(CASE WHEN IsDeleted = 0 THEN 1 END) as ActiveRecords,
    COUNT(CASE WHEN IsEnabled = 1 AND IsDeleted = 0 THEN 1 END) as EnabledRecords
FROM WarehouseStructure;

-- 显示插入的具体数据
SELECT '=== 仓库分类数据 ===' as Info;
SELECT Id, CategoryName, CategoryCode, Description, SortOrder FROM WarehouseCategory WHERE IsDeleted = 0 ORDER BY SortOrder;

SELECT '=== 存储类型数据 ===' as Info;
SELECT Id, TypeName, TypeCode, Description, SortOrder FROM StorageType WHERE IsDeleted = 0 ORDER BY SortOrder;

SELECT '=== 仓库结构数据 ===' as Info;
SELECT Id, StructureName, StructureCode, Description, SortOrder FROM WarehouseStructure WHERE IsDeleted = 0 ORDER BY SortOrder;
