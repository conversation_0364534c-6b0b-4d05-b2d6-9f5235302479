<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.Read</name>
    </assembly>
    <members>
        <member name="P:Traceability.API.Read.Application.Command.GetPermissionCommand.PermissionsName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Controllers.PermissionController">
            <summary>
            权限管理
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.PermissionController.GetPermission(Traceability.API.Read.Application.Command.GetPermissionCommand)">
            <summary>
            权限显示接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.MappingProfile">
            <summary>
            AutoMapper映射配置
            </summary>
        </member>
    </members>
</doc>
