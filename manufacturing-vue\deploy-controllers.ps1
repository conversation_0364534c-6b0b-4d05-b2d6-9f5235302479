# 部署控制器脚本
Write-Host "开始部署仓库管理数据字典API控制器..." -ForegroundColor Green

# 定义路径
$sourceDir = "D:\实训一\项目\EMS\manufacturing-vue\backend-apis"
$targetDir = "D:\实训一\项目\EMS\manufacturing-vs\ManufacturingsERP.API\Controllers"

# 控制器列表
$controllers = @(
    "WarehouseCategoryController.cs",
    "StorageTypeController.cs", 
    "WarehouseStructureController.cs",
    "PersonController.cs"
)

# 复制控制器文件
foreach ($controller in $controllers) {
    $sourcePath = Join-Path $sourceDir $controller
    $targetPath = Join-Path $targetDir $controller
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath -Force
        Write-Host "✅ 已复制: $controller" -ForegroundColor Green
    } else {
        Write-Host "❌ 文件不存在: $controller" -ForegroundColor Red
    }
}

Write-Host "`n检查目标目录中的控制器文件:" -ForegroundColor Yellow
Get-ChildItem $targetDir -Filter "*Controller.cs" | Select-Object Name, LastWriteTime

Write-Host "`n部署完成！请重新编译并启动后端服务。" -ForegroundColor Green
Write-Host "编译命令: dotnet build" -ForegroundColor Cyan
Write-Host "运行命令: dotnet run" -ForegroundColor Cyan
