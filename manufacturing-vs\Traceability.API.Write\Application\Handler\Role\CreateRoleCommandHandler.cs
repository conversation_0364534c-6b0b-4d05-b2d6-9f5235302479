﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Write.Application.Command.Role;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;
using Yitter.IdGenerator;

namespace Traceability.API.Write.Application.Handler.Role
{
    public class CreateRoleCommandHandler : IRequestHandler<CreateRoleCommand, APIResult<object>>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<RoleModel> _roleRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<RolePermissionModel> _rolePermissionRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;
        private readonly ILogger<CreateRoleCommandHandler> _logger;
        private readonly IIdentifyService identifyService;

        public CreateRoleCommandHandler(IBaseRepository<RoleModel> roleRepository,IBaseRepository<RolePermissionModel> rolePermissionRepository,I<PERSON><PERSON>per mapper,ILogger<CreateRoleCommandHandler> logger,IIdentifyService identifyService)
        {
            _roleRepository = roleRepository;
            _rolePermissionRepository = rolePermissionRepository;
            _mapper = mapper;
            _logger = logger;
            this.identifyService = identifyService;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public async Task<APIResult<object>> Handle(CreateRoleCommand request, CancellationToken cancellationToken)
        {
            APIResult<object> result = new APIResult<object>();
            result.Code = ResultCode.Success;
            result.Message = "添加成功";

            // 验证角色名称是否已存在
            var existingRole = _roleRepository.GetAll().FirstOrDefault(x => x.RoleName == request.RoleName);
            if (existingRole != null)
            {
                _logger.LogWarning($"创建角色失败：角色名称已存在，角色名称：{request.RoleName}");
                result.Code = ResultCode.Fail;
                result.Message = "你要添加的角色名称已存在";
                return result;
            }

            /* 为什么使用执行策略？
             * 原因是：
                MySQL 配置了重试策略，但您直接在代码中开启了事务
                当使用重试执行策略时，不能直接创建事务，而必须通过策略来包装事务操作
                如果直接创建事务，在重试时可能会导致数据不一致或事务状态混乱
             * 使用异步是为了不阻塞线程
             */
            //创建执行策略
            var strategy = _roleRepository.Context.Database.CreateExecutionStrategy();
            //使用实行策略执行事务操作
            await strategy.ExecuteAsync(async () => 
            {
                //在执行策略内部开启事务
                using (var tr = await _roleRepository.Context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        var role = _mapper.Map<RoleModel>(request);
                        role.CreateId = Convert.ToInt64(identifyService.UserId);
                        await _roleRepository.AddAsync(role);

                        //添加角色权限
                        foreach (var item in request.PermissionId)
                        {
                            RolePermissionModel permission = new()
                            {
                                Id = YitIdHelper.NextId(),
                                RoleId = role.Id,
                                PermissionId = item,
                                CreateTime = DateTime.Now,
                                CreateId = Convert.ToInt64(identifyService.UserId)
                            };

                            await _rolePermissionRepository.AddAsync(permission);
                        }

                        _logger.LogInformation($"角色及权限添加成功！");
                        //提交
                        await tr.CommitAsync();
                    }
                    catch (Exception ex)
                    {

                        result.Code = ResultCode.Fail;
                        result.Message = "添加失败";
                        _logger.LogError($"添加角色时出现异常:{ex.Message}");
                        //回滚
                        await tr.RollbackAsync();
                    }
                }
            });
            return result;
        }
    }
}
