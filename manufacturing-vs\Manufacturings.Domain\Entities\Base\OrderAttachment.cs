using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用订单附件表
    /// </summary>
    public class OrderAttachment : BaseEntity
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        [Required]
        public long OrderId { get; set; }

        /// <summary>
        /// 订单类型（销售订单/采购订单）
        /// </summary>
        [Required]
        public OrderType OrderType { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AttachmentName { get; set; } = string.Empty;

        /// <summary>
        /// 附件类型
        /// </summary>
        [StringLength(50)]
        public string? AttachmentType { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件扩展名
        /// </summary>
        [StringLength(20)]
        public string? FileExtension { get; set; }

        /// <summary>
        /// 附件描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 上传人ID
        /// </summary>
        [Required]
        public long UploadedBy { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [Required]
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }

    /// <summary>
    /// 订单类型枚举
    /// </summary>
    public enum OrderType
    {
        /// <summary>
        /// 销售订单
        /// </summary>
        SalesOrder = 1,
        /// <summary>
        /// 采购订单
        /// </summary>
        PurchaseOrder = 2
    }
} 