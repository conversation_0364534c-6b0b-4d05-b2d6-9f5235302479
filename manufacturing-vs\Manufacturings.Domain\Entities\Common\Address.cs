using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 地址表
    /// </summary>
    public class Address : BaseEntity
    {
        /// <summary>
        /// 地址类型（公司地址、注册地址、联系地址等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AddressType { get; set; } = string.Empty;

        /// <summary>
        /// 国家
        /// </summary>
        [StringLength(100)]
        public string? Country { get; set; }

        /// <summary>
        /// 省份
        /// </summary>
        [StringLength(100)]
        public string? Province { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        [StringLength(100)]
        public string? City { get; set; }

        /// <summary>
        /// 区县
        /// </summary>
        [StringLength(100)]
        public string? District { get; set; }

        /// <summary>
        /// 街道
        /// </summary>
        [StringLength(200)]
        public string? Street { get; set; }

        /// <summary>
        /// 详细地址
        /// </summary>
        [Required]
        [StringLength(500)]
        public string DetailAddress { get; set; } = string.Empty;

        /// <summary>
        /// 邮政编码
        /// </summary>
        [StringLength(20)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 是否默认地址
        /// </summary>
        [Required]
        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;
    }
} 