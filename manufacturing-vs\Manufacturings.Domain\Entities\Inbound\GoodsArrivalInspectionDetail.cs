using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 到货检验明细表
    /// </summary>
    public class GoodsArrivalInspectionDetail : BaseEntity
    {
        /// <summary>
        /// 到货检验ID
        /// </summary>
        [Required]
        public int GoodsArrivalInspectionId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        [StringLength(100)]
        public string? Brand { get; set; }

        /// <summary>
        /// 采购数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseQuantity { get; set; }

        /// <summary>
        /// 检验类型（免检、抽检、全检等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionType { get; set; } = string.Empty;

        /// <summary>
        /// 收货数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReceivedQuantity { get; set; }

        /// <summary>
        /// 检验数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InspectionQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检验结果（合格、不合格、部分合格等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionResult { get; set; } = "合格";

        /// <summary>
        /// 不合格原因
        /// </summary>
        [StringLength(500)]
        public string? UnqualifiedReason { get; set; }

        /// <summary>
        /// 检验备注
        /// </summary>
        [StringLength(500)]
        public string? InspectionRemarks { get; set; }
    }
} 