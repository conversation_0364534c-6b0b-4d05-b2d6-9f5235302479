using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Warehouses
{
    /// <summary>
    /// 仓库主表
    /// </summary>
    public class Warehouse : BaseEntity
    {
        /// <summary>
        /// 仓库编号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string WarehouseNumber { get; set; } = string.Empty;

        /// <summary>
        /// 仓库名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string WarehouseName { get; set; } = string.Empty;

        /// <summary>
        /// 上级仓库ID（树形结构）
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 仓库分类ID
        /// </summary>
        [Required]
        public int CategoryId { get; set; }

        /// <summary>
        /// 存储类型ID
        /// </summary>
        public int? StorageTypeId { get; set; }

        /// <summary>
        /// 仓库结构ID
        /// </summary>
        public int? StructureId { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? PersonInChargeId { get; set; }

        /// <summary>
        /// 仓库地址
        /// </summary>
        [StringLength(200)]
        public string? Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 状态（启用/禁用）
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;
    }
} 