using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用操作记录表（替代各种专门的操作记录表）
    /// </summary>
    public class CommonOperationRecord : BaseEntity
    {
        /// <summary>
        /// 关联实体ID
        /// </summary>
        [Required]
        public long RelatedEntityId { get; set; }

        /// <summary>
        /// 关联实体类型
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RelatedEntityType { get; set; } = string.Empty;

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        [StringLength(100)]
        public string OperationType { get; set; } = string.Empty;

        /// <summary>
        /// 操作描述
        /// </summary>
        [Required]
        [StringLength(500)]
        public string OperationDescription { get; set; } = string.Empty;

        /// <summary>
        /// 操作前状态
        /// </summary>
        [StringLength(100)]
        public string? BeforeStatus { get; set; }

        /// <summary>
        /// 操作后状态
        /// </summary>
        [StringLength(100)]
        public string? AfterStatus { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Required]
        public long OperatorId { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Required]
        public DateTime OperationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作IP地址
        /// </summary>
        [StringLength(50)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }
    }
} 