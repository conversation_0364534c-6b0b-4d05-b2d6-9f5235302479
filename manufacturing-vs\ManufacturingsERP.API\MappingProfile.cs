﻿using AutoMapper;
using Manufacturings.Domain.Entities.Warehouses;
using ManufacturingsERP.API.Application.DTOs;

namespace ManufacturingsERP.API
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // 仓库实体到DTO的映射
            CreateMap<Warehouse, WarehouseDto>()
                .ForMember(dest => dest.ParentName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.StorageTypeName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.StructureName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.PersonInChargeName, opt => opt.MapFrom(src => "")); // TODO: 通过关联查询获取

            CreateMap<Warehouse, WarehouseDetailDto>()
                .ForMember(dest => dest.ParentName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.StorageTypeName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.StructureName, opt => opt.MapFrom(src => "")) // TODO: 通过关联查询获取
                .ForMember(dest => dest.PersonInChargeName, opt => opt.MapFrom(src => "")); // TODO: 通过关联查询获取

            // 命令到实体的映射（如果需要的话）
            // CreateMap<CreateWarehouseCommand, Warehouse>();
            // CreateMap<UpdateWarehouseCommand, Warehouse>();
        }
    }
}
