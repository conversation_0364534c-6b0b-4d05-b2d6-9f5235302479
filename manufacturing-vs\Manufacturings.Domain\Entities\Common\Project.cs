using Manufacturings.Domain.Entities.Purchase;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 项目表
    /// </summary>
    [Table("Project")]
    public class Project : BaseEntity
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// 项目编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProjectNumber { get; set; } = string.Empty;

        /// <summary>
        /// 项目描述
        /// </summary>
        [StringLength(1000)]
        public string? ProjectDescription { get; set; }

        /// <summary>
        /// 项目状态（进行中、已完成、已暂停、已取消等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProjectStatus { get; set; } = "进行中";

        /// <summary>
        /// 项目阶段
        /// </summary>
        [StringLength(50)]
        public string? ProjectPhase { get; set; }

        /// <summary>
        /// 项目开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 项目结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 项目经理ID
        /// </summary>
        public long? ProjectManagerId { get; set; }

        /// <summary>
        /// 项目预算
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ProjectBudget { get; set; }

        /// <summary>
        /// 项目总额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 预计费用
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? EstimatedCost { get; set; }

        /// <summary>
        /// 项目分类
        /// </summary>
        [StringLength(100)]
        public string? Category { get; set; }

        /// <summary>
        /// 项目来源
        /// </summary>
        [StringLength(100)]
        public string? ProjectSource { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? ProjectRemark { get; set; }
    }
} 