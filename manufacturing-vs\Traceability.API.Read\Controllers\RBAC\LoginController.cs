using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Read.Application.Command.Permission;
using Traceability.API.Read.Application.Command.Role;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Controllers.RBAC
{
    /// <summary>
    /// 登录授权控制器-读
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class LoginController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public LoginController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 登录
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<APIResult<LoginUserDto>> Login([FromQuery] LoginUserCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 刷新Token
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        public async Task<APIResult<TokenResponseDto>> Refresh([FromBody] RefreshTokenQueryCommand command)
        {
            return await mediator.Send(command);
        }
        /// <summary>
        /// 导出用户信息
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Export()
        {
            ExportCommand command = new ExportCommand();
            var result = await mediator.Send(command);
            if (result.Code == ResultCode.Success)
            {
                return File(
                        result.Data.FileContent,
                        result.Data.ContentType,
                        result.Data.FileName
                    );
            }
            return Ok(result);
        }
    }
}
