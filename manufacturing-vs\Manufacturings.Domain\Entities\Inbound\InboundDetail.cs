using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 入库明细表
    /// </summary>
    public class InboundDetail : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(100)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 品牌
        /// </summary>
        [StringLength(100)]
        public string? Brand { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InboundQuantity { get; set; }

        /// <summary>
        /// 主单位数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MainUnitQuantity { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [StringLength(50)]
        public string? BatchNumber { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 