# 数据库脚本执行指南

## 连接信息
- **服务器**: 39.96.170.21
- **数据库**: ManufacturingModel
- **用户名**: root
- **密码**: Knn123456

## 执行步骤

### 1. 打开数据库管理工具
推荐使用：
- MySQL Workbench
- Navicat
- phpMyAdmin
- 或任何MySQL客户端

### 2. 连接到数据库
使用上述连接信息连接到MySQL数据库

### 3. 执行以下SQL脚本

```sql
-- 检查并创建仓库分类表
CREATE TABLE IF NOT EXISTS WarehouseCategory (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    CategoryName VARCHAR(100) NOT NULL,
    CategoryCode VARCHAR(20) NOT NULL DEFAULT '',
    Description VARCHAR(500) NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    CreateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CreateUser VARCHAR(50) NOT NULL DEFAULT 'system',
    ModificationTime DATETIME NULL,
    ModifierName VARCHAR(50) NULL DEFAULT ''
);

-- 检查并创建存储类型表
CREATE TABLE IF NOT EXISTS StorageType (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    TypeName VARCHAR(100) NOT NULL,
    TypeCode VARCHAR(20) NOT NULL DEFAULT '',
    Description VARCHAR(500) NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    CreateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CreateUser VARCHAR(50) NOT NULL DEFAULT 'system',
    ModificationTime DATETIME NULL,
    ModifierName VARCHAR(50) NULL DEFAULT ''
);

-- 检查并创建仓库结构表
CREATE TABLE IF NOT EXISTS WarehouseStructure (
    Id INT AUTO_INCREMENT PRIMARY KEY,
    StructureName VARCHAR(100) NOT NULL,
    StructureCode VARCHAR(20) NOT NULL DEFAULT '',
    Description VARCHAR(500) NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsEnabled BOOLEAN NOT NULL DEFAULT TRUE,
    IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
    CreateTime DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CreateUser VARCHAR(50) NOT NULL DEFAULT 'system',
    ModificationTime DATETIME NULL,
    ModifierName VARCHAR(50) NULL DEFAULT ''
);

-- 插入仓库分类种子数据
INSERT IGNORE INTO WarehouseCategory (Id, CategoryName, CategoryCode, Description, CreateUser) VALUES
(1, '原材料仓库', 'RAW', '存放生产原材料', 'system'),
(2, '成品仓库', 'FINISHED', '存放成品', 'system'),
(3, '半成品仓库', 'SEMI', '存放半成品', 'system'),
(4, '工具仓库', 'TOOL', '存放工具设备', 'system'),
(5, '备件仓库', 'SPARE', '存放备用零件', 'system');

-- 插入存储类型种子数据
INSERT IGNORE INTO StorageType (Id, TypeName, TypeCode, Description, CreateUser) VALUES
(1, '常温存储', 'NORMAL', '常温环境存储', 'system'),
(2, '冷藏存储', 'COLD', '2-8℃冷藏存储', 'system'),
(3, '冷冻存储', 'FROZEN', '-18℃以下冷冻存储', 'system'),
(4, '恒温存储', 'CONSTANT', '恒定温度存储', 'system'),
(5, '防潮存储', 'MOISTURE', '防潮环境存储', 'system');

-- 插入仓库结构种子数据
INSERT IGNORE INTO WarehouseStructure (Id, StructureName, StructureCode, Description, CreateUser) VALUES
(1, '平面仓库', 'FLAT', '平面布局仓库', 'system'),
(2, '立体仓库', 'VERTICAL', '立体货架仓库', 'system'),
(3, '货架仓库', 'RACK', '货架式仓库', 'system'),
(4, '自动化仓库', 'AUTO', '自动化立体仓库', 'system'),
(5, '露天仓库', 'OUTDOOR', '露天存储区域', 'system');

-- 验证数据插入
SELECT 'WarehouseCategory' as TableName, COUNT(*) as RecordCount FROM WarehouseCategory WHERE IsDeleted = FALSE
UNION ALL
SELECT 'StorageType' as TableName, COUNT(*) as RecordCount FROM StorageType WHERE IsDeleted = FALSE
UNION ALL
SELECT 'WarehouseStructure' as TableName, COUNT(*) as RecordCount FROM WarehouseStructure WHERE IsDeleted = FALSE;
```

### 4. 验证执行结果
执行完成后，您应该看到类似以下的结果：
```
TableName           RecordCount
WarehouseCategory   5
StorageType         5
WarehouseStructure  5
```

## ✅ 完成标志
如果看到上述结果，说明数据库脚本执行成功！
