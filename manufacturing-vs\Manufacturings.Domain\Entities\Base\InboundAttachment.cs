using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用入库附件表
    /// </summary>
    public class InboundAttachment : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public long InboundRecordId { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        [Required]
        public InboundType InboundType { get; set; }

        /// <summary>
        /// 附件名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AttachmentName { get; set; } = string.Empty;

        /// <summary>
        /// 附件类型
        /// </summary>
        [StringLength(50)]
        public string? AttachmentType { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件扩展名
        /// </summary>
        [StringLength(20)]
        public string? FileExtension { get; set; }

        /// <summary>
        /// 附件描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 上传人ID
        /// </summary>
        [Required]
        public long UploadedBy { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [Required]
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
} 