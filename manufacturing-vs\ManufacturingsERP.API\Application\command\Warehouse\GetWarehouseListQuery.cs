using MediatR;
using Manufacturings.Infrastructrue.Error;
using ManufacturingsERP.API.Application.DTOs;

namespace ManufacturingsERP.API.Application.command.Warehouse
{
    /// <summary>
    /// 获取仓库列表查询
    /// </summary>
    public class GetWarehouseListQuery : IRequest<APIResult<APIPageing<WarehouseDto>>>
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? Keyword { get; set; }

    }
} 