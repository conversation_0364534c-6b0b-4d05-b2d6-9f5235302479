﻿using MediatR;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.Permission
{
    public class UpdatePermissionCommand : IRequest<APIResult<object>>
    {
        public long Id { get; set; }
        /// <summary>
        /// 权限名称
        /// </summary>
        public string PermissionName { get; set; }

        /// <summary>
        /// 权限URL
        /// </summary>
        public string PermissionUrl { get; set; }

        /// <summary>
        /// 权限序号
        /// </summary>
        public int OrderNo { get; set; }

        /// <summary>
        /// 父级编号
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public long CreateId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
