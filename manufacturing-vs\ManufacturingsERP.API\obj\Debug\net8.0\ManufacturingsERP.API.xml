<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ManufacturingsERP.API</name>
    </assembly>
    <members>
        <member name="T:ManufacturingsERP.API.Application.command.Warehouse.GetWarehouseListQuery">
            <summary>
            获取仓库列表查询
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.command.Warehouse.GetWarehouseListQuery.Page">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.command.Warehouse.GetWarehouseListQuery.PageSize">
            <summary>
            每页大小
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.command.Warehouse.GetWarehouseListQuery.Keyword">
            <summary>
            搜索关键词
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.WarehouseDto">
            <summary>
            仓库DTO
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.Id">
            <summary>
            仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.WarehouseNumber">
            <summary>
            仓库编号
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.WarehouseName">
            <summary>
            仓库名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.ParentId">
            <summary>
            上级仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.ParentName">
            <summary>
            上级仓库名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.CategoryId">
            <summary>
            仓库分类ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.CategoryName">
            <summary>
            仓库分类名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.StorageTypeId">
            <summary>
            存储类型ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.StorageTypeName">
            <summary>
            存储类型名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.StructureId">
            <summary>
            仓库结构ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.StructureName">
            <summary>
            仓库结构名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.PersonInChargeId">
            <summary>
            负责人ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.PersonInChargeName">
            <summary>
            负责人姓名
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.Address">
            <summary>
            仓库地址
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.IsSystemNumber">
            <summary>
            是否系统编号
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.CreateName">
            <summary>
            创建人
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.ModifyTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.ModifierName">
            <summary>
            修改人
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.Children">
            <summary>
            子仓库列表
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.WarehouseDto.HasChildren">
            <summary>
            是否有子仓库
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.WarehouseDetailDto">
            <summary>
            仓库详情DTO
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand">
            <summary>
            创建仓库命令
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.WarehouseName">
            <summary>
            仓库名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.ParentId">
            <summary>
            上级仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.CategoryId">
            <summary>
            仓库分类ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.StorageTypeId">
            <summary>
            存储类型ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.StructureId">
            <summary>
            仓库结构ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.PersonInChargeId">
            <summary>
            负责人ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.Address">
            <summary>
            仓库地址
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand.IsSystemNumber">
            <summary>
            是否系统编号
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand">
            <summary>
            更新仓库命令
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.Id">
            <summary>
            仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.WarehouseName">
            <summary>
            仓库名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.ParentId">
            <summary>
            上级仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.CategoryId">
            <summary>
            仓库分类ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.StorageTypeId">
            <summary>
            存储类型ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.StructureId">
            <summary>
            仓库结构ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.PersonInChargeId">
            <summary>
            负责人ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.Address">
            <summary>
            仓库地址
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.Remarks">
            <summary>
            备注
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand.IsSystemNumber">
            <summary>
            是否系统编号
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.DeleteWarehouseCommand">
            <summary>
            删除仓库命令
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.DeleteWarehouseCommand.Id">
            <summary>
            仓库ID
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.DTOs.GetWarehouseByIdQuery">
            <summary>
            根据ID获取仓库查询
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.DTOs.GetWarehouseByIdQuery.Id">
            <summary>
            仓库ID
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Handlers.Warehouse.CreateWarehouseCommandHandler">
            <summary>
            创建仓库命令处理器
            </summary>
        </member>
        <member name="M:ManufacturingsERP.API.Application.Handlers.Warehouse.CreateWarehouseCommandHandler.GenerateWarehouseNumber">
            <summary>
            生成仓库编号
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Handlers.Warehouse.DeleteWarehouseCommandHandler">
            <summary>
            删除仓库命令处理器
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Handlers.Warehouse.GetWarehouseByIdQueryHandler">
            <summary>
            根据ID获取仓库详情查询处理器
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Handlers.Warehouse.UpdateWarehouseCommandHandler">
            <summary>
            更新仓库命令处理器
            </summary>
        </member>
        <member name="M:ManufacturingsERP.API.Application.Handlers.Warehouse.UpdateWarehouseCommandHandler.HasCircularReference(System.Int64,System.Int32)">
            <summary>
            检查是否形成循环引用
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Handler.Warehouse.GetWarehouseListHandler">
            <summary>
            获取仓库列表处理器
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Interfaces.IWarehouseRepository">
            <summary>
            仓库仓储接口
            </summary>
        </member>
        <member name="M:ManufacturingsERP.API.Application.Interfaces.IWarehouseRepository.GetByWarehouseNumberAsync(System.String)">
            <summary>
            根据仓库编号获取仓库
            </summary>
            <param name="warehouseNumber">仓库编号</param>
            <returns>仓库实体</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Application.Interfaces.IWarehouseRepository.IsWarehouseNameExistsAsync(System.String,System.Nullable{System.Int64})">
            <summary>
            检查仓库名称是否已存在
            </summary>
            <param name="warehouseName">仓库名称</param>
            <param name="excludeId">排除的仓库ID</param>
            <returns>是否存在</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Application.Interfaces.IWarehouseRepository.GetWarehouseTreeAsync">
            <summary>
            获取仓库树形结构
            </summary>
            <returns>仓库树形结构</returns>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto">
            <summary>
            仓库树形结构DTO
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto.Id">
            <summary>
            仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto.WarehouseNumber">
            <summary>
            仓库编号
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto.WarehouseName">
            <summary>
            仓库名称
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto.ParentId">
            <summary>
            上级仓库ID
            </summary>
        </member>
        <member name="P:ManufacturingsERP.API.Application.Interfaces.WarehouseTreeDto.Children">
            <summary>
            子仓库列表
            </summary>
        </member>
        <member name="T:ManufacturingsERP.API.Application.Repositories.WarehouseRepository">
            <summary>
            仓库仓储实现类
            </summary>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.PersonController.GetPersonList(System.Int32,System.Int32,System.String)">
            <summary>
            获取人员列表
            </summary>
            <param name="pageSize">每页大小</param>
            <param name="page">页码</param>
            <param name="keyword">搜索关键词</param>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.PersonController.GetAllPersons">
            <summary>
            获取所有人员（不分页）
            </summary>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.StorageTypeController.GetStorageTypeList(System.Int32,System.Int32,System.String)">
            <summary>
            获取存储类型列表
            </summary>
            <param name="pageSize">每页大小</param>
            <param name="page">页码</param>
            <param name="keyword">搜索关键词</param>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.StorageTypeController.GetAllStorageTypes">
            <summary>
            获取所有存储类型（不分页）
            </summary>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseCategoryController.GetWarehouseCategoryList(System.Int32,System.Int32,System.String)">
            <summary>
            获取仓库分类列表
            </summary>
            <param name="pageSize">每页大小</param>
            <param name="page">页码</param>
            <param name="keyword">搜索关键词</param>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseCategoryController.GetAllWarehouseCategories">
            <summary>
            获取所有仓库分类（不分页）
            </summary>
            <returns></returns>
        </member>
        <member name="T:ManufacturingsERP.API.Controllers.WarehouseController">
            <summary>
            仓库管理控制器
            </summary>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseController.CreateWarehouse(ManufacturingsERP.API.Application.DTOs.CreateWarehouseCommand)">
            <summary>
            新增仓库
            </summary>
            <param name="command">创建仓库命令</param>
            <returns>仓库ID</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseController.UpdateWarehouse(ManufacturingsERP.API.Application.DTOs.UpdateWarehouseCommand)">
            <summary>
            更新仓库
            </summary>
            <param name="command">更新仓库命令</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseController.DeleteWarehouse(System.Int64)">
            <summary>
            删除仓库
            </summary>
            <param name="id">仓库ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseController.GetWarehouseList(ManufacturingsERP.API.Application.command.Warehouse.GetWarehouseListQuery)">
            <summary>
            获取仓库列表
            </summary>
            <param name="query">查询参数</param>
            <returns>仓库列表</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseController.GetWarehouseById(System.Int64)">
            <summary>
            根据ID获取仓库详情
            </summary>
            <param name="id">仓库ID</param>
            <returns>仓库详情</returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseStructureController.GetWarehouseStructureList(System.Int32,System.Int32,System.String)">
            <summary>
            获取仓库结构列表
            </summary>
            <param name="pageSize">每页大小</param>
            <param name="page">页码</param>
            <param name="keyword">搜索关键词</param>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.Controllers.WarehouseStructureController.GetAllWarehouseStructures">
            <summary>
            获取所有仓库结构（不分页）
            </summary>
            <returns></returns>
        </member>
        <member name="M:ManufacturingsERP.API.ServiceCollectionExtensions.AddDBContextAccessor``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Boolean)">
            <summary>
            注册EFCore上下文
            </summary>
            <typeparam name="T"></typeparam>
            <param name="services"></param>
            <param name="connectionString">连接字符串</param>
            <param name="enablerSqlLog">是否启用SQL日志</param>
            <returns></returns>
        </member>
    </members>
</doc>
