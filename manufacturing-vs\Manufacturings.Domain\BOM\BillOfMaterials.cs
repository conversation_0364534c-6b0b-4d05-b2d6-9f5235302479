using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.BOM
{
    /// <summary>
    /// BOM主表
    /// </summary>
    public class BillOfMaterials : BaseEntity
    {
        /// <summary>
        /// BOM编号
        /// </summary>
        public string? BomNo { get; set; }

        /// <summary>
        /// BOM主题
        /// </summary>
        public string? BomTitle { get; set; }

        /// <summary>
        /// 版本号
        /// </summary>
        public string? Version { get; set; }

        /// <summary>
        /// 成品类型
        /// </summary>
        public string? ProductType { get; set; }

        /// <summary>
        /// 成品外键
        /// </summary>
        public long? ProductId { get; set; }

        /// <summary>
        /// 是否默认BOM
        /// </summary>
        public bool? IsDefaultBOM { get; set; }

        /// <summary>
        /// 日产量
        /// </summary>
        public decimal? DailyOutput { get; set; }

        /// <summary>
        /// 父级编号
        /// </summary>
        public long? ParentID { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? BOMNote { get; set; }
    }
}
