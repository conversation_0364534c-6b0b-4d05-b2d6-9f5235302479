using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售退货记录表
    /// </summary>
    public class SalesReturnRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 入库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 入库主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InboundSubject { get; set; } = string.Empty;

        /// <summary>
        /// 入库日期
        /// </summary>
        [Required]
        public DateTime InboundDate { get; set; }

        /// <summary>
        /// 入库类型（退货入库、其他入库等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string InboundType { get; set; } = string.Empty;

        /// <summary>
        /// 入库数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InboundQuantity { get; set; }

        /// <summary>
        /// 入库人员ID
        /// </summary>
        public long? InboundPersonId { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        public long? WarehouseId { get; set; }

        /// <summary>
        /// 退货原因
        /// </summary>
        [StringLength(500)]
        public string? ReturnReason { get; set; }

        /// <summary>
        /// 退货状态（待处理、已入库、已处理等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReturnStatus { get; set; } = "待处理";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 