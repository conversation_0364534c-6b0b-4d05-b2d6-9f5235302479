using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 角色权限表
    /// </summary>
    [Table("RolePermission")]
    public class RolePermissionModel : BaseEntity
    {
        /// <summary>
        /// 权限Id
        /// </summary>
        [Required]
        public long PermissionId { get; set; }
        
        /// <summary>
        /// 角色Id
        /// </summary>
        [Required]
        public long RoleId { get; set; }
    }
} 