[{"ContainingType": "Traceability.API.Write.Controllers.PermissionController", "Method": "CreatePermission", "RelativePath": "api/Permission/CreatePermission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.CreatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.PermissionController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Permission/Handle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.DelPermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.PermissionController", "Method": "UpdatePermission", "RelativePath": "api/Permission/UpdatePermission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.UpdatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=6.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]