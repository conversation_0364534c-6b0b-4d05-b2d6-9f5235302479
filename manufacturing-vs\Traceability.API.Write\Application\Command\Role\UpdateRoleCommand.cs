﻿using MediatR;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.Role
{
    public class UpdateRoleCommand : IRequest<APIResult<object>>
    {
        public long Id { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; }

        /// <summary>
        /// 角色描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool RoleState { get; set; }
        /// <summary>
        /// 权限Id
        /// </summary>
        public List<int> PermissionId { get; set; }
         /// <summary>
        /// 创建人Id
        /// </summary>
        public long CreateId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }
    }
}
