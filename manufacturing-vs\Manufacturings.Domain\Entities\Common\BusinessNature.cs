using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 营业性质表
    /// </summary>
    public class BusinessNature : BaseEntity
    {
        /// <summary>
        /// 性质名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string NatureName { get; set; } = string.Empty;

        /// <summary>
        /// 性质编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string NatureCode { get; set; } = string.Empty;

        /// <summary>
        /// 性质描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 