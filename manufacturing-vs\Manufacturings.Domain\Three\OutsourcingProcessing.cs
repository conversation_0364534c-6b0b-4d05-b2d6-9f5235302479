using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 委外加工实体
    /// </summary>
    [Table("OutsourcingProcessing")]
    public class OutsourcingProcessing : BaseEntity
    {
        /// <summary>
        /// 生产单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProductionOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 生产主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ProductionTheme { get; set; } = string.Empty;

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// 生产类型
        /// </summary>
        [StringLength(50)]
        public string ProductionType { get; set; } = "委外加工";

        /// <summary>
        /// 关联项目
        /// </summary>
        [StringLength(100)]
        public string RelatedProject { get; set; } = string.Empty;

        /// <summary>
        /// 所在部门
        /// </summary>
        [StringLength(100)]
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 委托单位
        /// </summary>
        [StringLength(200)]
        public string EntrustingUnit { get; set; } = string.Empty;

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string ContactPhone { get; set; } = string.Empty;

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string ContactPerson { get; set; } = string.Empty;

        /// <summary>
        /// 负责人
        /// </summary>
        [StringLength(50)]
        public string ResponsiblePerson { get; set; } = string.Empty;

        /// <summary>
        /// 销售订单
        /// </summary>
        [StringLength(50)]
        public long SalesOrder { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; } = string.Empty;

        /// <summary>
        /// 状态 (未完成/已完成)
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "未完成";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; } = string.Empty;
    }
}

