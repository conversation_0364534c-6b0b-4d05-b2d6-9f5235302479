# 仓库管理数据字典API部署指南

## 📋 概述

本指南将帮助您部署缺少的仓库管理数据字典API，包括：
- 仓库分类API (`/api/WarehouseCategory/list`)
- 存储类型API (`/api/StorageType/list`)
- 仓库结构API (`/api/WarehouseStructure/list`)
- 人员信息API (`/api/Person/list`)

## 🗄️ 数据库部署

### 1. 执行SQL脚本
在您的数据库中执行 `CreateTables.sql` 脚本，这将：
- 创建4个新表：`WarehouseCategory`、`StorageType`、`WarehouseStructure`、`Person`
- 插入种子数据（每个表5条记录）
- 创建必要的索引

### 2. 验证数据
执行以下查询验证数据是否正确插入：
```sql
SELECT * FROM WarehouseCategory;
SELECT * FROM StorageType;
SELECT * FROM WarehouseStructure;
SELECT * FROM Person;
```

## 🔧 后端代码部署

### 1. 添加模型类
将 `Models.cs` 中的模型类添加到您的项目中：
- `WarehouseCategory`
- `StorageType`
- `WarehouseStructure`
- `Person`

### 2. 更新DbContext
在您的 `ApplicationDbContext` 类中添加：
```csharp
public DbSet<WarehouseCategory> WarehouseCategories { get; set; }
public DbSet<StorageType> StorageTypes { get; set; }
public DbSet<WarehouseStructure> WarehouseStructures { get; set; }
public DbSet<Person> Persons { get; set; }
```

### 3. 添加控制器
将以下控制器文件添加到您的 `Controllers` 文件夹：
- `WarehouseCategoryController.cs`
- `StorageTypeController.cs`
- `WarehouseStructureController.cs`
- `PersonController.cs`

### 4. 配置依赖注入
确保在 `Program.cs` 或 `Startup.cs` 中正确配置了数据库上下文：
```csharp
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));
```

## 🚀 部署步骤

### 1. 停止应用程序
```bash
# 停止当前运行的API服务
```

### 2. 更新代码
- 复制所有控制器文件到项目中
- 更新DbContext类
- 添加模型类

### 3. 执行数据库脚本
```sql
-- 在数据库中执行 CreateTables.sql
```

### 4. 重新编译和启动
```bash
dotnet build
dotnet run
```

## 🧪 测试API

部署完成后，测试以下API端点：

### 仓库分类
```
GET http://localhost:5107/api/WarehouseCategory/list?pageSize=10
GET http://localhost:5107/api/WarehouseCategory
```

### 存储类型
```
GET http://localhost:5107/api/StorageType/list?pageSize=10
GET http://localhost:5107/api/StorageType
```

### 仓库结构
```
GET http://localhost:5107/api/WarehouseStructure/list?pageSize=10
GET http://localhost:5107/api/WarehouseStructure
```

### 人员信息
```
GET http://localhost:5107/api/Person/list?pageSize=10
GET http://localhost:5107/api/Person
```

## 📊 预期响应格式

所有API都返回统一格式：
```json
{
  "code": 200,
  "message": "查询成功",
  "token": null,
  "refreshToken": null,
  "data": {
    "totalCount": 5,
    "pageCount": 1,
    "pageData": [
      {
        "id": 1,
        "categoryName": "原材料仓库",
        "description": "存放生产原材料",
        "isEnabled": true,
        "createTime": "2025-01-15T10:00:00",
        "createUser": "system"
      }
    ]
  }
}
```

## 🔍 故障排除

### 1. 数据库连接问题
- 检查连接字符串是否正确
- 确认数据库服务是否运行

### 2. API 404错误
- 确认控制器已正确添加到项目中
- 检查路由配置是否正确

### 3. 数据为空
- 确认种子数据已正确插入
- 检查数据库表是否存在

## ✅ 验证清单

- [ ] 数据库表已创建
- [ ] 种子数据已插入
- [ ] 控制器已添加
- [ ] DbContext已更新
- [ ] 应用程序已重新编译
- [ ] API端点返回正确数据
- [ ] 前端可以正常调用API

完成以上步骤后，前端应该能够正确显示来自数据库的真实数据！
