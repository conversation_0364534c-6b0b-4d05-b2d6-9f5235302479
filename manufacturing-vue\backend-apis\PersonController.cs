using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Infrastructrue.Error;

namespace ManufacturingsERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PersonController : ControllerBase
    {
        private readonly MyDbContext _context;

        public PersonController(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取人员列表
        /// </summary>
        /// <param name="pageSize">每页大小</param>
        /// <param name="page">页码</param>
        /// <param name="keyword">搜索关键词</param>
        /// <returns></returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetPersonList(
            int pageSize = 20,
            int page = 1,
            string keyword = "")
        {
            try
            {
                var query = _context.Persons.AsQueryable();

                // 搜索过滤
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.Name.Contains(keyword) ||
                                           (x.Department != null && x.Department.Contains(keyword)) ||
                                           (x.Position != null && x.Position.Contains(keyword)));
                }

                // 总数统计
                var totalCount = await query.CountAsync();
                var pageCount = (int)Math.Ceiling((double)totalCount / pageSize);

                // 分页查询
                var persons = await query
                    .OrderBy(x => x.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(x => new
                    {
                        x.Id,
                        PersonName = x.Name,
                        x.Department,
                        x.Position,
                        Phone = x.PhoneNumber,
                        x.Email,
                        x.IsEnabled,
                        x.CreateTime,
                        x.CreateUser,
                        x.ModificationTime,
                        x.ModifierName
                    })
                    .ToListAsync();

                return Ok(new
                {
                    code = 200,
                    message = "查询成功",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = new
                    {
                        totalCount,
                        pageCount,
                        pageData = persons
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"查询失败: {ex.Message}",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = (object)null
                });
            }
        }

        /// <summary>
        /// 获取所有人员（不分页）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetAllPersons()
        {
            try
            {
                var persons = await _context.Persons
                    .Where(x => x.IsEnabled)
                    .OrderBy(x => x.Id)
                    .Select(x => new
                    {
                        x.Id,
                        PersonName = x.Name,
                        x.Department,
                        x.Position
                    })
                    .ToListAsync();

                return Ok(new
                {
                    code = 200,
                    message = "查询成功",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = persons
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    code = 500,
                    message = $"查询失败: {ex.Message}",
                    token = (string)null,
                    refreshToken = (string)null,
                    data = (object)null
                });
            }
        }
    }
}
