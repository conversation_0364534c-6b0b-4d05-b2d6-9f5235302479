﻿using AutoMapper;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;

namespace Traceability.API.Read.Common
{
    /// <summary>
    /// AutoMapper映射配置
    /// </summary>
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<UserModel, LoginUserDto>();
            CreateMap<RoleModel, RoleDto>();
            CreateMap<UserModel, UserDto>();



        }
    }
}
