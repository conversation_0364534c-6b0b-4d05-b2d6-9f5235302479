import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
        path: '/',
        name: 'Login',
        component: () => import('@/views/Main/LoginView.vue'),
    },
    {
      path: '/Main',
      name: 'Main',
      component: () => import('@/views/Main/Menu.vue'),
      children:[
         {
           path: '/GetPermission',
           name: 'GetPermission',
           component: () => import('@/views/RBAC/GetPermission.vue'),
        },
        {
           path: '/GetRole',
           name: 'GetRole',
           component: () => import('@/views/RBAC/GetRoleView.vue'),
        },
        {
           path: '/GetUser',
           name: 'GetUser',
           component: () => import('@/views/RBAC/GetUserView.vue'),
        },
        {
           path: '/WarehouseManagement',
           name: 'WarehouseManagement',
           component: () => import('@/views/Warehouse/WarehouseManagement.vue'),
        }
      ]
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    }
  ],
})

export default router
