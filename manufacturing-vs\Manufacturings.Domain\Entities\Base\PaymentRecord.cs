using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 付款记录表（合并客户和供应商付款记录）
    /// </summary>
    public class PaymentRecord : BaseEntity
    {
        /// <summary>
        /// 业务伙伴ID
        /// </summary>
        [Required]
        public long BusinessPartnerId { get; set; }

        /// <summary>
        /// 伙伴类型（Customer/Supplier）
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PartnerType { get; set; } = string.Empty;

        /// <summary>
        /// 付款单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PaymentNumber { get; set; } = string.Empty;

        /// <summary>
        /// 付款日期
        /// </summary>
        [Required]
        public DateTime PaymentDate { get; set; }

        /// <summary>
        /// 付款金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PaymentAmount { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        [StringLength(100)]
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 付款状态
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PaymentStatus { get; set; } = "待付款";

        /// <summary>
        /// 关联订单号
        /// </summary>
        [StringLength(50)]
        public string? RelatedOrderNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 