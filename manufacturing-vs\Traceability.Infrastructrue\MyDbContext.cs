﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Traceability.Domain.RBAC;

namespace Traceability.Infrastructure
{
    public class MyDbContext : DbContext
    {
        public MyDbContext(DbContextOptions options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<UserModel>().HasQueryFilter(x=>!x.IsDeleted);
            modelBuilder.Entity<RoleModel>().HasQueryFilter(x=>!x.IsDeleted);
            modelBuilder.Entity<PermissionModel>().HasQueryFilter(x=>!x.IsDeleted);
            modelBuilder.Entity<RolePermissionModel>().HasQueryFilter(x=>!x.IsDeleted);
            modelBuilder.Entity<UserRoleModel>().HasQueryFilter(x=>!x.IsDeleted);

            
        }
        #region RBAC
        /// <summary>
        /// 用户信息
        /// </summary>
        public DbSet<UserModel> Users { get; set; }
        /// <summary>
        /// 角色信息
        /// </summary>
        public DbSet<RoleModel> Roles { get; set; }
        /// <summary>
        /// 用户角色
        /// </summary>
        public DbSet<UserRoleModel> UserRoles { get; set; }
        /// <summary>
        /// 权限信息
        /// </summary>
        public DbSet<PermissionModel> Permissions { get; set; }
        /// <summary>
        /// 角色权限
        /// </summary>
        public DbSet<RolePermissionModel> RolePermissions { get; set; }
        /// <summary>
        /// 操作记录
        /// </summary>
        public DbSet<OperationRecord> OperationRecords { get; set; }

        #endregion


    }
}
