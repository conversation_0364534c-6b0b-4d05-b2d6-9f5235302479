﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Read.Application.Command.Permission;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.Permission
{
    public class GetAllPermissionCommandHandler : IRequestHandler<GetAllPermissionCommand, APIResult<List<PermissionModel>>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepo;
        private readonly ILogger<GetPermissionHandler> _logger;

        public GetAllPermissionCommandHandler(IBaseRepository<PermissionModel> permissionRepo, ILogger<GetPermissionHandler> logger)
        {
            this.permissionRepo = permissionRepo;
            this._logger = logger;
        }

        public Task<APIResult<List<PermissionModel>>> Handle(GetAllPermissionCommand request, CancellationToken cancellationToken)
        {
            APIResult<List<PermissionModel>> result = new APIResult<List<PermissionModel>>();
            try
            {
                var permission = permissionRepo.GetAll().AsNoTracking();
                result.Code = ResultCode.Success;
                result.Message = "获取权限成功";
                result.Data = permission.ToList();

                _logger.LogInformation($"获取权限成功，返回{result.Data.Count}条记录");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取权限失败：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "获取权限失败";
                return Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
