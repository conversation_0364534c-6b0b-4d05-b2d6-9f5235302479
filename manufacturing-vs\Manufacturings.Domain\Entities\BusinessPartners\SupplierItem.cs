using Manufacturings.Domain.Entities.Inventory;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 供应商物品表
    /// </summary>
    public class SupplierItem : BaseEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public long SupplierId { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(100)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(20)]
        public string? Unit { get; set; }

        /// <summary>
        /// 物品类别ID
        /// </summary>
        public long? ItemCategoryId { get; set; }

        /// <summary>
        /// 物品属性ID
        /// </summary>
        public long? ItemAttributeId { get; set; }

        /// <summary>
        /// 物品类型ID
        /// </summary>
        public long? ItemTypeId { get; set; }
    }
} 