using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 部门表
    /// </summary>
    [Table("Department")]
    public class Department : BaseEntity
    {
        /// <summary>
        /// 部门编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string DepartmentNumber { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DepartmentName { get; set; } = string.Empty;

        /// <summary>
        /// 部门描述
        /// </summary>
        [StringLength(500)]
        public string? DepartmentDescription { get; set; }

        /// <summary>
        /// 上级部门ID
        /// </summary>
        public long? ParentDepartmentId { get; set; }

        /// <summary>
        /// 部门负责人ID
        /// </summary>
        public long? DepartmentManagerId { get; set; }

        /// <summary>
        /// 部门类型
        /// </summary>
        [StringLength(50)]
        public string? DepartmentType { get; set; }

        /// <summary>
        /// 部门级别
        /// </summary>
        public int? DepartmentLevel { get; set; }

        /// <summary>
        /// 排序序号
        /// </summary>
        public int? SortOrder { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统部门
        /// </summary>
        [Required]
        public bool IsSystemDepartment { get; set; } = false;

        /// <summary>
        /// 部门负责人姓名
        /// </summary>
        [StringLength(50)]
        public string? DepartmentManagerName { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 部门地址
        /// </summary>
        [StringLength(500)]
        public string? Address { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 