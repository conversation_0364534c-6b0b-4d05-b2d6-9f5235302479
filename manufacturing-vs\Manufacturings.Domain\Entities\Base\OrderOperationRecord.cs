using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用订单操作记录表
    /// </summary>
    public class OrderOperationRecord : BaseEntity
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        [Required]
        public long OrderId { get; set; }

        /// <summary>
        /// 订单类型（销售订单/采购订单）
        /// </summary>
        [Required]
        public OrderType OrderType { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public OrderOperationType OperationType { get; set; }

        /// <summary>
        /// 操作前状态
        /// </summary>
        [StringLength(50)]
        public string? PreviousStatus { get; set; }

        /// <summary>
        /// 操作后状态
        /// </summary>
        [StringLength(50)]
        public string? CurrentStatus { get; set; }

        /// <summary>
        /// 操作描述
        /// </summary>
        [StringLength(500)]
        public string? OperationDescription { get; set; }

        /// <summary>
        /// 操作备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Required]
        public long OperatedBy { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Required]
        public DateTime OperatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// IP地址
        /// </summary>
        [StringLength(50)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// 订单操作类型枚举
    /// </summary>
    public enum OrderOperationType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Created = 1,
        /// <summary>
        /// 修改
        /// </summary>
        Modified = 2,
        /// <summary>
        /// 审核
        /// </summary>
        Approved = 3,
        /// <summary>
        /// 拒绝
        /// </summary>
        Rejected = 4,
        /// <summary>
        /// 确认
        /// </summary>
        Confirmed = 5,
        /// <summary>
        /// 取消
        /// </summary>
        Cancelled = 6,
        /// <summary>
        /// 完成
        /// </summary>
        Completed = 7,
        /// <summary>
        /// 关闭
        /// </summary>
        Closed = 8
    }
} 