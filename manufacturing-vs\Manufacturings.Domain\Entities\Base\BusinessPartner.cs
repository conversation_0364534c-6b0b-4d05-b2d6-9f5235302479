using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 业务伙伴基础表（客户和供应商的公共字段）
    /// </summary>
    public abstract class BusinessPartner : BaseEntity
    {
        /// <summary>
        /// 伙伴编号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PartnerNumber { get; set; } = string.Empty;

        /// <summary>
        /// 伙伴名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string PartnerName { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        [StringLength(200)]
        public string? CompanyName { get; set; }

        /// <summary>
        /// 伙伴等级ID
        /// </summary>
        public long? PartnerLevelId { get; set; }

        /// <summary>
        /// 伙伴类别ID
        /// </summary>
        public long? PartnerCategoryId { get; set; }

        /// <summary>
        /// 人员规模ID
        /// </summary>
        public long? PersonnelScaleId { get; set; }

        /// <summary>
        /// 公司地址ID
        /// </summary>
        public long? CompanyAddressId { get; set; }

        /// <summary>
        /// 注册地址
        /// </summary>
        [StringLength(200)]
        public string? RegisteredAddress { get; set; }

        /// <summary>
        /// 统一信用代码
        /// </summary>
        [StringLength(50)]
        public string? UnifiedSocialCreditCode { get; set; }

        /// <summary>
        /// 营业性质ID
        /// </summary>
        [Required]
        public long BusinessNatureId { get; set; }

        /// <summary>
        /// 所属行业ID
        /// </summary>
        public long? IndustryId { get; set; }

        /// <summary>
        /// 注册资本
        /// </summary>
        [StringLength(50)]
        public string? RegisteredCapital { get; set; }

        /// <summary>
        /// 所在地区ID
        /// </summary>
        public long? LocationId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 状态（启用/禁用）
        /// </summary>
        [Required]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 创建人ID
        /// </summary>
        [Required]
        public long CreatedBy { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改人ID
        /// </summary>
        public long? LastModifiedBy { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? LastModifiedAt { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
} 