using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单检验记录表
    /// </summary>
    public class PurchaseOrderInspectionRecord : BaseEntity
    {
        /// <summary>
        /// 采购订单ID
        /// </summary>
        [Required]
        public int PurchaseOrderId { get; set; }

        /// <summary>
        /// 检验单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 检验单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InspectionOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 检验日期
        /// </summary>
        [Required]
        public DateTime InspectionDate { get; set; }

        /// <summary>
        /// 检验人ID
        /// </summary>
        [Required]
        public int InspectorId { get; set; }

        /// <summary>
        /// 物品概要
        /// </summary>
        [StringLength(500)]
        public string? ItemSummary { get; set; }

        /// <summary>
        /// 检验结果（合格、不合格、部分合格等）
        /// </summary>
        [StringLength(50)]
        public string? InspectionResult { get; set; }

        /// <summary>
        /// 检验状态（待检验、检验中、已完成等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionStatus { get; set; } = "待检验";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }
    }
} 