﻿using MediatR;
using Traceability.API.Read.Application.Command.Permission;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.Permission
{
    public class GetMenuPermissionCommandHandler : IRequestHandler<GetMenuPermissionCommand, APIResult<List<CascadeItem>>>
    {
        private readonly IBaseRepository<PermissionModel> _permissionRepository;
        private readonly IBaseRepository<RolePermissionModel> _rolePermissionRepository;
        public GetMenuPermissionCommandHandler(IBaseRepository<PermissionModel> permissionRepository, IBaseRepository<RolePermissionModel> rolePermissionRepository)
        {
            _permissionRepository = permissionRepository;
            _rolePermissionRepository = rolePermissionRepository;
        }

        public Task<APIResult<List<CascadeItem>>> Handle(GetMenuPermissionCommand request, CancellationToken cancellationToken)
        {
            APIResult<List<CascadeItem>> result = new APIResult<List<CascadeItem>>();   
            result.Code = ResultCode.Success;
            result.Message = "查询成功";
            //如果角色ID为空就加载全部
            if (request.RoleId ==null)
            {
                var permission = _permissionRepository.GetAll().ToList();
                result.Data = GetCascade(0, permission);
                return Task.FromResult(result);
            }
            //否则就按照登录人的角色加载
            List<string> roleId = request.RoleId.Split(',').ToList();
            var list = _rolePermissionRepository.GetAll().Where(x=>roleId.Contains(x.RoleId.ToString())).Select(x=>x.PermissionId);
            var permissions = _permissionRepository.GetAll().Where(x => list.Contains(x.Id)).ToList();

            result.Data = GetCascade(0,permissions);
            return Task.FromResult(result);
        }
        /// <summary>
        /// 递归获取菜单
        /// </summary>
        /// <param name="parentId">父级编号</param>
        /// <param name="permissions">权限信息</param>
        /// <returns>返回级联程序集合</returns>
        public List<CascadeItem> GetCascade(long parentId,List<PermissionModel> permissions)
        {
            var result = permissions.Where(x => x.ParentId == parentId);

            List<CascadeItem> cascade = new List<CascadeItem>();
            foreach (var item in result)
            {
                cascade.Add(new CascadeItem()
                {
                    Value = item.Id,
                    Label = item.PermissionName,
                    Url = item.PermissionUrl,
                    Children = GetCascade(item.Id, permissions)
                });
            }
            return cascade;
        }
    }
}
