﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.User
{
    public class CreateUserCommand:IRequest<APIResult<object>>
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        public bool UserState { get; set; }

        public List<int> RoleId { get; set; }
    }
}
