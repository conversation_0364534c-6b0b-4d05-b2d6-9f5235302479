using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 采购入库记录表
    /// </summary>
    public class PurchaseInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [StringLength(50)]
        public string? PurchaseOrderNumber { get; set; }

        /// <summary>
        /// 采购日期
        /// </summary>
        public DateTime? PurchaseDate { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public int? SupplierId { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 采购人员ID
        /// </summary>
        public int? PurchasingPersonnelId { get; set; }

        /// <summary>
        /// 采购部门ID
        /// </summary>
        public int? PurchasingDepartmentId { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(100)]
        public string? ContactMethod { get; set; }
    }
} 