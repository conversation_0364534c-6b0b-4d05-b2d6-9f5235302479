﻿using MediatR;
using System.ComponentModel.DataAnnotations;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Application.Command.Permission
{
    public class CreatePermissionCommand : IRequest<APIResult<object>>
    {
        /// <summary>
        /// 权限名称
        /// </summary>
        public string PermissionName { get; set; }

        /// <summary>
        /// 权限URL
        /// </summary>
        public string PermissionUrl { get; set; }

        /// <summary>
        /// 权限序号
        /// </summary>
        public int OrderNo { get; set; }

        /// <summary>
        /// 父级编号
        /// </summary>
        public int? ParentId { get; set; }

    }
}
