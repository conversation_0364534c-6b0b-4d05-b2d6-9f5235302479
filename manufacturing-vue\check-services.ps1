# 检查服务状态脚本
Write-Host "检查服务状态..." -ForegroundColor Green

# 检查端口占用情况
Write-Host "`n=== 端口占用检查 ===" -ForegroundColor Yellow
$ports = @(5107, 5062, 5251, 5142, 5176, 5177)

foreach ($port in $ports) {
    $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "✅ 端口 $port 正在使用 (PID: $($connection.OwningProcess))" -ForegroundColor Green
    } else {
        Write-Host "❌ 端口 $port 未使用" -ForegroundColor Red
    }
}

# 检查进程
Write-Host "`n=== 进程检查 ===" -ForegroundColor Yellow
$processes = Get-Process | Where-Object {$_.ProcessName -like "*dotnet*" -or $_.ProcessName -like "*node*" -or $_.ProcessName -like "*ManufacturingsERP*"}

if ($processes) {
    Write-Host "找到相关进程:" -ForegroundColor Green
    $processes | Select-Object ProcessName, Id, CPU | Format-Table
} else {
    Write-Host "❌ 未找到相关进程" -ForegroundColor Red
}

# 测试API连接
Write-Host "`n=== API连接测试 ===" -ForegroundColor Yellow

$apiTests = @(
    @{Name="仓库管理API"; Url="http://localhost:5107/api/Warehouse/list?page=1&pageSize=1"},
    @{Name="前端开发服务器"; Url="http://localhost:5177"},
    @{Name="仓库分类API"; Url="http://localhost:5107/api/WarehouseCategory/list"}
)

foreach ($test in $apiTests) {
    try {
        $response = Invoke-WebRequest -Uri $test.Url -Method GET -TimeoutSec 5 -ErrorAction Stop
        Write-Host "✅ $($test.Name): $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "❌ $($test.Name): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 建议操作 ===" -ForegroundColor Cyan
Write-Host "1. 如果5107端口未使用，请启动后端服务" -ForegroundColor White
Write-Host "2. 如果5177端口未使用，请启动前端开发服务器" -ForegroundColor White
Write-Host "3. 访问 http://localhost:5177/api-test.html 测试API" -ForegroundColor White
Write-Host "4. 如果API测试成功，刷新仓库管理页面查看数据" -ForegroundColor White
