{"name": "work", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite --mode test", "test-build": "vite build --mode test", "build": "vite build --mode production", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "bootstrap": "^3.4.1", "element-china-area-data": "^6.1.0", "element-plus": "^2.10.2", "moment": "^2.30.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}