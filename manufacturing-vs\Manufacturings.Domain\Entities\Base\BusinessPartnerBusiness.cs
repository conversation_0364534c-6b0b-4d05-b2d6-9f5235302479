using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用业务伙伴业务信息表
    /// </summary>
    public class BusinessPartnerBusiness : BaseEntity
    {
        /// <summary>
        /// 业务伙伴ID
        /// </summary>
        [Required]
        public long BusinessPartnerId { get; set; }

        /// <summary>
        /// 业务伙伴类型（客户/供应商）
        /// </summary>
        [Required]
        public BusinessPartnerType BusinessPartnerType { get; set; }

        /// <summary>
        /// 主要联系人
        /// </summary>
        [StringLength(50)]
        public string? PrimaryContact { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        [StringLength(100)]
        public string? ContactEmail { get; set; }

        /// <summary>
        /// 传真号码
        /// </summary>
        [StringLength(20)]
        public string? FaxNumber { get; set; }

        /// <summary>
        /// 网站地址
        /// </summary>
        [StringLength(200)]
        public string? Website { get; set; }

        /// <summary>
        /// 业务范围
        /// </summary>
        [StringLength(500)]
        public string? BusinessScope { get; set; }

        /// <summary>
        /// 主要产品/服务
        /// </summary>
        [StringLength(500)]
        public string? MainProducts { get; set; }

        /// <summary>
        /// 年营业额
        /// </summary>
        [StringLength(50)]
        public string? AnnualRevenue { get; set; }

        /// <summary>
        /// 员工数量
        /// </summary>
        public int? EmployeeCount { get; set; }

        /// <summary>
        /// 成立时间
        /// </summary>
        public DateTime? FoundedDate { get; set; }

        /// <summary>
        /// 法定代表人
        /// </summary>
        [StringLength(50)]
        public string? LegalRepresentative { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 