using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 业务伙伴联系人表（合并客户和供应商联系人）
    /// </summary>
    public class BusinessPartnerContact : BaseEntity
    {
        /// <summary>
        /// 业务伙伴ID
        /// </summary>
        [Required]
        public long BusinessPartnerId { get; set; }

        /// <summary>
        /// 伙伴类型（Customer/Supplier）
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PartnerType { get; set; } = string.Empty;

        /// <summary>
        /// 联系人姓名
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ContactName { get; set; } = string.Empty;

        /// <summary>
        /// 职位
        /// </summary>
        [StringLength(100)]
        public string? Position { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        [StringLength(20)]
        public string? MobileNumber { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        [StringLength(100)]
        [EmailAddress]
        public string? EmailAddress { get; set; }

        /// <summary>
        /// 传真号码
        /// </summary>
        [StringLength(20)]
        public string? FaxNumber { get; set; }

        /// <summary>
        /// 联系地址ID
        /// </summary>
        public long? ContactAddressId { get; set; }

        /// <summary>
        /// 是否主要联系人
        /// </summary>
        public bool IsPrimaryContact { get; set; } = false;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 