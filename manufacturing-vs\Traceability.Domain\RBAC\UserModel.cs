using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 用户表
    /// </summary>
    [Table("User")]
    public class UserModel : BaseEntity
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [StringLength(100)]
        public string Name { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool UserState { get; set; }

    }
} 