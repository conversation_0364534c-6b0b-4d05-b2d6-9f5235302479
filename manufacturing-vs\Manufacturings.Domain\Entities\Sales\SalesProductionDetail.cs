using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售生产明细表
    /// </summary>
    public class SalesProductionDetail : BaseEntity
    {
        /// <summary>
        /// 销售生产记录ID
        /// </summary>
        [Required]
        public long SalesProductionRecordId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 计划数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PlannedQuantity { get; set; }

        /// <summary>
        /// 实际数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ActualQuantity { get; set; }

        /// <summary>
        /// 完成率
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 生产状态（未开始、进行中、已完成、已暂停等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProductionStatus { get; set; } = "未开始";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 