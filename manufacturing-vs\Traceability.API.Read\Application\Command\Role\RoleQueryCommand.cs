﻿using MediatR;
using Traceability.API.Read.Dto.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Application.Command.Role
{
    public class RoleQueryCommand:IRequest<APIResult<APIPageing<RoleDto>>>
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        public string? RoleName { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页容量
        /// </summary>
        public int PageSize { get; set; }
    }
}
