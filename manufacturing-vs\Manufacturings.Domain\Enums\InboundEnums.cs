namespace Manufacturings.Domain.Enums
{
    /// <summary>
    /// 入库类型枚举
    /// </summary>
    public enum InboundType
    {
        /// <summary>
        /// 采购入库
        /// </summary>
        PurchaseInbound = 1,

        /// <summary>
        /// 销售退货入库
        /// </summary>
        SalesReturnInbound = 2,

        /// <summary>
        /// 生产入库
        /// </summary>
        ProductionInbound = 3,

        /// <summary>
        /// 委外加工入库
        /// </summary>
        OutsourcedProcessingInbound = 4,

        /// <summary>
        /// 委外工单入库
        /// </summary>
        OutsourcedWorkOrderInbound = 5,

        /// <summary>
        /// 生产退料入库
        /// </summary>
        ProductionReturnMaterialInbound = 6,

        /// <summary>
        /// 其他入库
        /// </summary>
        OtherInbound = 7
    }

    /// <summary>
    /// 入库状态枚举
    /// </summary>
    public enum InboundStatus
    {
        /// <summary>
        /// 草稿
        /// </summary>
        Draft = 1,

        /// <summary>
        /// 待审核
        /// </summary>
        PendingApproval = 2,

        /// <summary>
        /// 已审核
        /// </summary>
        Approved = 3,

        /// <summary>
        /// 已入库
        /// </summary>
        Inbound = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5,

        /// <summary>
        /// 已拒绝
        /// </summary>
        Rejected = 6
    }

    /// <summary>
    /// 入库操作类型枚举
    /// </summary>
    public enum InboundOperationType
    {
        /// <summary>
        /// 新增
        /// </summary>
        Create = 1,

        /// <summary>
        /// 修改
        /// </summary>
        Update = 2,

        /// <summary>
        /// 删除
        /// </summary>
        Delete = 3,

        /// <summary>
        /// 提交审核
        /// </summary>
        Submit = 4,

        /// <summary>
        /// 审核通过
        /// </summary>
        Approve = 5,

        /// <summary>
        /// 审核拒绝
        /// </summary>
        Reject = 6,

        /// <summary>
        /// 确认入库
        /// </summary>
        Confirm = 7,

        /// <summary>
        /// 取消
        /// </summary>
        Cancel = 8,

        /// <summary>
        /// 上传文件
        /// </summary>
        UploadFile = 9,

        /// <summary>
        /// 删除文件
        /// </summary>
        DeleteFile = 10,

        /// <summary>
        /// 打印
        /// </summary>
        Print = 11
    }

    /// <summary>
    /// 质检状态枚举
    /// </summary>
    public enum QualityInspectionStatus
    {
        /// <summary>
        /// 待质检
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 质检中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 质检通过
        /// </summary>
        Passed = 3,

        /// <summary>
        /// 质检不通过
        /// </summary>
        Failed = 4,

        /// <summary>
        /// 免检
        /// </summary>
        Exempt = 5
    }

    /// <summary>
    /// 质量评级枚举
    /// </summary>
    public enum QualityRating
    {
        /// <summary>
        /// 优秀
        /// </summary>
        Excellent = 1,

        /// <summary>
        /// 良好
        /// </summary>
        Good = 2,

        /// <summary>
        /// 合格
        /// </summary>
        Qualified = 3,

        /// <summary>
        /// 不合格
        /// </summary>
        Unqualified = 4
    }

    /// <summary>
    /// 工单状态枚举
    /// </summary>
    public enum WorkOrderStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        Pending = 1,

        /// <summary>
        /// 处理中
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4,

        /// <summary>
        /// 已暂停
        /// </summary>
        Suspended = 5
    }

    /// <summary>
    /// 物料退料类型枚举
    /// </summary>
    public enum MaterialReturnType
    {
        /// <summary>
        /// 质量不合格
        /// </summary>
        QualityIssue = 1,

        /// <summary>
        /// 规格不符
        /// </summary>
        SpecificationMismatch = 2,

        /// <summary>
        /// 数量超发
        /// </summary>
        OverIssued = 3,

        /// <summary>
        /// 生产计划变更
        /// </summary>
        ProductionPlanChange = 4,

        /// <summary>
        /// 其他原因
        /// </summary>
        Other = 5
    }
} 