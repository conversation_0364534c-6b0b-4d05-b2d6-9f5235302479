using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Inventory
{
    /// <summary>
    /// 物品类型表
    /// </summary>
    public class ItemType : BaseEntity
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 类型编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 类型描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 