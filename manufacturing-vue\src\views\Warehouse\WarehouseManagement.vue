<template>
  <div class="warehouse-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>仓库管理</h2>
    </div>

    <!-- 调试信息 -->
    <div v-if="debugInfo.apiStatus !== '所有数据加载完成'" class="debug-info"
      style="background: #f0f9ff; border: 1px solid #0ea5e9; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
      <div style="color: #0369a1; font-weight: bold;">🔧 调试信息</div>
      <div>状态: {{ debugInfo.apiStatus }}</div>
      <div v-if="debugInfo.errorMessage">错误: {{ debugInfo.errorMessage }}</div>
      <div>数据字典大小: 分类({{ categoryMap.size }}) 类型({{ storageTypeMap.size }}) 结构({{ structureMap.size }}) 人员({{
        personMap.size }})</div>
    </div>

    <!-- 操作栏 -->
    <div class="operation-bar">
      <div class="left-operations">
        <el-button type="primary" @click="handleAdd">
          <el-icon>
            <Plus />
          </el-icon>
          新增
        </el-button>
        <el-button @click="handleExport">
          <el-icon>
            <Download />
          </el-icon>
          导出
        </el-button>
        <el-button type="warning" @click="testAllPorts">
          测试端口
        </el-button>
      </div>
      <div class="right-operations">
        <el-input v-model="searchKeyword" placeholder="请搜索仓库名称、编号或地址" class="search-input" clearable
          @input="handleSearch">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
        <el-button @click="toggleViewMode" class="view-toggle">
          <el-icon>
            <Grid v-if="viewMode === 'table'" />
            <List v-else />
          </el-icon>
        </el-button>
      </div>
    </div>



    <!-- 仓库列表 -->
    <div class="warehouse-list">
      <el-table v-loading="loading" :data="warehouseList" row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" class="warehouse-table">
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" width="80">
          <template #default="{ $index }">
            {{ serialNumber($index) }}
          </template>
        </el-table-column>
        <el-table-column prop="warehouseName" label="仓库名称" min-width="200">
          <template #default="{ row }">
            <div class="warehouse-name-cell">
              <el-icon v-if="row.children && row.children.length > 0">
                <Folder v-if="row.expanded" />
                <FolderOpened v-else />
              </el-icon>
              <el-icon v-else>
                <Document />
              </el-icon>
              <span>{{ row.warehouseName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="warehouseNumber" label="仓库编号" width="150" />
        <el-table-column prop="categoryName" label="仓库分类" width="120" />
        <el-table-column prop="storageTypeName" label="存储类型" width="120" />
        <el-table-column prop="structureName" label="仓库结构" width="120" />
        <el-table-column prop="isEnabled" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isEnabled ? 'success' : 'danger'" size="small">
              <el-icon v-if="row.isEnabled">
                <Check />
              </el-icon>
              <el-icon v-else>
                <Close />
              </el-icon>
              {{ row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="personInChargeName" label="负责人" width="100" />
        <el-table-column prop="address" label="仓库地址" min-width="200" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button>
            <el-button type="info" link @click="handleView(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <!-- 新增/编辑对话框 -->
    <WarehouseDialog v-model:visible="dialogVisible" :warehouse="currentWarehouse" :mode="dialogMode"
      @success="handleDialogSuccess" />

    <!-- 详情对话框 -->
    <WarehouseDetailDialog v-model:visible="detailDialogVisible" :warehouse="currentWarehouse"
      @edit="handleEditFromDetail" @delete="handleDeleteFromDetail" />

    <!-- 根节点编辑对话框 -->
    <RootNodeDialog v-model:visible="rootNodeDialogVisible" :warehouse="currentWarehouse"
      @success="handleRootNodeSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Search, Grid, List, Folder, FolderOpened, Document, Check, Close } from '@element-plus/icons-vue'
import WarehouseDialog from './components/WarehouseDialog.vue'
import WarehouseDetailDialog from './components/WarehouseDetailDialog.vue'
import RootNodeDialog from './components/RootNodeDialog.vue'
import {
  getWarehouseList,
  deleteWarehouse,
  getAllWarehouseCategories,
  getAllStorageTypes,
  getAllWarehouseStructures,
  getAllPersons
} from '@/Https/server'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const viewMode = ref('table')
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const rootNodeDialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const currentWarehouse = ref<any>(null)

// 数据字典
const categoryMap = ref<Map<number, string>>(new Map())
const storageTypeMap = ref<Map<number, string>>(new Map())
const structureMap = ref<Map<number, string>>(new Map())
const personMap = ref<Map<number, string>>(new Map())
const warehouseList = ref<any[]>([])



// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const serialNumber = computed(() => {
  return (index: number) => (pagination.currentPage - 1) * pagination.pageSize + index + 1
})

// 方法


// 测试数据字典API
const testDataDictionaryAPIs = async () => {
  const testUrls = [
    // 仓库分类
    'http://localhost:5107/api/WarehouseCategory/list',
    'http://localhost:5107/api/WarehouseCategory',
    'http://localhost:5107/api/Category/list',
    'http://localhost:5107/api/Category',
    // 存储类型
    'http://localhost:5107/api/StorageType/list',
    'http://localhost:5107/api/StorageType',
    // 仓库结构
    'http://localhost:5107/api/WarehouseStructure/list',
    'http://localhost:5107/api/WarehouseStructure',
    'http://localhost:5107/api/Structure/list',
    // 人员
    'http://localhost:5107/api/Person/list',
    'http://localhost:5107/api/Person',
    'http://localhost:5107/api/User/list',
    'http://localhost:5107/api/Employee/list'
  ]

  console.log('开始测试数据字典API端点...')

  for (const url of testUrls) {
    try {
      console.log(`测试: ${url}`)
      const response = await fetch(url + (url.includes('?') ? '&' : '?') + 'pageSize=10', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })
      console.log(`${url} - 状态: ${response.status}`)

      if (response.ok) {
        const data = await response.json()
        console.log(`✅ ${url} - 成功响应:`, data)
        // 如果找到有效数据，标记为可用
        if (data.code === 200 && data.data && (Array.isArray(data.data) ? data.data.length > 0 : data.data.pageData?.length > 0)) {
          console.log(`🎯 找到可用API: ${url}`)
        }
      } else {
        console.log(`❌ ${url} - 错误: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      console.log(`❌ ${url} - 网络错误:`, error)
    }
  }
}

// 初始化基础数据字典（基于常见的业务数据）
const initializeBasicDataDictionaries = () => {
  // 仓库分类映射
  categoryMap.value.set(1, '原材料仓库')
  categoryMap.value.set(2, '成品仓库')
  categoryMap.value.set(3, '半成品仓库')
  categoryMap.value.set(4, '工具仓库')
  categoryMap.value.set(5, '备件仓库')

  // 存储类型映射
  storageTypeMap.value.set(1, '常温存储')
  storageTypeMap.value.set(2, '冷藏存储')
  storageTypeMap.value.set(3, '冷冻存储')
  storageTypeMap.value.set(4, '恒温存储')
  storageTypeMap.value.set(5, '防潮存储')

  // 仓库结构映射
  structureMap.value.set(1, '平面仓库')
  structureMap.value.set(2, '立体仓库')
  structureMap.value.set(3, '货架仓库')
  structureMap.value.set(4, '自动化仓库')
  structureMap.value.set(5, '露天仓库')

  // 人员映射
  personMap.value.set(1, '张三')
  personMap.value.set(2, '李四')
  personMap.value.set(3, '王五')
  personMap.value.set(4, '赵六')
  personMap.value.set(5, '钱七')

  console.log('基础数据字典初始化完成:', {
    categories: categoryMap.value.size,
    storageTypes: storageTypeMap.value.size,
    structures: structureMap.value.size,
    persons: personMap.value.size
  })
}

// 加载数据字典
const loadDataDictionaries = async () => {
  try {
    console.log('开始加载数据字典...')

    // 先测试API是否可用 - 暂时禁用测试，直接加载数据
    // await testDataDictionaryAPIs()

    try {
      // 尝试从API加载数据字典 - 使用不分页的API
      console.log('开始加载数据字典...')
      const [categoryRes, storageTypeRes, structureRes, personRes] = await Promise.all([
        getAllWarehouseCategories(),
        getAllStorageTypes(),
        getAllWarehouseStructures(),
        getAllPersons()
      ])
      console.log('数据字典API响应:', { categoryRes, storageTypeRes, structureRes, personRes })

      let hasApiData = false

      // 构建分类映射 - 由于axios拦截器，直接使用response
      if ((categoryRes as any).code === 200) {
        const categories = (categoryRes as any).data || []
        console.log('仓库分类数据:', categories)
        if (categories.length > 0) {
          categoryMap.value.clear()
          categories.forEach((item: any) => {
            categoryMap.value.set(item.id, item.categoryName || item.name)
          })
          hasApiData = true
          console.log('仓库分类映射构建完成:', categoryMap.value)
        }
      }

      // 构建存储类型映射 - 由于axios拦截器，直接使用response
      if ((storageTypeRes as any).code === 200) {
        const storageTypes = (storageTypeRes as any).data || []
        console.log('存储类型数据:', storageTypes)
        if (storageTypes.length > 0) {
          storageTypeMap.value.clear()
          storageTypes.forEach((item: any) => {
            storageTypeMap.value.set(item.id, item.typeName || item.name)
          })
          hasApiData = true
          console.log('存储类型映射构建完成:', storageTypeMap.value)
        }
      }

      // 构建仓库结构映射 - 由于axios拦截器，直接使用response
      if ((structureRes as any).code === 200) {
        const structures = (structureRes as any).data || []
        console.log('仓库结构数据:', structures)
        if (structures.length > 0) {
          structureMap.value.clear()
          structures.forEach((item: any) => {
            structureMap.value.set(item.id, item.structureName || item.name)
          })
          hasApiData = true
          console.log('仓库结构映射构建完成:', structureMap.value)
        }
      }

      // 构建人员映射 - 由于axios拦截器，直接使用response
      if ((personRes as any).code === 200) {
        const persons = (personRes as any).data || []
        console.log('人员数据:', persons)
        if (persons.length > 0) {
          personMap.value.clear()
          persons.forEach((item: any) => {
            personMap.value.set(item.id, item.personName || item.name || item.userName)
          })
          hasApiData = true
          console.log('人员映射构建完成:', personMap.value)
        }
      }

      if (!hasApiData) {
        console.log('API数据为空，使用基础数据字典')
        initializeBasicDataDictionaries()
      } else {
        console.log('API数据字典加载完成:', {
          categories: categoryMap.value.size,
          storageTypes: storageTypeMap.value.size,
          structures: structureMap.value.size,
          persons: personMap.value.size
        })

        // 数据字典加载完成后，如果已经有仓库数据，重新映射关联字段
        if (warehouseList.value.length > 0) {
          console.log('重新映射仓库数据的关联字段...')
          warehouseList.value = warehouseList.value.map((warehouse: any) => ({
            ...warehouse,
            categoryName: categoryMap.value.get(warehouse.categoryId) || warehouse.categoryName || '未分类',
            storageTypeName: storageTypeMap.value.get(warehouse.storageTypeId) || warehouse.storageTypeName || '未设置',
            structureName: structureMap.value.get(warehouse.structureId) || warehouse.structureName || '未设置',
            personInChargeName: personMap.value.get(warehouse.personInChargeId) || warehouse.personInChargeName || '未指定'
          }))
        }
      }
    } catch (apiError) {
      console.log('API加载失败，使用基础数据字典:', apiError)
      initializeBasicDataDictionaries()
    }
  } catch (error) {
    console.error('加载数据字典失败，使用基础数据字典:', error)
    initializeBasicDataDictionaries()
  }
}

const loadWarehouseList = async () => {
  try {
    loading.value = true
    const params: any = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      keyword: searchKeyword.value
    }

    console.log('开始加载仓库列表...')
    console.log('请求参数:', params)

    const response: any = await getWarehouseList(params)
    console.log('API响应完整数据:', JSON.stringify(response, null, 2))

    // 由于axios拦截器返回response.data，所以response就是API的响应体
    const responseCode = response.code || response.Code
    console.log('响应状态码:', responseCode)
    console.log('响应数据结构:', response.data)

    if (responseCode === 200) {
      // response.data包含分页信息和数据
      let warehouses = response.data?.pageData || []
      console.log('提取的仓库数据:', warehouses)

      // 使用数据字典填充关联字段
      warehouses = warehouses.map((warehouse: any) => {
        const categoryName = categoryMap.value.get(warehouse.categoryId) || warehouse.categoryName || '未分类'
        const storageTypeName = storageTypeMap.value.get(warehouse.storageTypeId) || warehouse.storageTypeName || '未设置'
        const structureName = structureMap.value.get(warehouse.structureId) || warehouse.structureName || '未设置'
        const personInChargeName = personMap.value.get(warehouse.personInChargeId) || warehouse.personInChargeName || '未指定'

        console.log(`仓库 ${warehouse.warehouseName} 字段映射:`, {
          categoryId: warehouse.categoryId,
          categoryName,
          storageTypeId: warehouse.storageTypeId,
          storageTypeName,
          structureId: warehouse.structureId,
          structureName,
          personInChargeId: warehouse.personInChargeId,
          personInChargeName
        })

        return {
          ...warehouse,
          categoryName,
          storageTypeName,
          structureName,
          personInChargeName
        }
      })

      warehouseList.value = warehouses
      pagination.total = response.data?.totalCount || 0

      console.log('最终仓库列表数据:', warehouseList.value)
      console.log('总数:', pagination.total)
      console.log('数据字典状态:', {
        categories: categoryMap.value.size,
        storageTypes: storageTypeMap.value.size,
        structures: structureMap.value.size,
        persons: personMap.value.size
      })

      if (warehouses.length === 0) {
        console.warn('警告：API返回成功但数据为空')
      } else {
        ElMessage.success(`成功加载 ${warehouses.length} 条仓库数据`)
      }
    } else {
      console.error('API返回错误状态码:', responseCode)
      ElMessage.error(response.message || '获取仓库列表失败')
    }
  } catch (error: any) {
    console.error('加载仓库列表失败:', error)
    console.error('错误详情:', {
      message: error.message,
      response: error.response,
      status: error.response?.status,
      data: error.response?.data
    })

    // 检查是否是认证问题
    if (error.response?.status === 401) {
      ElMessage.error('认证失败，请重新登录')
      console.log('当前token:', localStorage.getItem('token'))
    } else if (error.response?.status === 404) {
      ElMessage.error('API接口不存在，请检查后端服务')
    } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      ElMessage.error('网络连接失败，请检查后端服务是否启动')
    } else {
      ElMessage.error(`加载仓库列表失败: ${error.message || '未知错误'}`)
    }

    // 设置空数据以显示"暂无数据"
    warehouseList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadWarehouseList()
}



const handleAdd = () => {
  dialogMode.value = 'add'
  currentWarehouse.value = null
  dialogVisible.value = true
}

const handleEdit = (row: any) => {
  // 如果是根节点，使用根节点编辑对话框
  if (row.warehouseNumber === '根节点') {
    currentWarehouse.value = { ...row }
    rootNodeDialogVisible.value = true
  } else {
    dialogMode.value = 'edit'
    currentWarehouse.value = { ...row }
    dialogVisible.value = true
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除仓库"${row.warehouseName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response: any = await deleteWarehouse(row.id)
    // 由于axios拦截器，response就是响应体
    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadWarehouseList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除仓库失败:', error)
      ElMessage.error('删除仓库失败')
    }
  }
}

const handleView = (row: any) => {
  currentWarehouse.value = { ...row }
  detailDialogVisible.value = true
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'table' ? 'list' : 'table'
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  loadWarehouseList()
  ElMessage.success(dialogMode.value === 'add' ? '新增成功' : '编辑成功')
}

const handleRootNodeSuccess = () => {
  rootNodeDialogVisible.value = false
  loadWarehouseList()
  ElMessage.success('根节点更新成功')
}

const handleEditFromDetail = (warehouse: any) => {
  handleEdit(warehouse)
}

const handleDeleteFromDetail = (warehouse: any) => {
  handleDelete(warehouse)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadWarehouseList()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  loadWarehouseList()
}

// 刷新数据字典
const refreshDataDictionaries = async () => {
  try {
    console.log('手动刷新数据字典...')
    await loadDataDictionaries()

    // 重新加载仓库列表以应用新的数据字典
    await loadWarehouseList()
    ElMessage.success('数据字典刷新成功')
  } catch (error) {
    console.error('刷新数据字典失败:', error)
    ElMessage.error('刷新数据字典失败')
  }
}

// 测试不同端口和API路径
const testAllPorts = async () => {
  const testUrls = [
    'http://localhost:5107/api/Warehouselist',
    'http://localhost:5107/api/Warehouse/list',
    'http://localhost:5062/api/Warehouselist',
    'http://localhost:5062/api/Warehouse/list',
    'http://localhost:5251/api/Warehouselist',
    'http://localhost:5251/api/Warehouse/list'
  ]

  console.log('开始测试所有可能的API端点...')

  for (const url of testUrls) {
    try {
      console.log(`测试: ${url}`)
      const response = await fetch(url + '?page=1&pageSize=5', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      })
      console.log(`${url} - 状态: ${response.status}`)

      if (response.ok) {
        const data = await response.json()
        console.log(`${url} - 成功响应:`, data)
        ElMessage.success(`找到可用API: ${url}`)
      } else {
        console.log(`${url} - 错误: ${response.statusText}`)
      }
    } catch (error) {
      console.log(`${url} - 网络错误:`, error)
    }
  }
}





// 添加调试信息显示
const debugInfo = ref({
  apiStatus: '未测试',
  dataLoaded: false,
  errorMessage: ''
})

// 生命周期
onMounted(async () => {
  console.log('组件已挂载，开始加载数据...')
  debugInfo.value.apiStatus = '正在加载数据字典...'

  try {
    // 检查认证状态
    const token = localStorage.getItem('token')
    console.log('当前token:', token)

    // 先加载数据字典，再加载仓库列表
    console.log('开始加载数据字典...')
    debugInfo.value.apiStatus = '正在加载数据字典...'
    await loadDataDictionaries()

    console.log('数据字典加载完成，开始加载仓库列表...')
    debugInfo.value.apiStatus = '正在加载仓库列表...'
    await loadWarehouseList()
    debugInfo.value.dataLoaded = true
    debugInfo.value.apiStatus = '所有数据加载完成'
  } catch (error: any) {
    debugInfo.value.errorMessage = error.message || '未知错误'
    debugInfo.value.apiStatus = '数据加载失败'
    console.error('数据加载失败:', error)
  }
})
</script>

<style scoped>
.warehouse-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.left-operations {
  display: flex;
  gap: 12px;
}

.right-operations {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.view-toggle {
  padding: 8px 12px;
}

.warehouse-list {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.warehouse-table {
  width: 100%;
}

.warehouse-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-button--link) {
  padding: 4px 8px;
  font-size: 14px;
}
</style>