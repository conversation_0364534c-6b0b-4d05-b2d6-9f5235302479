using System;

namespace Manufacturings.Domain
{
    /// <summary>
    /// 所有实体的基类
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// 主键标识
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 创建人Id
        /// </summary>
        public string CreateName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改人Id
        /// </summary>
        public string? ModifierName { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? ModifyTime { get; set; }

        /// <summary>
        /// 软删除标识（true表示已删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;
    }
}