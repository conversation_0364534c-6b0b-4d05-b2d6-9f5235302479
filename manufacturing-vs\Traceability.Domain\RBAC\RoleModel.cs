using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 角色表
    /// </summary>
    [Table("Role")]
    public class RoleModel : BaseEntity
    {
        /// <summary>
        /// 角色名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RoleName { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool RoleState { get; set; } 

        /// <summary>
        /// 角色描述
        /// </summary>
        [StringLength(300)]
        public string Description { get; set; }


    }
} 