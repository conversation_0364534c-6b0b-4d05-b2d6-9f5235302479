﻿using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售开票明细表
    /// </summary>
    public class SalesInvoiceDetail : BaseEntity
    {
        /// <summary>
        /// 销售开票记录ID
        /// </summary>
        [Required]
        public int SalesInvoiceRecordId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SequenceNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? Specification { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 开票数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal InvoiceQuantity { get; set; }

        /// <summary>
        /// 开票单价
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal InvoiceUnitPrice { get; set; }

        /// <summary>
        /// 开票金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InvoiceAmount { get; set; }

        /// <summary>
        /// 税率（百分比）
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 0;

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
}