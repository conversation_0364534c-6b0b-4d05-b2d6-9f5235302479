using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 用户角色表
    /// </summary>
    [Table("UserRole")]
    public class UserRoleModel : BaseEntity
    {
        /// <summary>
        /// 用户Id
        /// </summary>
        [Required]
        public long UserId { get; set; }
        
        /// <summary>
        /// 角色Id
        /// </summary>
        [Required]
        public long RoleId { get; set; }
    }
} 