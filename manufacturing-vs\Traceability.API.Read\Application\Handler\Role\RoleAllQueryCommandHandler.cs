﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Read.Application.Command.Role;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.Role
{
    public class RoleAllQueryCommandHandler : IRequestHandler<RoleAllQueryCommand, APIResult<List<RoleModel>>>
    {
        private readonly IBaseRepository<RoleModel> roleRepository;

        public RoleAllQueryCommandHandler(IBaseRepository<RoleModel> roleRepository)
        {
            this.roleRepository = roleRepository;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public Task<APIResult<List<RoleModel>>> Handle(RoleAllQueryCommand request, CancellationToken cancellationToken)
        {
            try
            {
                APIResult<List<RoleModel>> result = new APIResult<List<RoleModel>>();
                result.Code = ResultCode.Success;
                result.Message = "查询成功";
                var list = roleRepository.GetAll().AsNoTracking();
                result.Data = list.ToList();
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {

                throw;
            }
            finally 
            {
                
            };
        }
    }
}
