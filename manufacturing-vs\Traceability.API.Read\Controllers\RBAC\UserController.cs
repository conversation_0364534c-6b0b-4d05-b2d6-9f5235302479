﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Dto.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Controllers.RBAC
{
    /// <summary>
    /// 用户控制器-读
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class UserController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>

        public UserController(IMediator mediator)
        {
            this.mediator = mediator;
        }

        /// <summary>
        /// 获取用户及用户角色
        /// </summary>
        /// <param name="command"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<APIResult<APIPageing<UserDto>>> GetUser([FromQuery] UserQueryCommand command)
        {
            return await mediator.Send(command);
        }
    }
}
