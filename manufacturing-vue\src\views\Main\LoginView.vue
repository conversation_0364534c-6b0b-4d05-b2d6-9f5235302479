<template>
    <div class="login-container">
        <div class="login-content">
            <!-- 左侧插图区域 -->
            <div class="illustration-section">
                <div class="illustration-container">
                    <!-- 人物角色 -->
                    <div class="character male-character">
                        <div class="character-body">
                            <div class="head"></div>
                            <div class="torso yellow-shirt"></div>
                            <div class="legs blue-pants"></div>
                        </div>
                        <div class="smartphone">
                            <div class="phone-screen">
                                <div class="gear-icon">⚙️</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="character female-character">
                        <div class="character-body">
                            <div class="head"></div>
                            <div class="torso yellow-shirt"></div>
                            <div class="legs blue-pants"></div>
                        </div>
                    </div>
                    
                    <!-- 中央显示器 -->
                    <div class="desktop-monitor">
                        <div class="monitor-screen">
                            <div class="dashboard-content">
                                <div class="text-line">Dashboard</div>
                                <div class="text-line">System Info</div>
                                <div class="text-line">Data Status</div>
                                <div class="circular-icons">
                                    <div class="circle-icon"></div>
                                    <div class="circle-icon"></div>
                                    <div class="circle-icon"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 齿轮 -->
                    <div class="gears">
                        <div class="gear yellow-gear"></div>
                        <div class="gear blue-gear"></div>
                    </div>
                    
                    <!-- 云朵 -->
                    <div class="clouds">
                        <div class="cloud">
                            <span class="code-symbol">&lt; /&gt;</span>
                        </div>
                        <div class="cloud"></div>
                    </div>
                    
                    <!-- 数据服务器 -->
                    <div class="data-servers">
                        <div class="server-bar"></div>
                        <div class="server-bar"></div>
                        <div class="server-bar"></div>
                        <div class="server-bar"></div>
                    </div>
                    
                    <!-- 连接线 -->
                    <div class="connection-lines">
                        <div class="connection-line phone-to-monitor"></div>
                        <div class="connection-line monitor-to-servers"></div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧登录表单 -->
            <div class="form-section">
                <div class="form-container">
                    <div class="login-title">ERP&MES生产制造管理综合系统</div>
                    <el-form
                        ref="ruleFormRef"
                        :model="ruleForm"
                        :rules="rules"
                        class="login-form"
                    >
                        <el-form-item prop="UserName">
                            <el-input v-model="ruleForm.UserName" placeholder="请输入用户名称" class="custom-input" />
                        </el-form-item>

                        <el-form-item prop="PassWord">
                            <el-input v-model="ruleForm.PassWord" type="password" show-password placeholder="请输入登录密码" class="custom-input"/>
                        </el-form-item>

                        <el-form-item prop="IsCode" class="captcha-item">
                            <el-input v-model="ruleForm.IsCode" placeholder="请输入校验码" class="captcha-input"/>
                            <div class="captcha-image" @click="getCode">
                                {{ruleForm.Result}}
                            </div>
                        </el-form-item>

                        <el-form-item>
                            <el-checkbox v-model="ruleForm.rememberPassword" class="remember-password">记住密码</el-checkbox>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" class="login-button" @click="submitForm(ruleFormRef)">
                                登录
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
  
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { login } from '@/Https/server';
import { useRouter } from 'vue-router';
const router = useRouter();
import { useCounterStore } from '@/stores/counter';
const store = useCounterStore();

onMounted(()=>{
    getCode();
    checkSavedCredentials();
})

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  UserName: '',
  PassWord: '',
  IsCode: '',
  Result:'',
  rememberPassword: false
})

// 检查是否有保存的用户名和密码
const checkSavedCredentials = () => {
  const savedUserName = localStorage.getItem('savedUserName');
  const savedPassword = localStorage.getItem('savedPassword');
  
  if (savedUserName!=null && savedPassword!=null) {
    ruleForm.UserName = savedUserName;
    ruleForm.PassWord = savedPassword;
    ruleForm.rememberPassword = true;
  }
}

const rules = reactive<FormRules>({
  UserName: [
    { required: true, message: '用户名不能为空', trigger: 'blur' },
  ],
  PassWord: [
    {
      required: true,
      message: '密码不能为空',
      trigger: 'change',
    },
  ],
  IsCode: [
    {
      required: true,
      message: '验证码不能为空',
      trigger: 'change',
    },
  ],
})

const getCode =()=>{
    let charCode="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWKYZ";
    let codeResult='';
    for(let i =1;i<=4;i++)
    {
        codeResult += charCode.charAt(Math.ceil(Math.random()*charCode.length));
    }
    ruleForm.Result = codeResult;
}


const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
        if(ruleForm.IsCode.toLocaleLowerCase() != ruleForm.Result.toLocaleLowerCase())
        {
            ElMessage.error("验证码输入有误！！");
            return;
        }
      login(ruleForm).then((res:any)=>{
        if(res.code==200)
        {
            store.userInfo.Nickname = res.data.name;
            store.userInfo.UserName = res.data.username;
            store.userInfo.PassWord = res.data.password;
            store.userInfo.roleId = res.data.roleId;
            store.userInfo.roleName = res.data.roleName;
            
             // 登录判定
            // 登录成功，写JWT的 token到本地
            window.localStorage.setItem("token", res.token);
            window.localStorage.setItem("refreshToken", res.refreshToken);
            
            // 如果勾选了记住密码，则保存用户名和密码到本地存储
            if (ruleForm.rememberPassword) {
              localStorage.setItem('savedUserName', ruleForm.UserName);
              localStorage.setItem('savedPassword', ruleForm.PassWord);
            } else {
              // 如果没有勾选，则清除已保存的凭据
              localStorage.removeItem('savedUserName');
              localStorage.removeItem('savedPassword');
            }
            
            ElMessage.success(res.message);
            router.push({path:"/Main"})
        }
        else
        {
            ElMessage.error(res.message);
        }
      })
    } else {
      console.log('error submit!', fields)
    }
  })
}


</script>

<style>
/* ==================== 登录页面布局 ==================== */
.login-container {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #1890ff;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
    clip-path: polygon(0 100%, 100% 100%, 100% 0, 85% 20%, 70% 40%, 55% 60%, 40% 80%, 25% 90%, 10% 95%, 0 100%);
    pointer-events: none;
}

.login-content {
    display: flex;
    width: 1000px;
    height: 600px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    animation: fadeIn 1.5s ease-out;
}
/* ==================== 登录页面布局结束 ==================== */

/* ==================== 左侧插图区域 ==================== */
.illustration-section {
    flex: 1;
    background: #f0f8ff;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 40px;
}

.illustration-section::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 100%;
    background-image: radial-gradient(circle, #1890ff 2px, transparent 2px);
    background-size: 20px 20px;
    background-repeat: repeat-y;
}

.illustration-section::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 100%;
    background-image: radial-gradient(circle, #1890ff 2px, transparent 2px);
    background-size: 20px 20px;
    background-repeat: repeat-y;
}

.illustration-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 人物角色样式 */
.character {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.male-character {
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.female-character {
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.character-body {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.head {
    width: 30px;
    height: 30px;
    background: #ffb366;
    border-radius: 50%;
    margin-bottom: 5px;
}

.torso {
    width: 40px;
    height: 50px;
    border-radius: 8px;
    margin-bottom: 5px;
}

.yellow-shirt {
    background: #ffd700;
}

.legs {
    width: 35px;
    height: 40px;
    border-radius: 8px;
}

.blue-pants {
    background: #4a90e2;
}

/* 智能手机样式 */
.smartphone {
    position: absolute;
    left: 50px;
    top: -20px;
    width: 25px;
    height: 40px;
    background: #87ceeb;
    border-radius: 5px;
    border: 2px solid #666;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: #4a90e2;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gear-icon {
    font-size: 12px;
    color: white;
}

/* 中央显示器样式 */
.desktop-monitor {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 80px;
    background: #333;
    border-radius: 10px;
    border: 3px solid #666;
    display: flex;
    align-items: center;
    justify-content: center;
}

.monitor-screen {
    width: 90%;
    height: 80%;
    background: #4a90e2;
    border-radius: 5px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.dashboard-content {
    color: white;
    font-size: 8px;
    text-align: center;
}

.text-line {
    margin-bottom: 2px;
    font-weight: bold;
}

.circular-icons {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-top: 4px;
}

.circle-icon {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

/* 齿轮样式 */
.gears {
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.gear {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    border: 3px solid;
    position: relative;
    animation: rotate 4s linear infinite;
}

.gear::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: inherit;
    border-radius: 50%;
}

.yellow-gear {
    background: #ffd700;
    border-color: #ffb366;
}

.blue-gear {
    background: #4a90e2;
    border-color: #357abd;
}

/* 云朵样式 */
.clouds {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
}

.cloud {
    width: 30px;
    height: 20px;
    background: white;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-symbol {
    font-size: 12px;
    color: #000000;
    font-weight: bold;
}

/* 数据服务器样式 */
.data-servers {
    position: absolute;
    bottom: 20%;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.server-bar {
    width: 60px;
    height: 8px;
    background: #ffd700;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 连接线样式 */
.connection-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.connection-line {
    position: absolute;
    background: #87ceeb;
    border: 2px dashed #87ceeb;
    opacity: 0.8;
}

.phone-to-monitor {
    top: 40%;
    left: 25%;
    width: 20%;
    height: 2px;
    transform: rotate(15deg);
}

.monitor-to-servers {
    top: 70%;
    left: 50%;
    width: 2px;
    height: 15%;
    transform: translateX(-50%);
}

/* ==================== 右侧表单区域 ==================== */
.form-section {
    flex: 1;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.form-container {
    width: 100%;
    max-width: 350px;
}

/* ==================== 表单样式 ==================== */
.login-form {
    width: 100%;
}

.custom-input {
    width: 100%;
}

.custom-input :deep(.el-input__wrapper) {
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 10px;
    transition: all 0.3s ease;
    box-shadow: none;
}

.custom-input :deep(.el-input__wrapper:hover) {
    border-color: #4a90e2;
    background: white;
}

.custom-input :deep(.el-input__wrapper.is-focus) {
    border-color: #4a90e2;
    background: white;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.custom-input :deep(.el-input__inner) {
    height: 50px;
    font-size: 16px;
    color: #333;
}

.custom-input :deep(.el-input__inner::placeholder) {
    color: #999;
}

/* 验证码样式 */
.captcha-item {
    display: flex;
    gap: 15px;
}

.captcha-input {
    flex: 1;
}

.captcha-image {
    width: 80px;
    height: 50px;
    background: linear-gradient(135deg, #4a90e2, #357abd);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.captcha-image:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

/* ==================== 登录标题 ==================== */
.login-title {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    line-height: 1.3;
}
/* ==================== 登录标题结束 ==================== */

/* ==================== 登录按钮 ==================== */
.login-button {
    width: 100%;
    height: 40px;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 2px;
    background: linear-gradient(90deg, #409eff, #1890ff);
    border: none;
}

.remember-password {
    margin-left: 5px;
    color: #606266;
}
/* ==================== 登录按钮结束 ==================== */

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
/* ==================== 动画效果结束 ==================== */

/* ==================== Element-plus样式覆盖 ==================== */
/* 覆盖Element-plus的默认样式 */
:deep(.el-input__wrapper) {
    border-radius: 8px;
}

:deep(.el-button) {
    border-radius: 8px;
}
/* ==================== Element-plus样式覆盖结束 ==================== */

/* ==================== 响应式设计 ==================== */
@media (max-width: 1024px) {
    .login-content {
        width: 90%;
        max-width: 800px;
        height: auto;
        min-height: 500px;
        flex-direction: column;
    }
    
    .illustration-section {
        padding: 20px;
        min-height: 300px;
    }
    
    .form-section {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .login-content {
        width: 95%;
        margin: 20px;
    }
    
    .system-title {
        font-size: 20px;
        margin-bottom: 30px;
    }
    
    .illustration-section {
        min-height: 250px;
    }
    
    .character {
        transform: scale(0.8);
    }
    
    .desktop-monitor {
        transform: scale(0.9);
    }
}
/* ==================== 响应式设计结束 ==================== */
</style>