﻿using Manufacturings.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 委外消耗中间表
    /// </summary>
    public class SaleDepleteCommand:BaseEntity
    {
        /// <summary>
        /// 委外加工Id
        /// </summary>
        public long OutsourcingProcessing { get; set; }
        /// <summary>
        /// 消耗物品Id
        /// </summary>
        public long DepleteId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public string WarehouseId { get; set; } = string.Empty;
        /// <summary>
        /// 计划消耗数量
        /// </summary>
        public int Plannedquantity { get; set; }
        /// <summary>
        /// 主单位数量
        /// </summary>
        public int Number { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;
    }
}
