using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ManufacturingsERP.API.Models
{
    /// <summary>
    /// 仓库分类
    /// </summary>
    [Table("WarehouseCategory")]
    public class WarehouseCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string CategoryName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public bool IsEnabled { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string CreateUser { get; set; } = string.Empty;

        public DateTime? ModificationTime { get; set; }

        [StringLength(50)]
        public string ModifierName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 存储类型
    /// </summary>
    [Table("StorageType")]
    public class StorageType
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string TypeName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public bool IsEnabled { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string CreateUser { get; set; } = string.Empty;

        public DateTime? ModificationTime { get; set; }

        [StringLength(50)]
        public string ModifierName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 仓库结构
    /// </summary>
    [Table("WarehouseStructure")]
    public class WarehouseStructure
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string StructureName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public bool IsEnabled { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string CreateUser { get; set; } = string.Empty;

        public DateTime? ModificationTime { get; set; }

        [StringLength(50)]
        public string ModifierName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 人员信息
    /// </summary>
    [Table("Person")]
    public class Person
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string PersonName { get; set; } = string.Empty;

        [StringLength(100)]
        public string Department { get; set; } = string.Empty;

        [StringLength(100)]
        public string Position { get; set; } = string.Empty;

        [StringLength(20)]
        public string Phone { get; set; } = string.Empty;

        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        public bool IsEnabled { get; set; } = true;

        public DateTime CreateTime { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string CreateUser { get; set; } = string.Empty;

        public DateTime? ModificationTime { get; set; }

        [StringLength(50)]
        public string ModifierName { get; set; } = string.Empty;
    }
}
