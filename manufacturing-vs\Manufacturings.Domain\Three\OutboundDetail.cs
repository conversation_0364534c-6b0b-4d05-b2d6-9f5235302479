using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 出库明细实体
    /// </summary>
    [Table("OutboundDetail")]
    public class OutboundDetail : BaseEntity
    {
        /// <summary>
        /// 出库单ID
        /// </summary>
        public long OutboundOrderId { get; set; }

        /// <summary>
        /// 物料ID
        /// </summary>
        public long MaterialId { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ItemName { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(100)]
        public string SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string Unit { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        [StringLength(100)]
        public string Brand { get; set; }

        /// <summary>
        /// 出库数量
        /// </summary>
        [Required]
        public decimal OutboundQuantity { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [StringLength(50)]
        public string BatchNumber { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; }
    }
}

