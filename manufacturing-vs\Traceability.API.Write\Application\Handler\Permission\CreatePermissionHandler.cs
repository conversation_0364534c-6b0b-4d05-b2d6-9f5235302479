﻿using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Traceability.API.Write.Application.Command.Permission;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;
using Yitter.IdGenerator;

namespace Traceability.API.Write.Application.Handler.Permission
{
    public class CreatePermissionHandler : IRequestHandler<CreatePermissionCommand, APIResult<object>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;
        private readonly ILogger<CreatePermissionHandler> _logger;
        private readonly IIdentifyService identifyService;

        public CreatePermissionHandler(
            IBaseRepository<PermissionModel> permissionRepository, 
            IMapper mapper,
            ILogger<CreatePermissionHandler> logger, IIdentifyService identifyService)
        {
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
            this._logger = logger;
            this.identifyService = identifyService;
        }

        public async Task<APIResult<object>> Handle(CreatePermissionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"开始处理创建权限请求，权限名称：{request.PermissionName}");
            APIResult<object> result = new APIResult<object>();
            
            try
            {
                var permission = permissionRepository.GetAll().Where(x => x.PermissionName == request.PermissionName).FirstOrDefault();
                if (permission != null)
                {
                    _logger.LogWarning($"创建权限失败：权限名称已存在，权限名称：{request.PermissionName}");
                    result.Code = ResultCode.Fail;
                    result.Message = "你要添加的权限已存在";
                    return await Task.FromResult(result);
                }
                
                var model = mapper.Map<PermissionModel>(request);
                model.CreateTime = DateTime.Now;
                model.CreateId = Convert.ToInt64(identifyService.UserId);
                await permissionRepository.AddAsync(model);
                
                result.Code = ResultCode.Success;
                result.Message = "添加成功";
                result.Data = 1;
                
                _logger.LogInformation($"创建权限成功，权限ID：{model.Id}，权限名称：{model.PermissionName}");
                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建权限过程中出现异常：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "创建权限失败";
                return await Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
