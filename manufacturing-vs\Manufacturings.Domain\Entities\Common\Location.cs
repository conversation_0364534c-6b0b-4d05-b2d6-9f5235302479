using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 地区表
    /// </summary>
    public class Location : BaseEntity
    {
        /// <summary>
        /// 地区名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string LocationName { get; set; } = string.Empty;

        /// <summary>
        /// 地区编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string LocationCode { get; set; } = string.Empty;

        /// <summary>
        /// 上级地区ID（树形结构）
        /// </summary>
        public long? ParentId { get; set; }

        /// <summary>
        /// 地区级别（省/市/区）
        /// </summary>
        public int Level { get; set; } = 1;

        /// <summary>
        /// 地区描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 