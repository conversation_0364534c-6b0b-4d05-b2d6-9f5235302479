<template>
  <el-dialog :model-value="visible" title="仓库详情" width="800px" :close-on-click-modal="false" @close="handleClose">
    <div class="warehouse-detail">
      <!-- 仓库名称和操作按钮 -->
      <div class="detail-header">
        <div class="warehouse-title">
          <h3>{{ warehouse?.warehouseName || '仓库名称' }}</h3>
          <div class="metadata">
            <p>创建人: {{ warehouse?.createName || '-' }}</p>
            <p>创建时间: {{ formatDateTime(warehouse?.createTime) }}</p>
            <p>最后修改人: {{ warehouse?.modifierName || warehouse?.createName || '-' }}</p>
            <p>修改时间: {{ formatDateTime(warehouse?.modifyTime) || formatDateTime(warehouse?.createTime) }}</p>
          </div>
        </div>
        <div class="action-buttons">
          <el-button type="primary" @click="handleEdit">
            编辑
          </el-button>
          <el-button type="danger" @click="handleDelete">
            删除
          </el-button>
        </div>
      </div>

      <!-- 仓库详情信息 -->
      <div class="detail-content">
        <div class="section-title">
          <el-icon class="title-icon">
            <InfoFilled />
          </el-icon>
          仓库详情
        </div>

        <div class="section-subtitle">
          <el-icon class="title-icon">
            <InfoFilled />
          </el-icon>
          基础信息
        </div>

        <div class="info-grid">
          <div class="info-column">
            <div class="info-item">
              <span class="info-label">上级名称:</span>
              <span class="info-value">{{ getParentWarehouseName(warehouse?.parentId) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">仓库名称:</span>
              <span class="info-value">{{ warehouse?.warehouseName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">存储类型:</span>
              <span class="info-value">{{ getStorageTypeName(warehouse?.storageTypeId) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">负责人:</span>
              <span class="info-value">{{ getPersonName(warehouse?.personInChargeId) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">仓库地址:</span>
              <span class="info-value">{{ warehouse?.address || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">备注:</span>
              <span class="info-value">{{ warehouse?.remarks || '-' }}</span>
            </div>
          </div>

          <div class="info-column">
            <div class="info-item">
              <span class="info-label">仓库编号:</span>
              <span class="info-value">{{ warehouse?.warehouseNumber || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">仓库分类:</span>
              <span class="info-value">{{ getCategoryName(warehouse?.categoryId) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">仓库结构:</span>
              <span class="info-value">{{ getStructureName(warehouse?.structureId) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">状态:</span>
              <span class="info-value">
                <el-tag :type="warehouse?.isEnabled ? 'success' : 'danger'" size="small">
                  {{ warehouse?.isEnabled ? '启用' : '禁用' }}
                </el-tag>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'
import {
  getWarehouseList,
  getAllWarehouseCategories,
  getAllStorageTypes,
  getAllWarehouseStructures,
  getAllPersons
} from '@/Https/server'

// Props
interface Props {
  visible: boolean
  warehouse?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'edit': [warehouse: any]
  'delete': [warehouse: any]
}>()

// 响应式数据
const parentWarehouseOptions = ref<any[]>([])
const categoryOptions = ref<any[]>([])
const storageTypeOptions = ref<any[]>([])
const structureOptions = ref<any[]>([])
const personOptions = ref<any[]>([])

// 计算属性
const warehouse = computed(() => props.warehouse)

// 方法
const formatDateTime = (dateTime: string | Date) => {
  if (!dateTime) return '-'
  try {
    const date = new Date(dateTime)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return dateTime.toString()
  }
}

const getParentWarehouseName = (parentId: number | null) => {
  if (!parentId) return '-'
  const parent = parentWarehouseOptions.value.find(item => item.id === parentId)
  return parent?.warehouseName || '-'
}

const getCategoryName = (categoryId: number | null) => {
  if (!categoryId) return '-'
  const category = categoryOptions.value.find(item => item.id === categoryId)
  return category?.categoryName || '-'
}

const getStorageTypeName = (storageTypeId: number | null) => {
  if (!storageTypeId) return '-'
  const storageType = storageTypeOptions.value.find(item => item.id === storageTypeId)
  return storageType?.typeName || '-'
}

const getStructureName = (structureId: number | null) => {
  if (!structureId) return '-'
  const structure = structureOptions.value.find(item => item.id === structureId)
  return structure?.structureName || '-'
}

const getPersonName = (personId: number | null) => {
  if (!personId) return '-'
  const person = personOptions.value.find(item => item.id === personId)
  return person?.name || '-'
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleEdit = () => {
  emit('edit', props.warehouse)
  emit('update:visible', false)
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除仓库"${props.warehouse?.warehouseName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    emit('delete', props.warehouse)
    emit('update:visible', false)
  } catch (error) {
    // 用户取消删除
  }
}

// 加载选项数据
const loadOptions = async () => {
  try {
    // 加载上级仓库选项
    const parentResponse: any = await getWarehouseList({ pageSize: 1000 })
    const parentCode = parentResponse.code || parentResponse.Code
    if (parentCode === 200) {
      parentWarehouseOptions.value = parentResponse.data?.pageData || []
    }

    // 加载仓库分类选项 - 使用不分页的API
    const categoryResponse: any = await getAllWarehouseCategories()
    console.log('仓库分类响应:', categoryResponse)
    const categoryCode = categoryResponse.code || categoryResponse.Code
    if (categoryCode === 200) {
      categoryOptions.value = categoryResponse.data || []
    }

    // 加载存储类型选项 - 使用不分页的API
    const storageTypeResponse: any = await getAllStorageTypes()
    console.log('存储类型响应:', storageTypeResponse)
    const storageTypeCode = storageTypeResponse.code || storageTypeResponse.Code
    if (storageTypeCode === 200) {
      storageTypeOptions.value = storageTypeResponse.data || []
    }

    // 加载仓库结构选项 - 使用不分页的API
    const structureResponse: any = await getAllWarehouseStructures()
    console.log('仓库结构响应:', structureResponse)
    const structureCode = structureResponse.code || structureResponse.Code
    if (structureCode === 200) {
      structureOptions.value = structureResponse.data || []
    }

    // 加载人员选项 - 使用不分页的API
    const personResponse: any = await getAllPersons()
    console.log('人员响应:', personResponse)
    const personCode = personResponse.code || personResponse.Code
    if (personCode === 200) {
      personOptions.value = personResponse.data || []
    }
  } catch (error) {
    console.error('加载选项数据失败:', error)
  }
}

// 初始化时加载选项数据
loadOptions()
</script>

<style scoped>
.warehouse-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.warehouse-title h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.metadata p {
  margin: 5px 0;
  color: #909399;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.detail-content {
  background-color: #fafafa;
  border-radius: 8px;
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.section-subtitle {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #606266;
  font-size: 16px;
  font-weight: 500;
}

.title-icon {
  margin-right: 8px;
  color: #409eff;
}

.info-grid {
  display: flex;
  gap: 40px;
}

.info-column {
  flex: 1;
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-label {
  min-width: 100px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  color: #303133;
  flex: 1;
}

.dialog-footer {
  text-align: center;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-tag) {
  border-radius: 4px;
}
</style>