using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单退货记录表
    /// </summary>
    public class PurchaseOrderReturnRecord : BaseEntity
    {
        /// <summary>
        /// 采购订单ID
        /// </summary>
        [Required]
        public int PurchaseOrderId { get; set; }

        /// <summary>
        /// 出库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 出库订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OutboundOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 出库日期
        /// </summary>
        [Required]
        public DateTime OutboundDate { get; set; }

        /// <summary>
        /// 出库类型（采购退货出库、销售出库等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string OutboundType { get; set; } = string.Empty;

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }

        /// <summary>
        /// 出库总量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalOutboundQuantity { get; set; }

        /// <summary>
        /// 出库金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OutboundAmount { get; set; }

        /// <summary>
        /// 退货原因
        /// </summary>
        [StringLength(500)]
        public string? ReturnReason { get; set; }

        /// <summary>
        /// 退货状态（待退货、已退货、已确认等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ReturnStatus { get; set; } = "待退货";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000)]
        public string? Remarks { get; set; }
    }
} 