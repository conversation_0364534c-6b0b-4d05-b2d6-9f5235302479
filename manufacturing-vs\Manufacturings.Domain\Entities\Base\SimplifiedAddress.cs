using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 简化地址表（减少冗余字段）
    /// </summary>
    public class SimplifiedAddress : BaseEntity
    {
        /// <summary>
        /// 地址名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AddressName { get; set; } = string.Empty;

        /// <summary>
        /// 完整地址
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FullAddress { get; set; } = string.Empty;

        /// <summary>
        /// 邮政编码
        /// </summary>
        [StringLength(10)]
        public string? PostalCode { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 