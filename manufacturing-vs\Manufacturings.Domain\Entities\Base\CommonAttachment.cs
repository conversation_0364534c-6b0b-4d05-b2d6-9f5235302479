using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用附件表（替代各种专门的附件表）
    /// </summary>
    public class CommonAttachment : BaseEntity
    {
        /// <summary>
        /// 关联实体ID
        /// </summary>
        [Required]
        public long RelatedEntityId { get; set; }

        /// <summary>
        /// 关联实体类型
        /// </summary>
        [Required]
        [StringLength(100)]
        public string RelatedEntityType { get; set; } = string.Empty;

        /// <summary>
        /// 附件名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string AttachmentName { get; set; } = string.Empty;

        /// <summary>
        /// 原始文件名
        /// </summary>
        [StringLength(200)]
        public string? OriginalFileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        [StringLength(100)]
        public string? FileType { get; set; }

        /// <summary>
        /// 文件扩展名
        /// </summary>
        [StringLength(20)]
        public string? FileExtension { get; set; }

        /// <summary>
        /// 附件描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 上传人ID
        /// </summary>
        [Required]
        public long UploadedBy { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        [Required]
        public DateTime UploadedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 