{"format": 1, "restore": {"D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.Infrastructrue\\Traceability.Infrastructrue.csproj": {}}, "projects": {"D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.Infrastructrue\\Traceability.Infrastructrue.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.Infrastructrue\\Traceability.Infrastructrue.csproj", "projectName": "Traceability.Infrastructrue", "projectPath": "D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.Infrastructrue\\Traceability.Infrastructrue.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.Infrastructrue\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400\\RuntimeIdentifierGraph.json"}}}}}