﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Traceability.API.Write.Application.Command.Permission;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Write.Controllers.RBAC
{
    /// <summary>
    /// 权限管理
    /// </summary>
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class PermissionController : ControllerBase
    {
        /// <summary>
        /// 中介者
        /// </summary>
        private readonly IMediator mediator;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mediator">中介者</param>
        public PermissionController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 权限添加接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<object>> CreatePermission(CreatePermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 权限编辑接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<object>> UpdatePermission(UpdatePermissionCommand request)
        {
            return mediator.Send(request);
        }
        /// <summary>
        /// 权限删除接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public Task<APIResult<object>> Handle(DelPermissionCommand request)
        {
            return mediator.Send(request);
        }
    }
}
