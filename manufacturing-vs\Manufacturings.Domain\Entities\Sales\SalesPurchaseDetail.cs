using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售采购明细表
    /// </summary>
    public class SalesPurchaseDetail : BaseEntity
    {
        /// <summary>
        /// 销售采购记录ID
        /// </summary>
        [Required]
        public long SalesPurchaseRecordId { get; set; }

        /// <summary>
        /// 序号
        /// </summary>
        [Required]
        public int SerialNumber { get; set; }

        /// <summary>
        /// 物品编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemNumber { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(200)]
        public string? SpecificationModel { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(50)]
        public string? Unit { get; set; }

        /// <summary>
        /// 采购数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseQuantity { get; set; }

        /// <summary>
        /// 采购单价
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseUnitPrice { get; set; }

        /// <summary>
        /// 采购金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal PurchaseAmount { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(5,2)")]
        public decimal TaxRate { get; set; } = 0;

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 到货数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ReceivedQuantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 