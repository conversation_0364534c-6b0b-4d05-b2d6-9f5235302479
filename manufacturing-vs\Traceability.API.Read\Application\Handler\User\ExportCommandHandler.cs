﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System.Collections.Generic;
using System.Net.Mime;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.User
{
    public class ExportCommandHandler : IRequestHandler<ExportCommand, APIResult<FileResultDto>>
    {

        /// <summary>
        /// 用户仓储
        /// </summary>
        private readonly IBaseRepository<UserModel> _userRepository;
        /// <summary>
        /// 用户角色仓储
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> _userRoleRepository;
        /// <summary>
        /// 角色仓储
        /// </summary>
        private readonly IBaseRepository<RoleModel> _roleRepository;
        /// <summary>
        /// 映射
        /// </summary>
        private readonly IMapper mapper;
        /// <summary>
        /// 日志服务
        /// </summary>
        private readonly ILogger<UserQueryCommandHandler> _logger;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mapper">映射</param>
        /// <param name="userRepository">用户仓储</param>
        /// <param name="userRoleRepository">用户角色仓储</param>
        /// <param name="roleRepository">角色仓储</param>
        /// <param name="logger">日志服务</param>
        public ExportCommandHandler(
            IMapper mapper,
            IBaseRepository<UserModel> userRepository,
            IBaseRepository<UserRoleModel> userRoleRepository,
            IBaseRepository<RoleModel> roleRepository,
            ILogger<UserQueryCommandHandler> logger)
        {
            this.mapper = mapper;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public Task<APIResult<FileResultDto>> Handle(ExportCommand request, CancellationToken cancellationToken)
        {
            APIResult<FileResultDto> result = new APIResult<FileResultDto>();

            try
            {
                #region 获取用户信息
                var list = _userRepository.GetAll().AsNoTracking();
                var userDto = mapper.Map<List<UserDto>>(list);
                //获取用户Id
                var userIdList = list.Select(x => x.Id).ToList();
                // 1. 一次性获取所有用户角色关系
                var userRoles = _userRoleRepository.GetAll()
                    .Where(x => userIdList.Contains(x.UserId))
                    .Select(x => new { x.UserId, x.RoleId })
                    .ToList();

                // 2. 获取所有涉及的角色ID
                var roleIds = userRoles.Select(x => x.RoleId).Distinct().ToList();

                // 3. 一次性获取所有角色名称
                var roles = _roleRepository.GetAll()
                    .Where(x => roleIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.RoleName })
                    .ToList();

                // 4. 处理用户数据
                foreach (var item in userDto)
                {
                    var userRoleIds = userRoles
                        .Where(x => x.UserId == item.Id)
                        .Select(x => x.RoleId)
                        .ToList();

                    item.RoleId = userRoleIds;

                    var userRoleNames = roles
                        .Where(x => userRoleIds.Contains(x.Id))
                        .Select(x => x.RoleName);

                    item.RoleName = string.Join(",", userRoleNames);
                }
                #endregion

                //创建Excel
                IWorkbook book = new XSSFWorkbook();
                //在创建好的工作簿中创建工作表
                var sheet = book.CreateSheet("用户信息表");
                //在创建好的工作簿中创建表头标题行
                var row = sheet.CreateRow(0);
                row.CreateCell(0).SetCellValue("用户ID");
                row.CreateCell(1).SetCellValue("用户名");
                row.CreateCell(2).SetCellValue("用户姓名");
                row.CreateCell(3).SetCellValue("用户状态");
                row.CreateCell(4).SetCellValue("角色名称");

                //行号
                int num = 1;
                //通过循环获取订单信息
                foreach (var item in userDto)
                { 
                    var rowcell = sheet.CreateRow(num);
                    rowcell.CreateCell(0).SetCellValue(item.Id);
                    rowcell.CreateCell(1).SetCellValue(item.Username);
                    rowcell.CreateCell(2).SetCellValue(item.Name);
                    rowcell.CreateCell(3).SetCellValue(item.UserState?"启用":"禁用");
                    rowcell.CreateCell(4).SetCellValue(item.RoleName);
                    num++;
                }
                //定义字节数组,用于保存文件内容
                byte[] bytes = null;
                //类型转换为文件 C#类 内存流 把内存流变成字节数组
                using (MemoryStream ms = new MemoryStream())
                {
                    //把文件写入到内存流中
                    book.Write(ms);
                    //再把内存流转换为bype[]保存到变量中
                    bytes = ms.ToArray();
                }
                //返回文件对象
                //三个参数 一是文件内容的字节数组,二是文件格式,三是,文件名称
                result.Code = ResultCode.Success;
                result.Message = "导出成功";
                result.Data = new FileResultDto
                {
                    FileContent = bytes,
                    ContentType = MediaTypeNames.Application.Octet,
                    FileName = "用户信息表.xlsx"
                };
            }
            catch (Exception ex)
            {
                result.Code = ResultCode.Fail;
                result.Message = $"导出失败：{ex.Message}";
            }
            return Task.FromResult(result);
        }
    }
}
