using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Warehouses;
using ManufacturingsERP.API.Application.Interfaces;
using Microsoft.EntityFrameworkCore;
using Manufacturings.Infrastructure;

namespace ManufacturingsERP.API.Application.Repositories
{
    /// <summary>
    /// 仓库仓储实现类
    /// </summary>
    public class WarehouseRepository : BaseRepository<Warehouse>, IWarehouseRepository
    {
        public WarehouseRepository(MyDbContext context) : base(context)
        {
        }

        public async Task<Warehouse?> GetByWarehouseNumberAsync(string warehouseNumber)
        {
            return await Context.Warehouses
                .FirstOrDefaultAsync(w => w.WarehouseNumber == warehouseNumber);
        }

        public async Task<bool> IsWarehouseNameExistsAsync(string warehouseName, long? excludeId = null)
        {
            var query = Context.Warehouses.Where(w => w.WarehouseName == warehouseName);
            
            if (excludeId.HasValue)
            {
                query = query.Where(w => w.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task<List<WarehouseTreeDto>> GetWarehouseTreeAsync()
        {
            var warehouses = await Context.Warehouses
                .Where(w => w.IsEnabled)
                .OrderBy(w => w.CreateTime)
                .ToListAsync();

            var warehouseDict = warehouses.ToDictionary(w => w.Id);
            var rootWarehouses = new List<WarehouseTreeDto>();

            foreach (var warehouse in warehouses)
            {
                var warehouseDto = new WarehouseTreeDto
                {
                    Id = warehouse.Id,
                    WarehouseNumber = warehouse.WarehouseNumber,
                    WarehouseName = warehouse.WarehouseName,
                    ParentId = warehouse.ParentId
                };

                if (warehouse.ParentId.HasValue && warehouseDict.ContainsKey(warehouse.ParentId.Value))
                {
                    // 添加到父仓库的子仓库列表中
                    var parent = warehouseDict[warehouse.ParentId.Value];
                    var parentDto = rootWarehouses.FirstOrDefault(w => w.Id == parent.Id);
                    if (parentDto != null)
                    {
                        parentDto.Children.Add(warehouseDto);
                    }
                }
                else
                {
                    // 根仓库
                    rootWarehouses.Add(warehouseDto);
                }
            }

            return rootWarehouses;
        }
    }
} 