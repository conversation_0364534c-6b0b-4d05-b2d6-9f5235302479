﻿namespace Manufacturing.Execution.API.Common
{
    /// <summary>
    /// JWT配置类
    /// </summary>
    public class JwtSettings
    {
        /// <summary>
        /// 私钥
        /// </summary>
        public string SecretKey { get; set; }
        /// <summary>
        /// 发布者信息
        /// </summary>
        public string Issuer { get; set; }
        /// <summary>
        /// 接收者信息
        /// </summary>
        public string Audience { get; set; }
        /// <summary>
        /// 访问令牌过期时间（分钟）
        /// </summary>
        public int AccessTokenExpirationMinutes { get; set; }
        /// <summary>
        /// 访问令牌过期时间（分钟）
        /// </summary>
        public int RefreshExpireTime { get; set; }
    }
}
