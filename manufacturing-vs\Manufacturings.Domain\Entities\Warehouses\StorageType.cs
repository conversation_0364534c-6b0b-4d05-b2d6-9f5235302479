using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Warehouses
{
    /// <summary>
    /// 存储类型表
    /// </summary>
    public class StorageType : BaseEntity
    {
        /// <summary>
        /// 存储类型名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string TypeName { get; set; } = string.Empty;

        /// <summary>
        /// 存储类型编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string TypeCode { get; set; } = string.Empty;

        /// <summary>
        /// 存储类型描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 