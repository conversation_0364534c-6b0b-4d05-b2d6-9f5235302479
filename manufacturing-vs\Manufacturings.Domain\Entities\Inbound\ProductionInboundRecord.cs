using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 生产入库记录表
    /// </summary>
    public class ProductionInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 生产单号
        /// </summary>
        [StringLength(50)]
        public string? ProductionOrderNumber { get; set; }

        /// <summary>
        /// 生产线ID
        /// </summary>
        public int? ProductionLineId { get; set; }

        /// <summary>
        /// 生产计划ID
        /// </summary>
        public int? ProductionPlanId { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        public DateTime? ProductionDate { get; set; }

        /// <summary>
        /// 质检状态
        /// </summary>
        public QualityInspectionStatus? QualityInspectionStatus { get; set; }

        /// <summary>
        /// 质检人员ID
        /// </summary>
        public int? QualityInspectorId { get; set; }

        /// <summary>
        /// 质检日期
        /// </summary>
        public DateTime? QualityInspectionDate { get; set; }

        /// <summary>
        /// 生产备注
        /// </summary>
        [StringLength(500)]
        public string? ProductionRemarks { get; set; }
    }
} 