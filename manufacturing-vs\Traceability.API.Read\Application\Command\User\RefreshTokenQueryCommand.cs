﻿using MediatR;
using Traceability.API.Read.Dto.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Application.Command.User
{
    public class RefreshTokenQueryCommand:IRequest<APIResult<TokenResponseDto>>
    {
        /// <summary>
        /// 授权token
        /// </summary>
        public string? AccessToken { get; set; }
        /// <summary>
        /// 刷新token
        /// </summary>
        public string? RefreshToken { get; set; }
    }
}
