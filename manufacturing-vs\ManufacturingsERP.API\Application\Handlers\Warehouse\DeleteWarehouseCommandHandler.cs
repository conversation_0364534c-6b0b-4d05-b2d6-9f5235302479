using MediatR;
using ManufacturingsERP.API.Application.DTOs;
using ManufacturingsERP.API.Application.Interfaces;
using Manufacturings.Infrastructrue.Error;
using Manufacturings.Infrastructrue;
using Microsoft.EntityFrameworkCore;

namespace ManufacturingsERP.API.Application.Handlers.Warehouse
{
    /// <summary>
    /// 删除仓库命令处理器
    /// </summary>
    public class DeleteWarehouseCommandHandler : IRequestHandler<DeleteWarehouseCommand, APIResult<bool>>
    {
        private readonly IWarehouseRepository _warehouseRepository;

        public DeleteWarehouseCommandHandler(IWarehouseRepository warehouseRepository)
        {
            _warehouseRepository = warehouseRepository;
        }

        public async Task<APIResult<bool>> Handle(DeleteWarehouseCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 获取要删除的仓库
                var warehouse = await _warehouseRepository.GetModel(request.Id);
                if (warehouse == null)
                {
                    return new APIResult<bool>
                    {
                        Code = ResultCode.Fail,
                        Message = "仓库不存在",
                        Data = false
                    };
                }

                // 检查是否有子仓库
                var hasChildren = _warehouseRepository.GetAll().Where(w => w.ParentId == request.Id && !w.IsDeleted).ToList();
                
                if (hasChildren.Any())
                {
                    return new APIResult<bool>
                    {
                        Code = ResultCode.Fail,
                        Message = "该仓库下还有子仓库，无法删除",
                        Data = false
                    };
                }

                // 检查是否有关联的库存记录
                // 这里需要根据实际的业务逻辑来检查
                // 例如：检查是否有入库记录、出库记录等

                // 执行软删除
                warehouse.IsDeleted = true;
                warehouse.ModifyTime = DateTime.Now;
                warehouse.ModifierName = "系统"; // 这里应该从当前用户上下文获取

                await _warehouseRepository.UpdateAsync(warehouse);

                return new APIResult<bool>
                {
                    Code = ResultCode.Success,
                    Message = "仓库删除成功",
                    Data = true
                };
            }
            catch (Exception ex)
            {
                return new APIResult<bool>
                {
                    Code = ResultCode.Fail,
                    Message = $"删除仓库失败: {ex.Message}",
                    Data = false
                };
            }
        }
    }
} 