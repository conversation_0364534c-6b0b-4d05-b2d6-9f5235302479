namespace Manufacturings.Domain.Enums
{
    /// <summary>
    /// 仓库状态枚举
    /// </summary>
    public enum WarehouseStatus
    {
        /// <summary>
        /// 启用
        /// </summary>
        Enabled = 1,

        /// <summary>
        /// 禁用
        /// </summary>
        Disabled = 0
    }

    /// <summary>
    /// 仓库类型枚举
    /// </summary>
    public enum WarehouseType
    {
        /// <summary>
        /// 根节点
        /// </summary>
        Root = 0,

        /// <summary>
        /// 主仓库
        /// </summary>
        Main = 1,

        /// <summary>
        /// 分仓库
        /// </summary>
        Branch = 2,

        /// <summary>
        /// 子仓库
        /// </summary>
        Sub = 3
    }

    /// <summary>
    /// 操作类型枚举
    /// </summary>
    public enum OperationType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Create = 1,

        /// <summary>
        /// 编辑
        /// </summary>
        Edit = 2,

        /// <summary>
        /// 删除
        /// </summary>
        Delete = 3,

        /// <summary>
        /// 查看
        /// </summary>
        View = 4
    }
} 