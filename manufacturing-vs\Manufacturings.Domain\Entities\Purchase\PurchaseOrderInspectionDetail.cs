using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Purchase
{
    /// <summary>
    /// 采购订单检验明细表
    /// </summary>
    public class PurchaseOrderInspectionDetail : BaseEntity
    {
        /// <summary>
        /// 检验记录ID
        /// </summary>
        [Required]
        public int InspectionRecordId { get; set; }

        /// <summary>
        /// 物品编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ItemCode { get; set; } = string.Empty;

        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 检验数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal InspectionQuantity { get; set; }

        /// <summary>
        /// 合格数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal QualifiedQuantity { get; set; }

        /// <summary>
        /// 不合格数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal UnqualifiedQuantity { get; set; }

        /// <summary>
        /// 检验结果（合格、不合格、部分合格）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InspectionResult { get; set; } = "合格";

        /// <summary>
        /// 不合格原因
        /// </summary>
        [StringLength(500)]
        public string? UnqualifiedReason { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 