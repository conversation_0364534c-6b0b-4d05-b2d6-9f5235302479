using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 工序实体
    /// </summary>
    [Table("Process")]
    public class Process : BaseEntity
    {
        /// <summary>
        /// 工序编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProcessCode { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ProcessName { get; set; }

        /// <summary>
        /// 工序分类ID
        /// </summary>
        public long ProcessClassificationId { get; set; }
        /// <summary>
        /// 部门ID
        /// </summary>
        public long DepartmentId { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        [StringLength(50)]
        public string ResponsiblePerson { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; } = 1;

        /// <summary>
        /// 标准时间
        /// </summary>
        public int StandardTime { get; set; }

        /// <summary>
        /// 状态 (启用/禁用)
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; }
    }
}

