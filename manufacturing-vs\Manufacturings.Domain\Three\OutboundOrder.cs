using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 出库单实体
    /// </summary>
    [Table("OutboundOrder")]
    public class OutboundOrder : BaseEntity
    {
        /// <summary>
        /// 出库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundOrderNumber { get; set; }

        /// <summary>
        /// 出库主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OutboundSubject { get; set; }

        /// <summary>
        /// 出库日期
        /// </summary>
        public DateTime OutboundDate { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundType { get; set; }

        /// <summary>
        /// 出库仓库
        /// </summary>
        [StringLength(100)]
        public string OutboundWarehouse { get; set; }

        /// <summary>
        /// 仓库ID
        /// </summary>
        public long WarehouseId { get; set; }

        /// <summary>
        /// 申领人员
        /// </summary>
        [StringLength(50)]
        public string Applicant { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [StringLength(100)]
        public string Department { get; set; }

        /// <summary>
        /// 出库人员
        /// </summary>
        [StringLength(50)]
        public string OutboundPersonnel { get; set; }

        /// <summary>
        /// 状态 (未完成/已完成)
        /// </summary>
        [StringLength(20)]
        public string Status { get; set; } = "未完成";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remarks { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; }
    }
}

