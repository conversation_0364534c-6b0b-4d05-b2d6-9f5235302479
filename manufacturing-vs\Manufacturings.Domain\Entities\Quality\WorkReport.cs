using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Quality
{
    /// <summary>
    /// 质检单表
    /// </summary>
    [Table("WorkReport")]
    public class WorkReport : BaseEntity
    {
        /// <summary>
        /// 关联质检工单ID
        /// </summary>
        public long? OrderId { get; set; }

        /// <summary>
        /// 关联工序ID
        /// </summary>
        public long? OperationId { get; set; }

        /// <summary>
        /// 生产物品ID
        /// </summary>
        public long? ProductId { get; set; }

        /// <summary>
        /// 报工人员ID
        /// </summary>
        public long? ReporterId { get; set; }

        /// <summary>
        /// 报工时间
        /// </summary>
        public DateTime? ReportTime { get; set; }

        /// <summary>
        /// 完工数量
        /// </summary>
        public int? CompletedQty { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的质检工单
        /// </summary>
        [ForeignKey("OrderId")]
        public virtual QualityOrder QualityOrder { get; set; }
    }
}
