# 仓库管理系统

## 功能概述

仓库管理系统是一个完整的仓库信息管理解决方案，支持仓库的增删改查、树形结构管理、分类管理等功能。

## 主要功能

### 1. 仓库列表管理
- 支持树形结构显示仓库层级关系
- 支持搜索、筛选、分页
- 支持批量操作
- 支持导出功能

### 2. 仓库新增
- 支持选择上级仓库（树形结构）
- 支持选择仓库分类、存储类型、仓库结构
- 支持选择负责人
- 支持系统自动生成编号或手动输入
- 支持设置启用/禁用状态

### 3. 仓库编辑
- 支持修改所有仓库信息
- 支持根节点特殊编辑（仅名称和备注）
- 防止循环引用（不能选择自己或子仓库作为上级）

### 4. 仓库详情查看
- 显示完整的仓库信息
- 支持从详情页直接编辑或删除
- 显示创建和修改记录

### 5. 仓库删除
- 支持软删除
- 检查是否有子仓库
- 检查是否有关联数据

## 技术架构

### 前端技术栈
- Vue 3 + TypeScript
- Element Plus UI组件库
- Vue Router 路由管理
- Pinia 状态管理
- Axios HTTP客户端

### 后端技术栈
- .NET 6
- Entity Framework Core
- MediatR CQRS模式
- 分层架构（API、Application、Domain、Infrastructure）

## 文件结构

```
manufacturing-vue/src/views/Warehouse/
├── WarehouseManagement.vue          # 主页面
├── components/
│   ├── WarehouseDialog.vue          # 新增/编辑对话框
│   ├── WarehouseDetailDialog.vue    # 详情对话框
│   └── RootNodeDialog.vue           # 根节点编辑对话框
└── README.md                        # 说明文档

manufacturing-vs/ManufacturingsERP.API/
├── Controllers/
│   └── WarehouseController.cs       # 仓库控制器
├── Application/
│   ├── DTOs/
│   │   └── WarehouseDto.cs         # 数据传输对象
│   └── Handlers/Warehouse/          # 命令查询处理器
│       ├── CreateWarehouseCommandHandler.cs
│       ├── UpdateWarehouseCommandHandler.cs
│       ├── DeleteWarehouseCommandHandler.cs
│       ├── GetWarehouseListQueryHandler.cs
│       └── GetWarehouseByIdQueryHandler.cs
└── Application/Interfaces/
    └── IWarehouseRepository.cs      # 仓库仓储接口
```

## API接口

### 1. 获取仓库列表
```
GET /api/Warehouse/list
参数：
- page: 页码
- pageSize: 每页大小
- keyword: 搜索关键词
- categoryId: 仓库分类ID
- storageTypeId: 存储类型ID
- isEnabled: 状态
```

### 2. 新增仓库
```
POST /api/Warehouse
Body: CreateWarehouseCommand
```

### 3. 更新仓库
```
PUT /api/Warehouse
Body: UpdateWarehouseCommand
```

### 4. 删除仓库
```
DELETE /api/Warehouse/{id}
```

### 5. 获取仓库详情
```
GET /api/Warehouse/{id}
```

## 使用说明

### 1. 访问仓库管理
在系统菜单中点击"仓库管理"进入仓库管理页面。

### 2. 新增仓库
1. 点击"新增"按钮
2. 填写仓库信息
3. 选择上级仓库（可选）
4. 选择仓库分类（必填）
5. 选择存储类型、仓库结构、负责人（可选）
6. 填写仓库地址和备注
7. 设置状态
8. 点击"确定"保存

### 3. 编辑仓库
1. 在仓库列表中点击"编辑"按钮
2. 修改需要更新的信息
3. 点击"确定"保存

### 4. 查看仓库详情
1. 在仓库列表中点击"查看"按钮
2. 查看完整的仓库信息
3. 可以点击"编辑"或"删除"进行相应操作

### 5. 删除仓库
1. 在仓库列表中点击"删除"按钮
2. 确认删除操作
3. 系统会检查是否可以删除

### 6. 搜索和筛选
1. 使用搜索框搜索仓库名称、编号或地址
2. 使用筛选条件按分类、类型、状态等筛选

## 注意事项

1. **根节点特殊处理**：根节点仓库只能编辑名称和备注，不能修改其他属性
2. **循环引用检查**：更新仓库时，系统会检查是否形成循环引用
3. **删除限制**：有子仓库的仓库不能删除
4. **编号唯一性**：仓库编号必须唯一，系统会自动检查
5. **软删除**：删除操作采用软删除，数据不会物理删除

## 扩展功能

### 1. 批量操作
- 支持批量启用/禁用
- 支持批量删除
- 支持批量导出

### 2. 高级搜索
- 支持多条件组合搜索
- 支持模糊搜索
- 支持范围搜索

### 3. 数据导入
- 支持Excel导入
- 支持批量创建仓库
- 支持数据验证

### 4. 权限控制
- 基于角色的权限控制
- 支持细粒度权限设置
- 支持操作日志记录

## 常见问题

### Q: 为什么无法删除某个仓库？
A: 可能的原因：
1. 该仓库下有子仓库
2. 该仓库有关联的库存记录
3. 该仓库是根节点

### Q: 如何修改根节点名称？
A: 点击根节点的"编辑"按钮，会打开特殊的根节点编辑对话框，只能修改名称和备注。

### Q: 仓库编号可以重复吗？
A: 不可以，仓库编号必须唯一。系统会自动检查并提示错误。

### Q: 支持多少层级的仓库结构？
A: 理论上支持无限层级，但建议控制在5层以内以保证系统性能。

## 联系支持

如有问题或建议，请联系系统管理员或开发团队。 