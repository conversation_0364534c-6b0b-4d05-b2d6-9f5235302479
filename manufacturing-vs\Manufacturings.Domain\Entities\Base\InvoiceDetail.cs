using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用发票明细表
    /// </summary>
    public class InvoiceDetail : BaseEntity
    {
        /// <summary>
        /// 发票记录ID
        /// </summary>
        [Required]
        public long InvoiceRecordId { get; set; }

        /// <summary>
        /// 商品ID
        /// </summary>
        [Required]
        public long ItemId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ItemName { get; set; } = string.Empty;

        /// <summary>
        /// 商品规格
        /// </summary>
        [StringLength(200)]
        public string? ItemSpecification { get; set; }

        /// <summary>
        /// 商品单位
        /// </summary>
        [StringLength(20)]
        public string? ItemUnit { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,4)")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 折扣率
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal DiscountRate { get; set; } = 1.0000m;

        /// <summary>
        /// 折扣金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; }

        /// <summary>
        /// 不含税金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountExcludingTax { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; }

        /// <summary>
        /// 含税金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal AmountIncludingTax { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 