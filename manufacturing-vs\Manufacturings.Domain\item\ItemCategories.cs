using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.item
{
    /// <summary>
    /// 物品分类表
    /// </summary>
    public class ItemCategories : BaseEntity
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        public string? CategoryName { get; set; }

        /// <summary>
        /// 父级ID
        /// </summary>
        public long? CategoriesParentId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Categoriesstate { get; set; }
    }
}
