<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
</head>
<body>
    <h1>仓储管理API测试</h1>
    <div id="results"></div>
    
    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('开始测试API...');
                
                // 测试仓库列表API
                const response = await fetch('http://localhost:5107/api/Warehouse/list?page=1&pageSize=10', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('API响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应数据:', data);
                    resultsDiv.innerHTML = `
                        <h2>API调用成功</h2>
                        <p>状态码: ${response.status}</p>
                        <p>响应数据:</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <h2>API调用失败</h2>
                        <p>状态码: ${response.status}</p>
                        <p>错误信息: ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                console.error('API调用错误:', error);
                resultsDiv.innerHTML = `
                    <h2>API调用错误</h2>
                    <p>错误信息: ${error.message}</p>
                `;
            }
        }
        
        // 页面加载后自动测试
        window.onload = testAPI;
    </script>
</body>
</html>
