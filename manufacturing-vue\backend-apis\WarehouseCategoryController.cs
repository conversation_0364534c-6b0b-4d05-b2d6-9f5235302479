using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Manufacturings.Infrastructrue;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Infrastructrue.Error;

namespace ManufacturingsERP.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WarehouseCategoryController : ControllerBase
    {
        private readonly MyDbContext _context;

        public WarehouseCategoryController(MyDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取仓库分类列表
        /// </summary>
        /// <param name="pageSize">每页大小</param>
        /// <param name="page">页码</param>
        /// <param name="keyword">搜索关键词</param>
        /// <returns></returns>
        [HttpGet("list")]
        public async Task<IActionResult> GetWarehouseCategoryList(
            int pageSize = 20,
            int page = 1,
            string keyword = "")
        {
            try
            {
                var query = _context.WarehouseCategories.AsQueryable();

                // 搜索过滤
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.CategoryName.Contains(keyword) ||
                                           (x.Description != null && x.Description.Contains(keyword)));
                }

                // 总数统计
                var totalCount = await query.CountAsync();
                var pageCount = (int)Math.Ceiling((double)totalCount / pageSize);

                // 分页查询
                var categories = await query
                    .OrderBy(x => x.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(x => new
                    {
                        x.Id,
                        x.CategoryName,
                        x.Description,
                        x.IsEnabled,
                        x.CreateTime,
                        x.CreateUser,
                        x.ModificationTime,
                        x.ModifierName
                    })
                    .ToListAsync();

                return Ok(new APIResult<APIPageing<object>>
                {
                    Code = ResultCode.Success,
                    Message = "查询成功",
                    Data = new APIPageing<object>
                    {
                        TotalCount = totalCount,
                        PageCount = pageCount,
                        PageData = categories
                    }
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new APIResult<APIPageing<object>>
                {
                    Code = ResultCode.Fail,
                    Message = $"查询失败: {ex.Message}",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 获取所有仓库分类（不分页）
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetAllWarehouseCategories()
        {
            try
            {
                var categories = await _context.WarehouseCategories
                    .Where(x => x.IsEnabled)
                    .OrderBy(x => x.Id)
                    .Select(x => new
                    {
                        x.Id,
                        x.CategoryName,
                        x.Description
                    })
                    .ToListAsync();

                return Ok(new APIResult<object>
                {
                    Code = ResultCode.Success,
                    Message = "查询成功",
                    Data = categories
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new APIResult<object>
                {
                    Code = ResultCode.Fail,
                    Message = $"查询失败: {ex.Message}",
                    Data = null
                });
            }
        }
    }
}
