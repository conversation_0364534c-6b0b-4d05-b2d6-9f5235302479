using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 客户出库记录表
    /// </summary>
    public class CustomerOutboundRecord : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        public long CustomerId { get; set; }

        /// <summary>
        /// 出库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 出库订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OutboundOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 出库日期
        /// </summary>
        [Required]
        public DateTime OutboundDate { get; set; }

        /// <summary>
        /// 出库类型
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundType { get; set; } = string.Empty;

        /// <summary>
        /// 出库总量
        /// </summary>
        public int TotalOutboundQuantity { get; set; }

        /// <summary>
        /// 出库金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal OutboundAmount { get; set; }

        /// <summary>
        /// 出库人ID
        /// </summary>
        public long? OutboundPersonId { get; set; }
    }
} 