﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Write.Application.Command.User;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Write.Application.Handler.User
{
    public class DeleteUserCommandHandler : IRequestHandler<DeleteUserCommand, APIResult<object>>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserModel> _userRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> _userRoleRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;
        private readonly ILogger<DeleteUserCommandHandler> _logger;

        public DeleteUserCommandHandler(IMapper mapper, ILogger<DeleteUserCommandHandler> logger, IBaseRepository<UserModel> userRepository, IBaseRepository<UserRoleModel> userRoleRepository)
        {
            _mapper = mapper;
            _logger = logger;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public async Task<APIResult<object>> Handle(DeleteUserCommand request, CancellationToken cancellationToken)
        {
            APIResult<object> result = new APIResult<object>();
            result.Code = ResultCode.Success;
            result.Message = "删除成功";
            /* 为什么使用执行策略？
             * 原因是：
                MySQL 配置了重试策略，但您直接在代码中开启了事务
                当使用重试执行策略时，不能直接创建事务，而必须通过策略来包装事务操作
                如果直接创建事务，在重试时可能会导致数据不一致或事务状态混乱
             * 使用异步是为了不阻塞线程
             */
            //创建执行策略
            var strategy = _userRepository.Context.Database.CreateExecutionStrategy();

            //在执行策略中使用事务
            await strategy.ExecuteAsync(async () =>
            {
                using (var tr = await _userRepository.Context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        //软删除用户
                        var user = _mapper.Map<UserModel>(request);
                        user.IsDeleted = true;
                        await _userRepository.UpdateAsync(user);
                        //先删除用户的角色
                        var ur = _userRoleRepository.GetAll().Where(x => x.UserId == user.Id).ToList();
                        foreach (var role in ur)
                        {
                            await _userRoleRepository.DeleteAsync(role);
                        }
                        _logger.LogInformation($"用户及其角色删除成功！");
                        await tr.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        result.Code = ResultCode.Fail;
                        result.Message = "删除失败";
                        _logger.LogError($"删除用户时时出现异常:{ex.Message}");
                        //回滚
                        await tr.RollbackAsync();
                    }
                }
            });
            return result;
        }
    }
}
