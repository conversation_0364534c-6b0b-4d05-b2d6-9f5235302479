using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 委外加工入库记录表
    /// </summary>
    public class OutsourcedProcessingInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 委外加工单号
        /// </summary>
        [StringLength(50)]
        public string? OutsourcedProcessingOrderNumber { get; set; }

        /// <summary>
        /// 委外加工商ID
        /// </summary>
        public int? OutsourcedProcessorId { get; set; }

        /// <summary>
        /// 委外加工日期
        /// </summary>
        public DateTime? OutsourcedProcessingDate { get; set; }

        /// <summary>
        /// 加工费用
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? ProcessingFee { get; set; }

        /// <summary>
        /// 加工质量评级
        /// </summary>
        public QualityRating? QualityRating { get; set; }

        /// <summary>
        /// 质检人员ID
        /// </summary>
        public int? QualityInspectorId { get; set; }

        /// <summary>
        /// 质检日期
        /// </summary>
        public DateTime? QualityInspectionDate { get; set; }

        /// <summary>
        /// 委外加工备注
        /// </summary>
        [StringLength(500)]
        public string? OutsourcedProcessingRemarks { get; set; }
    }
} 