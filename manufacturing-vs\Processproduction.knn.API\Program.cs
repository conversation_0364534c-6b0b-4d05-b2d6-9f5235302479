﻿using IGeekFan.AspNetCore.Knife4jUI;
using Processproduction.knn.API.Extensions;

var builder = WebApplication.CreateBuilder(args).Inject();

// Add services to the container.


var app = builder.Build();
// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{

    //app.UseSwaggerUI();
}
app.UseSwagger();

app.UseKnife4UI();
//
app.UseCors(x => x.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
// ..... 
app.UseAuthentication(); // ֤
app.UseAuthorization(); // 

app.MapControllers();

app.Run();
