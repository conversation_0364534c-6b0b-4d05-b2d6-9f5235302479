﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Read.Application.Command.Role;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.Role
{
    public class RoleQueryCommandHandler : IRequestHandler<RoleQueryCommand, APIResult<APIPageing<RoleDto>>>
    {
        private readonly IBaseRepository<RoleModel> roleRepository;
        private readonly IBaseRepository<RolePermissionModel> rolePermissionRepository;
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;

        public RoleQueryCommandHandler(IBaseRepository<RoleModel> roleRepository,IBaseRepository<RolePermissionModel> rolePermissionRepository,IBaseRepository<PermissionModel> permissionRepository,IMapper mapper)
        {
            this.roleRepository = roleRepository;
            this.rolePermissionRepository = rolePermissionRepository;
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
        }

        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public Task<APIResult<APIPageing<RoleDto>>> Handle(RoleQueryCommand request, CancellationToken cancellationToken)
        {
            APIResult<APIPageing<RoleDto>> result = new APIResult<APIPageing<RoleDto>>();
            result.Code = ResultCode.Success;
            result.Message = "查询成功";
            //获取角色列表
            var list = roleRepository.GetAll().AsNoTracking();
            //模糊查询
            if (request.RoleName != null)
            {
                list = list.Where(x=>x.RoleName.Contains(request.RoleName));
            }
            var roleDto = mapper.Map<List<RoleDto>>(list);
            //分页
            roleDto = roleDto.OrderBy(x => x.Id).Skip((request.PageIndex - 1) * request.PageSize).Take(request.PageSize).ToList();
            var totalCount = list.Count();
            var pageCount = (int)Math.Ceiling((totalCount * 1.0) / request.PageSize);

            // 获取所有角色ID
            var roleIds = roleDto.Select(x => x.Id).ToList();
            // 一次性获取所有角色权限关系与权限名称
            var rolePermissions = (from rp in rolePermissionRepository.GetAll()
                                   join p in permissionRepository.GetAll() on rp.PermissionId equals p.Id
                                   where roleIds.Contains(rp.RoleId)
                                   select new
                                   {
                                       RoleId = rp.RoleId,
                                       PermissionId = rp.PermissionId,
                                       PermissionName = p.PermissionName
                                   }).ToList();

            // 为每个角色填充权限信息
            foreach (var item in roleDto)
            {
                var permissions = rolePermissions.Where(x => x.RoleId == item.Id);
                item.PermissionId = permissions.Select(x => x.PermissionId).ToList();
                item.PermissionName = string.Join(",", permissions.Select(x => x.PermissionName));
            }
            APIPageing<RoleDto> pageing = new APIPageing<RoleDto>();
            pageing.TotalCount = totalCount;
            pageing.PageCount = pageCount;
            pageing.PageData = roleDto;

            result.Data = pageing;
            return Task.FromResult(result);
        }
    }
}
