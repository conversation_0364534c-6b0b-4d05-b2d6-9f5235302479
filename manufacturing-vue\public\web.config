<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<system.webServer>
		<rewrite>
			<rules>
				<rule name="Handle History Mode and custom 404/500" stopProcessing="true">
					<match url="(.*)" />
					<conditions logicalGrouping="MatchAll">
						<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
						<add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
					</conditions>
					<action type="Rewrite" url="/index.html" />
				</rule>
				<!-- 保留重定向规则 -->
				<rule name="Redirect product detail URLs" stopProcessing="true">
					<match url="^product/([0-9]+)$" />
					<action type="Redirect" url="/product/detail/{R:1}" redirectType="Permanent" />
				</rule>
				<rule name="Redirect plain IDs" stopProcessing="true">
					<match url="^([0-9]+)$" />
					<action type="Redirect" url="/product/detail/{R:1}" redirectType="Permanent" />
				</rule>
			</rules>
		</rewrite>
		<httpErrors>
			<remove statusCode="404" subStatusCode="-1" />
			<remove statusCode="500" subStatusCode="-1" />
			<error statusCode="404" path="/index.html" responseMode="ExecuteURL" />
			<error statusCode="500" path="/index.html" responseMode="ExecuteURL" />
		</httpErrors>
		<!-- 移除可能导致冲突的staticContent配置 -->
		<!-- IIS可能已经有.json的MIME类型映射，所以我们使用remove先移除再添加 -->
		<staticContent>
			<remove fileExtension=".json" />
			<mimeMap fileExtension=".json" mimeType="application/json" />
		</staticContent>
		<!-- 保留CORS配置 -->
		<httpProtocol>
			<customHeaders>
				<add name="Access-Control-Allow-Origin" value="*" />
				<add name="Access-Control-Allow-Methods" value="GET,POST,OPTIONS" />
				<add name="Access-Control-Allow-Headers" value="Content-Type" />
			</customHeaders>
		</httpProtocol>
	</system.webServer>
</configuration>
