using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 生产物品表
    /// </summary>
    public class ProductionItems : BaseEntity
    {
        /// <summary>
        /// 物品Id
        /// </summary>
        public long? GoodsId { get; set; }

        /// <summary>
        /// 内部生产Id
        /// </summary>
        public long? ProductionId { get; set; }

        /// <summary>
        /// 计划生产数量
        /// </summary>
        public decimal? Plannedproduction { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
