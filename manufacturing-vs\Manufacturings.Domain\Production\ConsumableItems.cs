using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 耗材物品表
    /// </summary>
    public class ConsumableItems : BaseEntity
    {
        /// <summary>
        /// 物品ID
        /// </summary>
        public long? GoodsId { get; set; }

        /// <summary>
        /// 内部生产ID
        /// </summary>
        public long? interiorId { get; set; }

        /// <summary>
        /// 仓库
        /// </summary>
        public long? warehouseId { get; set; }

        /// <summary>
        /// 计划消耗数量
        /// </summary>
        public decimal? Plannedquantity { get; set; }

        /// <summary>
        /// 主单位数量
        /// </summary>
        public decimal? Number { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
