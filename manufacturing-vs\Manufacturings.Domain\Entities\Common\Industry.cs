using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 行业表
    /// </summary>
    public class Industry : BaseEntity
    {
        /// <summary>
        /// 行业名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string IndustryName { get; set; } = string.Empty;

        /// <summary>
        /// 行业编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string IndustryCode { get; set; } = string.Empty;

        /// <summary>
        /// 行业描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 