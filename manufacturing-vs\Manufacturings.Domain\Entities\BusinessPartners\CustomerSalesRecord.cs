using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 客户销售记录表
    /// </summary>
    public class CustomerSalesRecord : BaseEntity
    {
        /// <summary>
        /// 客户ID
        /// </summary>
        [Required]
        public long CustomerId { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SalesOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 销售订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string SalesOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 销售日期
        /// </summary>
        [Required]
        public DateTime SalesDate { get; set; }

        /// <summary>
        /// 物品概要
        /// </summary>
        [StringLength(500)]
        public string? ItemSummary { get; set; }

        /// <summary>
        /// 客户经理ID
        /// </summary>
        public long? CustomerManagerId { get; set; }

        /// <summary>
        /// 所在部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }
    }
} 