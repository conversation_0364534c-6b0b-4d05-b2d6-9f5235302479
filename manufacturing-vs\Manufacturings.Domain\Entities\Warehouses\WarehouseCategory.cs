using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Warehouses
{
    /// <summary>
    /// 仓库分类表
    /// </summary>
    public class WarehouseCategory : BaseEntity
    {
        /// <summary>
        /// 分类名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 分类编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// 分类描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 