﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Traceability.API.Read.Application.Command.Role;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.User
{
    public class UserQueryCommandHandler : IRequestHandler<UserQueryCommand, APIResult<APIPageing<UserDto>>>
    {
        /// <summary>
        /// 用户仓储
        /// </summary>
        private readonly IBaseRepository<UserModel> _userRepository;
        /// <summary>
        /// 用户角色仓储
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> _userRoleRepository;
        /// <summary>
        /// 角色仓储
        /// </summary>
        private readonly IBaseRepository<RoleModel> _roleRepository;
        /// <summary>
        /// 映射
        /// </summary>
        private readonly IMapper mapper;
        /// <summary>
        /// 日志服务
        /// </summary>
        private readonly ILogger<UserQueryCommandHandler> _logger;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="mapper">映射</param>
        /// <param name="userRepository">用户仓储</param>
        /// <param name="userRoleRepository">用户角色仓储</param>
        /// <param name="roleRepository">角色仓储</param>
        /// <param name="logger">日志服务</param>
        public UserQueryCommandHandler(
            IMapper mapper, 
            IBaseRepository<UserModel> userRepository, 
            IBaseRepository<UserRoleModel> userRoleRepository, 
            IBaseRepository<RoleModel> roleRepository,
            ILogger<UserQueryCommandHandler> logger)
        {
            this.mapper = mapper;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
            _roleRepository = roleRepository;
            _logger = logger;
        }

        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public Task<APIResult<APIPageing<UserDto>>> Handle(UserQueryCommand request, CancellationToken cancellationToken)
        {
            APIResult<APIPageing<UserDto>> result = new APIResult<APIPageing<UserDto>>();
            
            try
            {
                result.Code = ResultCode.Success;
                result.Message = "查询成功";
                //获取用户列表
                var list = _userRepository.GetAll().AsNoTracking();
                //模糊查询
                if (request.NickName != null)
                {
                    list = list.Where(x => x.Name.Contains(request.NickName));
                }
                var userDto = mapper.Map<List<UserDto>>(list);

                //分页
                userDto = userDto.OrderBy(x => x.Id).Skip((request.PageIndex - 1) * request.PageSize).Take(request.PageSize).ToList();
                var totalCount = list.Count();
                var pageCount = (int)Math.Ceiling((totalCount * 1.0) / request.PageSize);

                var userIdList = list.Select(x => x.Id).ToList();
                // 1. 一次性获取所有用户角色关系
                var userRoles = _userRoleRepository.GetAll()
                    .Where(x => userIdList.Contains(x.UserId))
                    .Select(x => new { x.UserId, x.RoleId })
                    .ToList();

                // 2. 获取所有涉及的角色ID
                var roleIds = userRoles.Select(x => x.RoleId).Distinct().ToList();

                // 3. 一次性获取所有角色名称
                var roles = _roleRepository.GetAll()
                    .Where(x => roleIds.Contains(x.Id))
                    .Select(x => new { x.Id, x.RoleName })
                    .ToList();

                // 4. 处理用户数据
                foreach (var item in userDto)
                {
                    var userRoleIds = userRoles
                        .Where(x => x.UserId == item.Id)
                        .Select(x => x.RoleId)
                        .ToList();

                    item.RoleId = userRoleIds;

                    var userRoleNames = roles
                        .Where(x => userRoleIds.Contains(x.Id))
                        .Select(x => x.RoleName);

                    item.RoleName = string.Join(",", userRoleNames);
                }
                APIPageing<UserDto> pageing = new APIPageing<UserDto>();
                pageing.TotalCount = totalCount;
                pageing.PageCount = pageCount;
                pageing.PageData = userDto;

                result.Data = pageing;
                
                _logger.LogInformation($"用户查询成功，返回{userDto.Count}条记录，总记录数：{totalCount}");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"用户查询过程中出现异常：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "查询失败，服务器异常";
                return Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
