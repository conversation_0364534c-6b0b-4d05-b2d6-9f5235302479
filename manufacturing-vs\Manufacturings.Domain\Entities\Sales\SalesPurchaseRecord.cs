using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售采购记录表
    /// </summary>
    public class SalesPurchaseRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 采购单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PurchaseOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 采购主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string PurchaseSubject { get; set; } = string.Empty;

        /// <summary>
        /// 采购日期
        /// </summary>
        [Required]
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Supplier { get; set; } = string.Empty;

        /// <summary>
        /// 采购人员ID
        /// </summary>
        public long? PurchasingAgentId { get; set; }

        /// <summary>
        /// 采购部门ID
        /// </summary>
        public long? PurchasingDepartmentId { get; set; }

        /// <summary>
        /// 物品概要
        /// </summary>
        [StringLength(500)]
        public string? ItemSummary { get; set; }

        /// <summary>
        /// 采购总数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPurchaseQuantity { get; set; }

        /// <summary>
        /// 采购总金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPurchaseAmount { get; set; }

        /// <summary>
        /// 采购状态（待采购、采购中、已完成、已取消等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PurchaseStatus { get; set; } = "待采购";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 