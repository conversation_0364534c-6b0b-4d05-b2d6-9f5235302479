using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.Common;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 供应商主表（继承自BusinessPartner）
    /// </summary>
    public class Supplier : BusinessPartner
    {
        /// <summary>
        /// 供应商编号（系统自动生成）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string SupplierNumber { get; set; } = string.Empty;

        /// <summary>
        /// 供应商名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string SupplierName { get; set; } = string.Empty;

        /// <summary>
        /// 供应商等级ID（使用BusinessPartner的PartnerLevelId）
        /// </summary>
        [NotMapped]
        public long? SupplierLevelId => PartnerLevelId;

        /// <summary>
        /// 供应商类别ID（使用BusinessPartner的PartnerCategoryId）
        /// </summary>
        [NotMapped]
        public long? SupplierCategoryId => PartnerCategoryId;

        /// <summary>
        /// 供应商类型（生产商/代理商/服务商）
        /// </summary>
        [Required]
        public SupplierType SupplierType { get; set; } = SupplierType.Manufacturer;

        /// <summary>
        /// 供应商状态
        /// </summary>
        [Required]
        public SupplierStatus SupplierStatus { get; set; } = SupplierStatus.Active;

        /// <summary>
        /// 采购代表ID
        /// </summary>
        public long? PurchaseRepresentativeId { get; set; }

        /// <summary>
        /// 供应商来源
        /// </summary>
        [StringLength(50)]
        public string? SupplierSource { get; set; }

        /// <summary>
        /// 合作开始时间
        /// </summary>
        public DateTime? CooperationStartDate { get; set; }

        /// <summary>
        /// 合作结束时间
        /// </summary>
        public DateTime? CooperationEndDate { get; set; }

        /// <summary>
        /// 付款条件（天数）
        /// </summary>
        public int? PaymentTerms { get; set; }

        /// <summary>
        /// 信用等级（A/B/C/D）
        /// </summary>
        [StringLength(1)]
        public string? CreditRating { get; set; }

        /// <summary>
        /// 是否战略合作伙伴
        /// </summary>
        public bool IsStrategicPartner { get; set; } = false;

        /// <summary>
        /// 是否优先供应商
        /// </summary>
        public bool IsPreferredSupplier { get; set; } = false;
    }

    /// <summary>
    /// 供应商类型枚举
    /// </summary>
    public enum SupplierType
    {
        /// <summary>
        /// 生产商
        /// </summary>
        Manufacturer = 1,
        /// <summary>
        /// 代理商
        /// </summary>
        Agent = 2,
        /// <summary>
        /// 服务商
        /// </summary>
        ServiceProvider = 3
    }

    /// <summary>
    /// 供应商状态枚举
    /// </summary>
    public enum SupplierStatus
    {
        /// <summary>
        /// 活跃
        /// </summary>
        Active = 1,
        /// <summary>
        /// 非活跃
        /// </summary>
        Inactive = 2,
        /// <summary>
        /// 黑名单
        /// </summary>
        Blacklisted = 3,
        /// <summary>
        /// 暂停合作
        /// </summary>
        Suspended = 4
    }
} 