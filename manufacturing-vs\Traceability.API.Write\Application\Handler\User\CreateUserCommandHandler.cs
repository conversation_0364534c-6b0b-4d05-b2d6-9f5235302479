﻿using AutoMapper;
using MD5Hash;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Traceability.API.Write.Application.Command.Role;
using Traceability.API.Write.Application.Command.User;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;
using Yitter.IdGenerator;

namespace Traceability.API.Write.Application.Handler.User
{

    public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, APIResult<object>>
    {
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserModel> _userRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> _userRoleRepository;
        /// <summary>
        /// 
        /// </summary>
        private readonly IMapper _mapper;
        private readonly ILogger<CreateUserCommandHandler> _logger;
        private readonly IIdentifyService identifyService;

        public CreateUserCommandHandler(IMapper mapper,
                                        ILogger<CreateUserCommandHandler> logger,
                                        IBaseRepository<UserModel> userRepository,
                                        IBaseRepository<UserRoleModel> userRoleRepository,
                                        IIdentifyService identifyService)
        {
            _mapper = mapper;
            _logger = logger;
            _userRepository = userRepository;
            _userRoleRepository = userRoleRepository;
            this.identifyService = identifyService;
        }
        /// <summary>
        /// 处理
        /// </summary>
        /// <param name="request">请求</param>
        /// <param name="cancellationToken">取消</param>
        /// <returns>返回任务</returns>
        public async Task<APIResult<object>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
        {
            APIResult<object> result = new APIResult<object>();
            result.Code = ResultCode.Success;
            result.Message = "添加成功";

            // 验证用户名是否已存在
            var existingUser = _userRepository.GetAll().FirstOrDefault(x => x.Username == request.Username);
            if (existingUser != null)
            {
                _logger.LogWarning($"创建用户失败：用户名已存在，用户名：{request.Username}");
                result.Code = ResultCode.Fail;
                result.Message = "你要添加的用户名已存在";
                return result;
            }

            /* 为什么使用执行策略？
             * 原因是：
                MySQL 配置了重试策略，但是由于直接在代码中开启了事务
                当使用重试执行策略时，不能直接创建事务，而必须通过策略来包装事务操作
                如果直接创建事务，在重试时可能会导致数据不一致或事务状态混乱
             * 使用异步是为了不阻塞线程
             */

            //创建执行策略
            var strategy = _userRepository.Context.Database.CreateExecutionStrategy();

            //在执行策略中使用事务
            await strategy.ExecuteAsync(async () =>
            {
                using (var tr = await _userRepository.Context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        //添加用户
                        var user = _mapper.Map<UserModel>(request);
                        user.Password = request.Password.GetMD5();
                        user.CreateId = 1;
                        user.CreateTime = DateTime.Now;
                        await _userRepository.AddAsync(user);

                        //添加用户角色
                        foreach (var roleId in request.RoleId)
                        {
                            UserRoleModel userRole = new UserRoleModel()
                            { 
                                Id = YitIdHelper.NextId(),
                                RoleId = roleId,
                                UserId = user.Id,
                                CreateTime = DateTime.Now,
                                CreateId = Convert.ToInt64(identifyService.UserId)
                            };
                            await _userRoleRepository.AddAsync(userRole);
                        }
                        _logger.LogInformation($"用户及其角色添加成功！");
                        await tr.CommitAsync();
                    }
                    catch (Exception ex)
                    {
                        result.Code = ResultCode.Fail;
                        result.Message = "添加失败";
                        _logger.LogError($"添加用户时时出现异常:{ex.Message}");
                        //回滚
                        await tr.RollbackAsync();
                    }
                    finally
                    {
                        
                    }
                }
            });
            return result;
        }
    }
}
