{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": { "MySQL": "Server=localhost;uid=root;pwd=******;database=Traceability2211A" },
  "JwtSettings": {
    "SecretKey": "1qaz2wsx3edc4rfv5tgb6yhn7ujm8ikl", //私钥
    "Issuer": "六百六十六", //发行者
    "Audience": "六百六十六", //接收者
    "AccessTokenExpirationMinutes": 120, //授权Token过期时间
    "RefreshExpireTime": 1200 //续期Token过期时间
  },
  //服务发现
  "Consul": {
    //服务发现地址
    "Address": "http://************:8500",
    //服务发现数据中心
    "Datacenter": "dc1",
    //API服务名称
    "ServiceName": "Traceability.API.Read",
    //API服务地址
    "ServiceAddress": "************",
    //API服务端口
    "ServicePort": "26543"
  }

}
