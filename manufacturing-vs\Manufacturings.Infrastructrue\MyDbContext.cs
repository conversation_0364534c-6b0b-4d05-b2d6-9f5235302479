﻿using Microsoft.EntityFrameworkCore;
using Manufacturings.Domain;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Inventory;
using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Base;
using Manufacturings.Domain.Entities.Quality;
using Manufacturings.Domain.Entities.Sales;
using Manufacturings.Domain.Entities.Purchase;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain.Entities.Inbound;
using Manufacturings.Domain.Production;
using Manufacturings.Domain.BOM;
using Manufacturings.Domain.item;
using Manufacturings.Domain.Three;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Manufacturings.Infrastructrue
{
    public class MyDbContext : DbContext
    {
        public MyDbContext(DbContextOptions options) : base(options)
        {
        }

        // Common Entities
        public DbSet<Address> Addresses { get; set; }
        public DbSet<Person> People { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<PersonnelScale> PersonnelScales { get; set; }
        public DbSet<Location> Locations { get; set; }
        public DbSet<BusinessNature> BusinessNatures { get; set; }
        public DbSet<Industry> Industries { get; set; }

        // Inventory Entities
        public DbSet<ItemCategory> ItemCategories { get; set; }
        public DbSet<ItemAttribute> ItemAttributes { get; set; }
        public DbSet<ItemType> ItemTypes { get; set; }

        // Business Partners Entities
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<SupplierItem> SupplierItems { get; set; }
        public DbSet<SupplierInboundRecord> SupplierInboundRecords { get; set; }
        public DbSet<SupplierPurchaseRecord> SupplierPurchaseRecords { get; set; }
        public DbSet<CustomerSalesRecord> CustomerSalesRecords { get; set; }
        public DbSet<CustomerOutboundRecord> CustomerOutboundRecords { get; set; }
        public DbSet<CustomerDeliveryAddress> CustomerDeliveryAddresses { get; set; }

        // Sales Entities
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderDetail> SalesOrderDetails { get; set; }
        public DbSet<SalesOutboundRecord> SalesOutboundRecords { get; set; }
        public DbSet<SalesOutboundDetail> SalesOutboundDetails { get; set; }
        public DbSet<SalesReturnRecord> SalesReturnRecords { get; set; }
        public DbSet<SalesReturnDetail> SalesReturnDetails { get; set; }
        public DbSet<SalesReturnInboundRecord> SalesReturnInboundRecords { get; set; }
        public DbSet<SalesPurchaseRecord> SalesPurchaseRecords { get; set; }
        public DbSet<SalesPurchaseDetail> SalesPurchaseDetails { get; set; }
        public DbSet<SalesProductionRecord> SalesProductionRecords { get; set; }
        public DbSet<SalesProductionDetail> SalesProductionDetails { get; set; }
        public DbSet<SalesPaymentRecord> SalesPaymentRecords { get; set; }
        public DbSet<SalesInvoiceRecord> SalesInvoiceRecords { get; set; }
        public DbSet<SalesInvoiceDetail> SalesInvoiceDetails { get; set; }

        // Purchase Entities
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderDetail> PurchaseOrderDetails { get; set; }
        public DbSet<PurchaseOrderReturnRecord> PurchaseOrderReturnRecords { get; set; }
        public DbSet<PurchaseOrderReturnDetail> PurchaseOrderReturnDetails { get; set; }
        public DbSet<PurchaseOrderInspectionRecord> PurchaseOrderInspectionRecords { get; set; }
        public DbSet<PurchaseOrderInspectionDetail> PurchaseOrderInspectionDetails { get; set; }
        public DbSet<PurchaseOrderApprovalRecord> PurchaseOrderApprovalRecords { get; set; }

        // Warehouse Entities
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<WarehouseCategory> WarehouseCategories { get; set; }
        public DbSet<StorageType> StorageTypes { get; set; }
        public DbSet<WarehouseStructure> WarehouseStructures { get; set; }

        // Inbound Entities
        public DbSet<InboundRecord> InboundRecords { get; set; }
        public DbSet<InboundDetail> InboundDetails { get; set; }
        public DbSet<PurchaseInboundRecord> PurchaseInboundRecords { get; set; }
        public DbSet<ProductionInboundRecord> ProductionInboundRecords { get; set; }
        public DbSet<ProductionReturnMaterialInboundRecord> ProductionReturnMaterialInboundRecords { get; set; }
        public DbSet<OutsourcedWorkOrderInboundRecord> OutsourcedWorkOrderInboundRecords { get; set; }
        public DbSet<OutsourcedProcessingInboundRecord> OutsourcedProcessingInboundRecords { get; set; }
        public DbSet<OtherInboundRecord> OtherInboundRecords { get; set; }
        public DbSet<GoodsArrivalInspection> GoodsArrivalInspections { get; set; }
        public DbSet<GoodsArrivalInspectionDetail> GoodsArrivalInspectionDetails { get; set; }

        // Production Entities
        public DbSet<ProductionPlan> ProductionPlans { get; set; }
        public DbSet<ProductionProcessFlow> ProductionProcessFlows { get; set; }
        public DbSet<InternalProduction> InternalProductions { get; set; }
        public DbSet<ProductionOrderItem> ProductionOrderItems { get; set; }
        public DbSet<ConsumableItems> ConsumableItems { get; set; }
        public DbSet<ProductionItems> ProductionItems { get; set; }
        public DbSet<ProductionOrder> ProductionOrders { get; set; }

        // BOM Entities
        public DbSet<Attachment> Attachments { get; set; }
        public DbSet<ProcessOperation> ProcessOperations { get; set; }
        public DbSet<BillOfMaterials> BillOfMaterials { get; set; }
        public DbSet<MaterialComponents> MaterialComponents { get; set; }
        public DbSet<BOMProcessFlow> BOMProcessFlows { get; set; }
        public DbSet<ProcessFlow> ProcessFlows { get; set; }

        // Item Entities
        public DbSet<Items> Items { get; set; }
        public DbSet<TieredPricing> TieredPricings { get; set; }
        public DbSet<ItemCategories> ItemCategoriesList { get; set; }

        // Quality Entities
        public DbSet<QualityOrder> QualityOrders { get; set; }
        public DbSet<QualityInspectionRecord> QualityInspectionRecords { get; set; }
        public DbSet<WorkReport> WorkReports { get; set; }

        // Three Entities
        public DbSet<SaleProductionItemsCommand> SaleProductionItemsCommands { get; set; }
        public DbSet<SaleDepleteCommand> SaleDepleteCommands { get; set; }
        public DbSet<OutboundOrder> OutboundOrders { get; set; }
        public DbSet<ApprovalProcess> ApprovalProcesses { get; set; }
        public DbSet<OutsourcingWorkOrder> OutsourcingWorkOrders { get; set; }
        public DbSet<ProcessClassification> ProcessClassifications { get; set; }
        public DbSet<ApprovalNode> ApprovalNodes { get; set; }
        public DbSet<OutboundDetail> OutboundDetails { get; set; }
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Process> Processes { get; set; }
        public DbSet<OutsourcingProcessing> OutsourcingProcessings { get; set; }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // 为所有继承BaseEntity的实体配置软删除查询过滤器
            modelBuilder.Entity<Address>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Person>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Department>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Project>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PersonnelScale>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Location>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<BusinessNature>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Industry>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<ItemCategory>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ItemAttribute>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ItemType>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<Supplier>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Customer>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SupplierItem>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SupplierInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SupplierPurchaseRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<CustomerSalesRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<CustomerOutboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<CustomerDeliveryAddress>().HasQueryFilter(x => !x.IsDeleted);

            // Sales Entities
            modelBuilder.Entity<SalesOrder>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesOrderDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesOutboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesOutboundDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesReturnRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesReturnDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesReturnInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesPurchaseRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesPurchaseDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesProductionRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesProductionDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesPaymentRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesInvoiceRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SalesInvoiceDetail>().HasQueryFilter(x => !x.IsDeleted);

            // Purchase Entities
            modelBuilder.Entity<PurchaseOrder>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderReturnRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderReturnDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderInspectionRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderInspectionDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseOrderApprovalRecord>().HasQueryFilter(x => !x.IsDeleted);

            // Warehouse Entities
            modelBuilder.Entity<Warehouse>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<WarehouseCategory>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<StorageType>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<WarehouseStructure>().HasQueryFilter(x => !x.IsDeleted);

            // Inbound Entities
            modelBuilder.Entity<InboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<InboundDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<PurchaseInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionReturnMaterialInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutsourcedWorkOrderInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutsourcedProcessingInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OtherInboundRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<GoodsArrivalInspection>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<GoodsArrivalInspectionDetail>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<ProductionPlan>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionProcessFlow>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<InternalProduction>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionOrderItem>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ConsumableItems>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionItems>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProductionOrder>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<Attachment>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProcessOperation>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<BillOfMaterials>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<MaterialComponents>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<BOMProcessFlow>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProcessFlow>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<Items>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<TieredPricing>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ItemCategories>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<QualityOrder>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<QualityInspectionRecord>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<WorkReport>().HasQueryFilter(x => !x.IsDeleted);

            modelBuilder.Entity<SaleProductionItemsCommand>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<SaleDepleteCommand>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutboundOrder>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ApprovalProcess>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutsourcingWorkOrder>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ProcessClassification>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<ApprovalNode>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutboundDetail>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Employee>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<Process>().HasQueryFilter(x => !x.IsDeleted);
            modelBuilder.Entity<OutsourcingProcessing>().HasQueryFilter(x => !x.IsDeleted);

            base.OnModelCreating(modelBuilder);
        }
    }
}
