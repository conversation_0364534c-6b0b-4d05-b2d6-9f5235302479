import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: 'localhost', // 只绑定到localhost，不允许局域网内访问
    port: 5176, // 使用端口号5176
    cors: true, // 启用CORS
    // 配置代理，如果API服务器在不同域名
    proxy: {
      // 如果有API代理需求，可以在这里配置
    }
  },
  preview: {
    host: 'localhost',
    port: 5176
  }
})
