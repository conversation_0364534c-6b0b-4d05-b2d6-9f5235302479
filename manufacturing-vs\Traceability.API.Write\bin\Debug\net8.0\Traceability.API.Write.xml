<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.Write</name>
    </assembly>
    <members>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.DelPermissionCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.PermissionUrl">
            <summary>
            权限URL
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.OrderNo">
            <summary>
            权限序号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.ParentId">
            <summary>
            父级编号
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Application.Command.Role.CreateRoleCommand">
            <summary>
            添加角色
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.CreateRoleCommand.PermissionId">
            <summary>
            权限Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.DeleteRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.PermissionId">
            <summary>
            权限Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand.Id">
            <summary>
            主键Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.CreateUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.UserState">
            <summary>
            
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.DeleteUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.CreateId">
            <summary>
            创建人Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Application.Command.User.UpdateUserCommand.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.CreateRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.CreateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.DeleteRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.DeleteRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.UpdateRoleCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.UpdateRoleCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._roleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._rolePermissionRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler._logger">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.Role.UpdateRoleStateCommandHandler.Handle(Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.CreateUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.CreateUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.DeleteUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.DeleteUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.UpdateUserCommandHandler.Handle(Traceability.API.Write.Application.Command.User.UpdateUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._userRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._userRoleRepository">
            <summary>
            
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler._mapper">
            <summary>
            
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Application.Handler.User.UpdateUserStateCommandHandler.Handle(Traceability.API.Write.Application.Command.User.UpdateUserStateCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="P:Traceability.API.Write.Common.IIdentifyService.UserId">
            <summary>
            定义身份服务的相关方法
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.JwtSettings">
            <summary>
            JWT配置类
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.SecretKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.Issuer">
            <summary>
            发布者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.Audience">
            <summary>
            接收者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.AccessTokenExpirationMinutes">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.JwtSettings.RefreshExpireTime">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.MappingProfile">
            <summary>
            AutoMapper映射配置类
            用于定义Command对象到实体模型的映射规则
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Common.MappingProfile.#ctor">
            <summary>
            构造函数，配置所有的映射关系
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Common.RefreshToken">
            <summary>
            续期的Token
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Token">
            <summary>
            生成一个唯一Id
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Expires">
            <summary>
            续期Token的过期时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.Created">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Write.Common.RefreshToken.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.PermissionController">
            <summary>
            权限管理
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.PermissionController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.CreatePermission(Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand)">
            <summary>
            权限添加接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.UpdatePermission(Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand)">
            <summary>
            权限编辑接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.PermissionController.Handle(Traceability.API.Write.Application.Command.Permission.DelPermissionCommand)">
            <summary>
            权限删除接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.RoleController">
            <summary>
            角色权限
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.RoleController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.AddRole(Traceability.API.Write.Application.Command.Role.CreateRoleCommand)">
            <summary>
            添加角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.UpdateRole(Traceability.API.Write.Application.Command.Role.UpdateRoleCommand)">
            <summary>
            修改角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.UpdateRoleState(Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand)">
            <summary>
            修改角色状态
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.RoleController.DeleteRole(Traceability.API.Write.Application.Command.Role.DeleteRoleCommand)">
            <summary>
            删除角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Controllers.RBAC.UserController">
            <summary>
            用户控制器
            </summary>
        </member>
        <member name="F:Traceability.API.Write.Controllers.RBAC.UserController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.AddUser(Traceability.API.Write.Application.Command.User.CreateUserCommand)">
            <summary>
            添加用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.UpdateUser(Traceability.API.Write.Application.Command.User.UpdateUserCommand)">
            <summary>
            修改用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.UpdateUserState(Traceability.API.Write.Application.Command.User.UpdateUserStateCommand)">
            <summary>
            修改用户状态
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Write.Controllers.RBAC.UserController.DeleteUser(Traceability.API.Write.Application.Command.User.DeleteUserCommand)">
            <summary>
            删除用户信息
            </summary>
            <param name="command">命令</param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Write.Extensions.ServiceCollectionExtensions">
            <summary>
            服务注册扩展类
            </summary>
        </member>
        <member name="M:Traceability.API.Write.Extensions.ServiceCollectionExtensions.AddDBContextAccessor``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Boolean)">
            <summary>
            注册EFCore上下文
            </summary>
            <typeparam name="T"></typeparam>
            <param name="services"></param>
            <param name="connectionString">连接字符串</param>
            <param name="enablerSqlLog">是否启用SQL日志</param>
            <returns></returns>
        </member>
    </members>
</doc>
