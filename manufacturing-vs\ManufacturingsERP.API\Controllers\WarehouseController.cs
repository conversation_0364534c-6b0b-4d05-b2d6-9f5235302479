using Microsoft.AspNetCore.Mvc;
using MediatR;
using ManufacturingsERP.API.Application.command.Warehouse;
using Manufacturings.Infrastructrue.Error;
using ManufacturingsERP.API.Application.DTOs;

namespace ManufacturingsERP.API.Controllers
{
    /// <summary>
    /// 仓库管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class WarehouseController : ControllerBase
    {
        private readonly IMediator _mediator;

        public WarehouseController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 新增仓库
        /// </summary>
        /// <param name="command">创建仓库命令</param>
        /// <returns>仓库ID</returns>
        [HttpPost]
        public async Task<ActionResult<APIResult<long>>> CreateWarehouse([FromBody] CreateWarehouseCommand command)
        {
            try
            {
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new APIResult<long>
                {
                    Code = ResultCode.Fail,
                    Message = $"仓库创建失败: {ex.Message}",
                    Data = 0
                });
            }
        }

        /// <summary>
        /// 更新仓库
        /// </summary>
        /// <param name="command">更新仓库命令</param>
        /// <returns>更新结果</returns>
        [HttpPut]
        public async Task<ActionResult<APIResult<bool>>> UpdateWarehouse([FromBody] UpdateWarehouseCommand command)
        {
            try
            {
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new APIResult<bool>
                {
                    Code = ResultCode.Fail,
                    Message = $"仓库更新失败: {ex.Message}",
                    Data = false
                });
            }
        }

        /// <summary>
        /// 删除仓库
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<APIResult<bool>>> DeleteWarehouse(long id)
        {
            try
            {
                var command = new DeleteWarehouseCommand { Id = id };
                var result = await _mediator.Send(command);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new APIResult<bool>
                {
                    Code = ResultCode.Fail,
                    Message = $"仓库删除失败: {ex.Message}",
                    Data = false
                });
            }
        }

        /// <summary>
        /// 获取仓库列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>仓库列表</returns>
        [HttpGet("list")]
        public async Task<ActionResult<APIResult<APIPageing<WarehouseDto>>>> GetWarehouseList([FromQuery] GetWarehouseListQuery query)
        {
            try
            {
                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new APIResult<APIPageing<WarehouseDto>>
                {
                    Code = ResultCode.Fail,
                    Message = $"获取仓库列表失败: {ex.Message}",
                    Data = null
                });
            }
        }

        /// <summary>
        /// 根据ID获取仓库详情
        /// </summary>
        /// <param name="id">仓库ID</param>
        /// <returns>仓库详情</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<APIResult<WarehouseDetailDto>>> GetWarehouseById(long id)
        {
            try
            {
                var query = new GetWarehouseByIdQuery { Id = id };
                var result = await _mediator.Send(query);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new APIResult<WarehouseDetailDto>
                {
                    Code = ResultCode.Fail,
                    Message = $"获取仓库详情失败: {ex.Message}",
                    Data = null
                });
            }
        }
    }
} 