using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 人员表
    /// </summary>
    public class Person : BaseEntity
    {
        /// <summary>
        /// 姓名
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 工号
        /// </summary>
        [Required]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        /// <summary>
        /// 手机号
        /// </summary>
        [StringLength(20)]
        public string? PhoneNumber { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [StringLength(100)]
        public string? Department { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [StringLength(100)]
        public string? Position { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 