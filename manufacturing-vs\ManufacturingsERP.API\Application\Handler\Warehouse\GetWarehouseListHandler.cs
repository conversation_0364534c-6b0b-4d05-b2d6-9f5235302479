using MediatR;
using ManufacturingsERP.API.Application.Interfaces;
using ManufacturingsERP.API.Application.command.Warehouse;
using Manufacturings.Infrastructrue.Error;
using Microsoft.EntityFrameworkCore;
using ManufacturingsERP.API.Application.DTOs;

namespace ManufacturingsERP.API.Application.Handler.Warehouse
{
    /// <summary>
    /// 获取仓库列表处理器
    /// </summary>
    public class GetWarehouseListHandler : IRequestHandler<GetWarehouseListQuery, APIResult<APIPageing<WarehouseDto>>>
    {
        private readonly IWarehouseRepository _warehouseRepository;

        public GetWarehouseListHandler(IWarehouseRepository warehouseRepository)
        {
            _warehouseRepository = warehouseRepository;
        }

        public async Task<APIResult<APIPageing<WarehouseDto>>> Handle(GetWarehouseListQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var query = _warehouseRepository.GetAll()
                    .Where(w => !w.IsDeleted)
                    .AsQueryable();

                // 应用筛选条件
                if (!string.IsNullOrEmpty(request.Keyword))
                {
                    query = query.Where(w => 
                        w.WarehouseName.Contains(request.Keyword) || 
                        w.WarehouseNumber.Contains(request.Keyword) ||
                        (w.Address != null && w.Address.Contains(request.Keyword))
                    );
                }

                // 获取总数
                var totalCount = await query.CountAsync(cancellationToken);

                // 获取分页数据
                var warehouseEntities = await query
                    .OrderBy(w => w.CreateTime)
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToListAsync(cancellationToken);

                // 转换为DTO（简化版本，不包含关联数据）
                var warehouses = warehouseEntities.Select(w => new WarehouseDto
                {
                    Id = w.Id,
                    WarehouseNumber = w.WarehouseNumber,
                    WarehouseName = w.WarehouseName,
                    ParentId = w.ParentId,
                    ParentName = "", // 简化版本，暂时为空
                    CategoryId = w.CategoryId,
                    CategoryName = "", // 简化版本，暂时为空
                    StorageTypeId = w.StorageTypeId,
                    StorageTypeName = "", // 简化版本，暂时为空
                    StructureId = w.StructureId,
                    StructureName = "", // 简化版本，暂时为空
                    PersonInChargeId = w.PersonInChargeId,
                    PersonInChargeName = "", // 简化版本，暂时为空
                    Address = w.Address,
                    Remarks = w.Remarks,
                    IsEnabled = w.IsEnabled,
                    IsSystemNumber = w.IsSystemNumber,
                    CreateTime = w.CreateTime,
                    CreateName = w.CreateName,
                    ModifyTime = w.ModifyTime,
                    ModifierName = w.ModifierName,
                    HasChildren = warehouseEntities.Any(child => child.ParentId == w.Id && !child.IsDeleted)
                }).ToList();

                var result = new APIPageing<WarehouseDto>
                {
                    PageData = warehouses,
                    TotalCount = totalCount,
                    PageCount = (int)Math.Ceiling((double)totalCount / request.PageSize)
                };

                return new APIResult<APIPageing<WarehouseDto>>
                {
                    Code = ResultCode.Success,
                    Message = "获取成功",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new APIResult<APIPageing<WarehouseDto>>
                {
                    Code = ResultCode.Fail,
                    Message = $"获取仓库列表失败: {ex.Message}",
                    Data = null
                };
            }
        }
    }
} 