﻿using Manufacturings.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 委外生产中间表
    /// </summary>
    public class SaleProductionItemsCommand : BaseEntity
    {
        /// <summary>
        /// 委外加工Id
        /// </summary>
        public long OutsourcingProcessing {  get; set; }
        /// <summary>
        /// 生产物品Id
        /// </summary>
        public long ProduceId {  get; set; }
        /// <summary>
        /// 计划生产数量
        /// </summary>
        public int Plannedproduction { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = string.Empty;
    }
}
