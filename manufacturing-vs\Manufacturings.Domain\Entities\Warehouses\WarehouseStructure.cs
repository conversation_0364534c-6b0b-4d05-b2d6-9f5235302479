using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Warehouses
{
    /// <summary>
    /// 仓库结构表
    /// </summary>
    public class WarehouseStructure : BaseEntity
    {
        /// <summary>
        /// 结构名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string StructureName { get; set; } = string.Empty;

        /// <summary>
        /// 结构编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string StructureCode { get; set; } = string.Empty;

        /// <summary>
        /// 结构描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 