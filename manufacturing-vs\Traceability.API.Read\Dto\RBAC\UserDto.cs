﻿using System.ComponentModel.DataAnnotations;

namespace Traceability.API.Read.Dto.RBAC
{
    public class UserDto
    {
        public long Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool UserState { get; set; }
        /// <summary>
        /// 角色编号
        /// </summary>
        public List<long> RoleId { get; set; }
        /// <summary>
        /// 角色名称
        /// </summary>
        public string RoleName { get; set; }

        public DateTime CreateTime { get; set; }
    }
}
