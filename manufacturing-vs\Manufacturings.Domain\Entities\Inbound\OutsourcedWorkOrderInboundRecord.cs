using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 委外工单入库记录表
    /// </summary>
    public class OutsourcedWorkOrderInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 委外工单号
        /// </summary>
        [StringLength(50)]
        public string? OutsourcedWorkOrderNumber { get; set; }

        /// <summary>
        /// 委外工单日期
        /// </summary>
        public DateTime? OutsourcedWorkOrderDate { get; set; }

        /// <summary>
        /// 委外加工商ID
        /// </summary>
        public int? OutsourcedProcessorId { get; set; }

        /// <summary>
        /// 工单类型
        /// </summary>
        [StringLength(100)]
        public string? WorkOrderType { get; set; }

        /// <summary>
        /// 工单状态
        /// </summary>
        public WorkOrderStatus? WorkOrderStatus { get; set; }

        /// <summary>
        /// 预计完成日期
        /// </summary>
        public DateTime? ExpectedCompletionDate { get; set; }

        /// <summary>
        /// 实际完成日期
        /// </summary>
        public DateTime? ActualCompletionDate { get; set; }

        /// <summary>
        /// 工单备注
        /// </summary>
        [StringLength(500)]
        public string? WorkOrderRemarks { get; set; }
    }
} 