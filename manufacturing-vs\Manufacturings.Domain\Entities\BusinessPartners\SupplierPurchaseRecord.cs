using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.BusinessPartners
{
    /// <summary>
    /// 供应商采购记录表
    /// </summary>
    public class SupplierPurchaseRecord : BaseEntity
    {
        /// <summary>
        /// 采购编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string PurchaseCode { get; set; } = string.Empty;

        /// <summary>
        /// 采购订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string PurchaseOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 采购日期
        /// </summary>
        [Required]
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 物品概要
        /// </summary>
        [StringLength(500)]
        public string? ItemSummary { get; set; }

        /// <summary>
        /// 采购部门ID
        /// </summary>
        public long? PurchasingDepartmentId { get; set; }

        /// <summary>
        /// 采购人员ID
        /// </summary>
        public long? PurchasingPersonnelId { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public long SupplierId { get; set; }
    }
} 