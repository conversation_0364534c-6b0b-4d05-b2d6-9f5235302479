﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.API.Read", "Traceability.API.Read\Traceability.API.Read.csproj", "{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.API.UploadImg", "Traceability.API.UploadImg\Traceability.API.UploadImg.csproj", "{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.API.Write", "Traceability.API.Write\Traceability.API.Write.csproj", "{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.Domain", "Traceability.Domain\Traceability.Domain.csproj", "{6787083A-A5F1-FDEF-4707-0BE126ED5DB6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.ErrorCount", "Traceability.ErrorCount\Traceability.ErrorCount.csproj", "{8C351886-2D16-0AB9-0175-4D369762DEF3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Traceability.Infrastructure", "Traceability.Infrastructrue\Traceability.Infrastructure.csproj", "{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "RBAC", "RBAC", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Manufacturings", "Manufacturings", "{AEE97333-36F3-4A62-B539-344DF849A452}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Manufacturing Execution System", "Manufacturing Execution System", "{3044C39F-3D40-4538-B7A2-A2699295A998}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Manufacturings.Domain", "Manufacturings.Domain\Manufacturings.Domain.csproj", "{E7D81204-C61A-4366-80D3-312EF35B90CF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Manufacturings.Infrastructrue", "Manufacturings.Infrastructrue\Manufacturings.Infrastructrue.csproj", "{B9F4A9F8-5D47-4041-9166-B4979F28DF9C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Manufacturing.Execution.API", "Manufacturing.Execution.API\Manufacturing.Execution.API.csproj", "{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Manufacturing.Application", "Manufacturing.Application\Manufacturing.Application.csproj", "{71919A0E-F79B-48B4-B01B-FEBA1C010A9C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Process production", "Process production", "{D80B2274-1A17-42BA-AC87-9FCA1720A386}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Processproduction.knn.API", "Processproduction.knn.API\Processproduction.knn.API.csproj", "{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Processproduction.knn.Application", "Processproduction.knn.Application\Processproduction.knn.Application.csproj", "{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ManufacturingsERP", "ManufacturingsERP", "{85466A66-1E0C-4120-B34B-C30ACC536ADF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ManufacturingsERP.API", "ManufacturingsERP.API\ManufacturingsERP.API.csproj", "{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB}.Release|Any CPU.Build.0 = Release|Any CPU
		{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2}.Release|Any CPU.Build.0 = Release|Any CPU
		{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{6787083A-A5F1-FDEF-4707-0BE126ED5DB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6787083A-A5F1-FDEF-4707-0BE126ED5DB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6787083A-A5F1-FDEF-4707-0BE126ED5DB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6787083A-A5F1-FDEF-4707-0BE126ED5DB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{8C351886-2D16-0AB9-0175-4D369762DEF3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8C351886-2D16-0AB9-0175-4D369762DEF3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8C351886-2D16-0AB9-0175-4D369762DEF3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8C351886-2D16-0AB9-0175-4D369762DEF3}.Release|Any CPU.Build.0 = Release|Any CPU
		{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7D81204-C61A-4366-80D3-312EF35B90CF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7D81204-C61A-4366-80D3-312EF35B90CF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7D81204-C61A-4366-80D3-312EF35B90CF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7D81204-C61A-4366-80D3-312EF35B90CF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9F4A9F8-5D47-4041-9166-B4979F28DF9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9F4A9F8-5D47-4041-9166-B4979F28DF9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9F4A9F8-5D47-4041-9166-B4979F28DF9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9F4A9F8-5D47-4041-9166-B4979F28DF9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A}.Release|Any CPU.Build.0 = Release|Any CPU
		{71919A0E-F79B-48B4-B01B-FEBA1C010A9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71919A0E-F79B-48B4-B01B-FEBA1C010A9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71919A0E-F79B-48B4-B01B-FEBA1C010A9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71919A0E-F79B-48B4-B01B-FEBA1C010A9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{E0A40854-09FD-06D3-093C-6FDAFDEA0DAB} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{0FB42B08-426A-31D3-C3A7-B2ADAF5B00B2} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{A33695E4-0312-5FBB-0D5A-B9C79B9C54B6} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6787083A-A5F1-FDEF-4707-0BE126ED5DB6} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{8C351886-2D16-0AB9-0175-4D369762DEF3} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{1C2D84E0-FD17-9F3A-7400-EFCB74F0C664} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3044C39F-3D40-4538-B7A2-A2699295A998} = {AEE97333-36F3-4A62-B539-344DF849A452}
		{E7D81204-C61A-4366-80D3-312EF35B90CF} = {AEE97333-36F3-4A62-B539-344DF849A452}
		{B9F4A9F8-5D47-4041-9166-B4979F28DF9C} = {AEE97333-36F3-4A62-B539-344DF849A452}
		{70C3EFF0-F61A-482E-B975-DDE4AF09EF8A} = {3044C39F-3D40-4538-B7A2-A2699295A998}
		{71919A0E-F79B-48B4-B01B-FEBA1C010A9C} = {3044C39F-3D40-4538-B7A2-A2699295A998}
		{D80B2274-1A17-42BA-AC87-9FCA1720A386} = {AEE97333-36F3-4A62-B539-344DF849A452}
		{EF54AD27-E9D1-4B28-9D2F-019F6D5DFB5C} = {D80B2274-1A17-42BA-AC87-9FCA1720A386}
		{F17CF389-41E5-4CF1-9C8E-4A5D2F78C8EB} = {D80B2274-1A17-42BA-AC87-9FCA1720A386}
		{85466A66-1E0C-4120-B34B-C30ACC536ADF} = {AEE97333-36F3-4A62-B539-344DF849A452}
		{DD5DCFE6-A69E-4A1C-8250-519AAC0FC403} = {85466A66-1E0C-4120-B34B-C30ACC536ADF}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {969CC9D0-A928-4E7E-B614-4CC92E1EE142}
	EndGlobalSection
EndGlobal
