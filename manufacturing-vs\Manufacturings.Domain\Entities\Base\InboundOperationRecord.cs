using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Enums;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用入库操作记录表
    /// </summary>
    public class InboundOperationRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public long InboundRecordId { get; set; }

        /// <summary>
        /// 入库类型
        /// </summary>
        [Required]
        public InboundType InboundType { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public InboundOperationType OperationType { get; set; }

        /// <summary>
        /// 操作前状态
        /// </summary>
        [StringLength(50)]
        public string? PreviousStatus { get; set; }

        /// <summary>
        /// 操作后状态
        /// </summary>
        [StringLength(50)]
        public string? CurrentStatus { get; set; }

        /// <summary>
        /// 操作描述
        /// </summary>
        [StringLength(500)]
        public string? OperationDescription { get; set; }

        /// <summary>
        /// 操作备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        [Required]
        public long OperatedBy { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        [Required]
        public DateTime OperatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// IP地址
        /// </summary>
        [StringLength(50)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }
    }

    /// <summary>
    /// 入库操作类型枚举
    /// </summary>
    public enum InboundOperationType
    {
        /// <summary>
        /// 创建
        /// </summary>
        Created = 1,
        /// <summary>
        /// 修改
        /// </summary>
        Modified = 2,
        /// <summary>
        /// 审核
        /// </summary>
        Approved = 3,
        /// <summary>
        /// 拒绝
        /// </summary>
        Rejected = 4,
        /// <summary>
        /// 确认
        /// </summary>
        Confirmed = 5,
        /// <summary>
        /// 取消
        /// </summary>
        Cancelled = 6,
        /// <summary>
        /// 入库
        /// </summary>
        Inbound = 7,
        /// <summary>
        /// 检验
        /// </summary>
        Inspected = 8,
        /// <summary>
        /// 完成
        /// </summary>
        Completed = 9,
        /// <summary>
        /// 关闭
        /// </summary>
        Closed = 10
    }
} 