{"format": 1, "restore": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Execution.API\\Manufacturing.Execution.API.csproj": {}}, "projects": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\Manufacturing.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\Manufacturing.Application.csproj", "projectName": "Manufacturing.Application", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\Manufacturing.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj"}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\Manufacturings.Infrastructrue.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\Manufacturings.Infrastructrue.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Execution.API\\Manufacturing.Execution.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Execution.API\\Manufacturing.Execution.API.csproj", "projectName": "Manufacturing.Execution.API", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Execution.API\\Manufacturing.Execution.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Execution.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\Manufacturing.Application.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturing.Application\\Manufacturing.Application.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "IGeekFan.AspNetCore.Knife4jUI": {"target": "Package", "version": "[0.0.16, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.19, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.19, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.19, )"}, "NLog": {"target": "Package", "version": "[6.0.3, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[6.0.3, )"}, "NPOI": {"target": "Package", "version": "[2.7.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.3, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj", "projectName": "Manufacturings.Domain", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\Manufacturings.Infrastructrue.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\Manufacturings.Infrastructrue.csproj", "projectName": "Manufacturings.Infrastructrue", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\Manufacturings.Infrastructrue.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Infrastructrue\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Manufacturings.Domain\\Manufacturings.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}}}