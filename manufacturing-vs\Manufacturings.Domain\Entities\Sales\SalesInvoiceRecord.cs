using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售开票记录表
    /// </summary>
    public class SalesInvoiceRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public int SalesOrderId { get; set; }

        /// <summary>
        /// 开票编号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InvoiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 开票主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string InvoiceSubject { get; set; } = string.Empty;

        /// <summary>
        /// 开票日期
        /// </summary>
        [Required]
        public DateTime InvoiceDate { get; set; }

        /// <summary>
        /// 发票类型（增值税专用发票、增值税普通发票、其他发票等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string InvoiceType { get; set; } = string.Empty;

        /// <summary>
        /// 发票号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InvoiceDocumentNumber { get; set; } = string.Empty;

        /// <summary>
        /// 开票总金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalInvoiceAmount { get; set; }

        /// <summary>
        /// 收票人
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Recipient { get; set; } = string.Empty;

        /// <summary>
        /// 开票状态（待开票、已开票、已寄出、已收到等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string InvoiceStatus { get; set; } = "待开票";

        /// <summary>
        /// 开票人员ID
        /// </summary>
        public int? InvoicePersonId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 