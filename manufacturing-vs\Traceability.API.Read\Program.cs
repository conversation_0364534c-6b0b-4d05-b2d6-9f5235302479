using IGeekFan.AspNetCore.Knife4jUI;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using Traceability.API.Read;
using Traceability.API.Read.Extensions;
using Traceability.Infrastructure;
using Yitter.IdGenerator;
using NLog.Web;

//������չ���� ʵ������ע��
var builder = WebApplication.CreateBuilder(args).Inject();
// Add services to the container.


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
  
    //app.UseSwaggerUI();
}
app.UseSwagger();
//�Զ���Swagger
app.UseKnife4UI();
//����
app.UseCors(x=>x.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
// ..... 
app.UseAuthentication(); // ��֤
app.UseAuthorization(); // ����Ȩ

app.MapControllers();


app.Run();
