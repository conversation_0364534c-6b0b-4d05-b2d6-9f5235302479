﻿using MediatR;
using Traceability.API.Read.Dto.RBAC;
using Traceability.ErrorCount;

namespace Traceability.API.Read.Application.Command.User
{
    public class UserQueryCommand:IRequest<APIResult<APIPageing<UserDto>>>
    {
        /// <summary>
        /// 用户姓名
        /// </summary>
        public string? NickName { get; set; }
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        /// <summary>
        /// 页容量
        /// </summary>
        public int PageSize { get; set; }
    }
}
