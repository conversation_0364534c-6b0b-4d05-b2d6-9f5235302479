﻿using IGeekFan.AspNetCore.Knife4jUI;
using Manufacturing.Execution.API.Extensions;

var builder = WebApplication.CreateBuilder(args).Inject();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{

    //app.UseSwaggerUI();
}
app.UseSwagger();

app.UseKnife4UI();

app.UseCors(x => x.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
// ..... 
app.UseAuthentication(); // 
app.UseAuthorization(); // 

app.MapControllers();

app.Run();
