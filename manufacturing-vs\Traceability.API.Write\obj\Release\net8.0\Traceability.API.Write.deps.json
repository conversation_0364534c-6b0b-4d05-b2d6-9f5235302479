{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Traceability.API.Write/1.0.0": {"dependencies": {"AutoMapper": "13.0.1", "IGeekFan.AspNetCore.Knife4jUI": "0.0.16", "MD5": "2.2.5", "MediatR": "12.5.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.17", "Microsoft.AspNetCore.OpenApi": "8.0.3", "Microsoft.EntityFrameworkCore.Design": "8.0.3", "Microsoft.EntityFrameworkCore.Tools": "8.0.3", "NLog.Web.AspNetCore": "5.5.0", "NPOI": "2.7.4", "Newtonsoft.Json": "13.0.3", "Pomelo.EntityFrameworkCore.MySql": "8.0.0", "Swashbuckle.AspNetCore": "6.5.0", "Traceability.ErrorCount": "1.0.0", "Traceability.Infrastructure": "1.0.0", "Yitter.IdGenerator": "1.0.14"}, "runtime": {"Traceability.API.Write.dll": {}}}, "AutoMapper/13.0.1": {"dependencies": {"Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net6.0/AutoMapper.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.0"}}}, "BouncyCastle.Cryptography/2.4.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.4.0.33771"}}}, "Enums.NET/5.0.0": {"runtime": {"lib/net7.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"runtime": {"lib/net8.0/ExtendedNumerics.BigDecimal.dll": {"assemblyVersion": "2025.1001.2.129", "fileVersion": "2025.1001.2.129"}}}, "Humanizer.Core/2.14.1": {}, "IGeekFan.AspNetCore.Knife4jUI/0.0.16": {"runtime": {"lib/net7.0/IGeekFan.AspNetCore.Knife4jUI.dll": {"assemblyVersion": "0.0.16.0", "fileVersion": "0.0.16.0"}}}, "MathNet.Numerics.Signed/5.0.0": {"runtime": {"lib/net6.0/MathNet.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MD5/2.2.5": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/MD5.dll": {"assemblyVersion": "2.2.5.0", "fileVersion": "2.2.5.0"}}}, "MediatR/12.5.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "1*******", "fileVersion": "12.5.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.17": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.17.0", "fileVersion": "8.0.1725.26609"}}}, "Microsoft.AspNetCore.OpenApi/8.0.3": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11615"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}}, "Microsoft.EntityFrameworkCore/8.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.3", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.3", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11510"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.3": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11510"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.3": {}, "Microsoft.EntityFrameworkCore.Design/8.0.3": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.3", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}}, "Microsoft.EntityFrameworkCore.Relational/8.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.3", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11510"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.3": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.3"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Logging/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.1.2", "System.IdentityModel.Tokens.Jwt": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IdentityModel.Tokens/7.1.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.1.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "3.0.1.0", "fileVersion": "3.0.1.0"}}}, "Microsoft.OpenApi/1.4.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.4.3.0", "fileVersion": "1.4.3.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}}, "MySqlConnector/2.3.5": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.5.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NLog/5.5.0": {"runtime": {"lib/netstandard2.0/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "5.5.0.3962"}}}, "NLog.Extensions.Logging/5.5.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "NLog": "5.5.0"}, "runtime": {"lib/net8.0/NLog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.5.0.656"}}}, "NLog.Web.AspNetCore/5.5.0": {"dependencies": {"NLog.Extensions.Logging": "5.5.0"}, "runtime": {"lib/net8.0/NLog.Web.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "5.5.0.1230"}}}, "NPOI/2.7.4": {"dependencies": {"BouncyCastle.Cryptography": "2.4.0", "Enums.NET": "5.0.0", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.1", "SharpZipLib": "1.4.2", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.10", "System.Security.Cryptography.Xml": "8.0.2", "ZString": "2.6.0"}, "runtime": {"lib/net8.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net8.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pomelo.EntityFrameworkCore.MySql/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.3", "MySqlConnector": "2.3.5"}, "runtime": {"lib/net8.0/Pomelo.EntityFrameworkCore.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpZipLib/1.4.2": {"runtime": {"lib/net6.0/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.1": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.4.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}}, "System.Composition.Runtime/6.0.0": {}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.1.2", "Microsoft.IdentityModel.Tokens": "7.1.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.2.41121"}}}, "System.IO.Pipelines/6.0.3": {}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Pkcs/8.0.1": {"runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Security.Cryptography.Xml/8.0.2": {"dependencies": {"System.Security.Cryptography.Pkcs": "8.0.1"}, "runtime": {"lib/net8.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Channels/6.0.0": {}, "Yitter.IdGenerator/1.0.14": {"runtime": {"lib/netstandard2.0/Yitter.IdGenerator.dll": {"assemblyVersion": "1.0.14.0", "fileVersion": "1.0.14.0"}}}, "ZString/2.6.0": {"runtime": {"lib/net7.0/ZString.dll": {"assemblyVersion": "2.6.0.0", "fileVersion": "2.6.0.0"}}}, "Traceability.Domain/1.0.0": {"runtime": {"Traceability.Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Traceability.ErrorCount/1.0.0": {"runtime": {"Traceability.ErrorCount.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "Traceability.Infrastructure/1.0.0": {"dependencies": {"Pomelo.EntityFrameworkCore.MySql": "8.0.0", "Traceability.Domain": "1.0.0"}, "runtime": {"Traceability.Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"Traceability.API.Write/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/Fx1SbJ16qS7dU4i604Sle+U9VLX+WSNVJggk6MupKVkYvvBm4XqYaeFuf67diHefHKHs50uQIS2YEDFhPCakQ==", "path": "automapper/13.0.1", "hashPath": "automapper.13.0.1.nupkg.sha512"}, "BouncyCastle.Cryptography/2.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SwXsAV3sMvAU/Nn31pbjhWurYSjJ+/giI/0n6tCrYoupEK34iIHCuk3STAd9fx8yudM85KkLSVdn951vTng/vQ==", "path": "bouncycastle.cryptography/2.4.0", "hashPath": "bouncycastle.cryptography.2.4.0.nupkg.sha512"}, "Enums.NET/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfGq1iLJZ15XZPgBhjk4Ns1XZ+beaGk6cog6B4LxcROdGoSMdgCJqYXF70P6VTd3dz/vFRY4h1u1lAMqW/DC2w==", "path": "enums.net/5.0.0", "hashPath": "enums.net.5.0.0.nupkg.sha512"}, "ExtendedNumerics.BigDecimal/2025.1001.2.129": {"type": "package", "serviceable": true, "sha512": "sha512-+woGT1lsBtwkntOpx2EZbdbySv0aWPefE0vrfvclxVdbi4oa2bbtphFPWgMiQe+kRCPICbfFJwp6w1DuR7Ge2Q==", "path": "extendednumerics.bigdecimal/2025.1001.2.129", "hashPath": "extendednumerics.bigdecimal.2025.1001.2.129.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "IGeekFan.AspNetCore.Knife4jUI/0.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-bJjSJWP+FCkH0vweZq+evqOZ4OO2lSR13O19MpjiX49SRNarsM9DqJsqkYqKr17zzF1dgd/pzREdBbJDBRWUjA==", "path": "igeekfan.aspnetcore.knife4jui/0.0.16", "hashPath": "igeekfan.aspnetcore.knife4jui.0.0.16.nupkg.sha512"}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "path": "mathnet.numerics.signed/5.0.0", "hashPath": "mathnet.numerics.signed.5.0.0.nupkg.sha512"}, "MD5/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-gl+KomjAeBTr4ew2Ny4iFGLfCm0QV8jYfgMrqQBnwH8ep5okiqVT5vo7PX72XuIlwr5DWSlqAbBpAmq+frjBMA==", "path": "md5/2.2.5", "hashPath": "md5.2.2.5.nupkg.sha512"}, "MediatR/12.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "path": "mediatr/12.5.0", "hashPath": "mediatr.12.5.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-sDxQH6HXibqSpMCyAag2b8ZJMFeCNF3FiwuGKOzJd0n3X7P6OI75QUp4LSam2/d8DYSO36Z8nm7mmRCHJ5vkMw==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.17", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.17.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mUq0UL+H7UtA3Jud/7/BC7n5W2c4zCvTFUa2hE2+/oQqATa4oGHb87ETON09SEWkcbBRTz3WM16kTE+zuoXq2A==", "path": "microsoft.aspnetcore.openapi/8.0.3", "hashPath": "microsoft.aspnetcore.openapi.8.0.3.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-QUPQbeq4yCjgIL/6PzkhfwhljXmai3CNOsErWFJ/WJ1Z41V8+At0Bi4PT8/2pX25kPgf83g0CUKIZd0QbeKT4A==", "path": "microsoft.entityframeworkcore/8.0.3", "hashPath": "microsoft.entityframeworkcore.8.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cW+SKdx34wZ25ZVKCpk/6+6z27wrZlQ1qXyx7UWpy34s9CyAojH0QiYlV/2owNOGSAH67rm+LxAjUOicsqlGzQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.3", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-3csRAzz5O5Gn+GQBMyLn26OICtEo2/U2iDDygQhKb3LnC78bAUvutkMqvb0Ek5A6uHrBcZQrKQJfkgfnRT5XZw==", "path": "microsoft.entityframeworkcore.analyzers/8.0.3", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-OAh9P0M5VK3+mtmnjOyyw92s3L0r3fmYwWWAnh0pb/8JH3u7L2FmrJnNPFysChBiWIJu17+6Bqj4k/bkFK4RVw==", "path": "microsoft.entityframeworkcore.design/8.0.3", "hashPath": "microsoft.entityframeworkcore.design.8.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-8JnVZHWaNFkrrD/FC0O4jekiHIYey8y6TQ4Co3OzLz0wd5Dm1cwJfTp++1TvaVu0BBd4bVDtiktppa5epuoPrA==", "path": "microsoft.entityframeworkcore.relational/8.0.3", "hashPath": "microsoft.entityframeworkcore.relational.8.0.3.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-jKczLbj13jY6TvYsXgAYqfhkIow6VdqumCv0m8EvNr3vU1+nJ6LKoG+6pPKnkVOHycXHGy5DEhH0GN0ldmx7Kw==", "path": "microsoft.entityframeworkcore.tools/8.0.3", "hashPath": "microsoft.entityframeworkcore.tools.8.0.3.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-33eTIA2uO/L9utJjZWbKsMSVsQf7F8vtd6q5mQX7ZJzNvCpci5fleD6AeANGlbbb7WX7XKxq9+Dkb5e3GNDrmQ==", "path": "microsoft.identitymodel.abstractions/7.1.2", "hashPath": "microsoft.identitymodel.abstractions.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-cloLGeZolXbCJhJBc5OC05uhrdhdPL6MWHuVUnkkUvPDeK7HkwThBaLZ1XjBQVk9YhxXE2OvHXnKi0PLleXxDg==", "path": "microsoft.identitymodel.jsonwebtokens/7.1.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-YCxBt2EeJP8fcXk9desChkWI+0vFqFLvBwrz5hBMsoh0KJE6BC66DnzkdzkJNqMltLromc52dkdT206jJ38cTw==", "path": "microsoft.identitymodel.logging/7.1.2", "hashPath": "microsoft.identitymodel.logging.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-SydLwMRFx6EHPWJ+N6+MVaoArN1Htt92b935O3RUWPY1yUF63zEjvd3lBu79eWdZUwedP8TN2I5V9T3nackvIQ==", "path": "microsoft.identitymodel.protocols/7.1.2", "hashPath": "microsoft.identitymodel.protocols.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-6lHQoLXhnMQ42mGrfDkzbIOR3rzKM1W1tgTeMPLgLCqwwGw0d96xFi/UiX/fYsu7d6cD5MJiL3+4HuI8VU+sVQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.1.2", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-oICJMqr3aNEDZOwnH5SK49bR6Z4aX0zEAnOLuhloumOSuqnNq+GWBdQyrgILnlcT5xj09xKCP/7Y7gJYB+ls/g==", "path": "microsoft.identitymodel.tokens/7.1.2", "hashPath": "microsoft.identitymodel.tokens.7.1.2.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s/s20YTVY9r9TPfTrN5g8zPF1YhwxyqO6PxUkrYTGI2B+OGPe9AdajWZrLhFqXIvqIW23fnUE4+ztrUWNU1+9g==", "path": "microsoft.io.recyclablememorystream/3.0.1", "hashPath": "microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.4.3": {"type": "package", "serviceable": true, "sha512": "sha512-rURwggB+QZYcSVbDr7HSdhw/FELvMlriW10OeOzjPT7pstefMo7IThhtNtDudxbXhW+lj0NfX72Ka5EDsG8x6w==", "path": "microsoft.openapi/1.4.3", "hashPath": "microsoft.openapi.1.4.3.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "MySqlConnector/2.3.5": {"type": "package", "serviceable": true, "sha512": "sha512-AmEfUPkFl+Ev6jJ8Dhns3CYHBfD12RHzGYWuLt6DfG6/af6YvOMyPz74ZPPjBYQGRJkumD2Z48Kqm8s5DJuhLA==", "path": "mysqlconnector/2.3.5", "hashPath": "mysqlconnector.2.3.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NLog/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FCH8s7GWlonH5JXV9/EpeNJ8pRZQMVZOSWX3JrHPU8rzdHJhS5+lUGGvJIUOtzkGV1clYBFR0WXOI5FnUwVCMA==", "path": "nlog/5.5.0", "hashPath": "nlog.5.5.0.nupkg.sha512"}, "NLog.Extensions.Logging/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l8gxUThXCiV5Vcilz9SF2/mf39p9ccdm+V3zoadQIfnv5dMHiy6BH8PSF/9ptOMHfUXZI5qOlBTiO+YcoMY/4Q==", "path": "nlog.extensions.logging/5.5.0", "hashPath": "nlog.extensions.logging.5.5.0.nupkg.sha512"}, "NLog.Web.AspNetCore/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-MFDhgF9lHxq74Nmf3rNNHs/axsPpYgnZXNyAhKj9KKctJ0ZlrR+HR/184AQzu4h0KIH9SgRHeLgbWK9aCWWtRw==", "path": "nlog.web.aspnetcore/5.5.0", "hashPath": "nlog.web.aspnetcore.5.5.0.nupkg.sha512"}, "NPOI/2.7.4": {"type": "package", "serviceable": true, "sha512": "sha512-1tCebTkr9qAfwiEa2ErXco2IT+D8MNmT9d4KFz9nWn3owkc5fAOsvxV8kq6y4531B4Z3gnInrvEdonwFyoRqPQ==", "path": "npoi/2.7.4", "hashPath": "npoi.2.7.4.nupkg.sha512"}, "Pomelo.EntityFrameworkCore.MySql/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wEzKTeYCs/+cGSpT5zE8UrKkJyqpJ24yBufthDoCWUhpCCYUIsMKeX4fvaYcm9mbkfHP2dZ+p6V+ZKTAZ3hXRQ==", "path": "pomelo.entityframeworkcore.mysql/8.0.0", "hashPath": "pomelo.entityframeworkcore.mysql.8.0.0.nupkg.sha512"}, "SharpZipLib/1.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-yjj+3zgz8zgXpiiC3ZdF/iyTBbz2fFvMxZFEBPUcwZjIvXOf37Ylm+K58hqMfIBt5JgU/Z2uoUS67JmTLe973A==", "path": "sharpziplib/1.4.2", "hashPath": "sharpziplib.1.4.2.nupkg.sha512"}, "SixLabors.Fonts/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ljezRHWc7N0azdQViib7Aa5v+DagRVkKI2/93kEbtjVczLs+yTkSq6gtGmvOcx4IqyNbO3GjLt7SAQTpLkySNw==", "path": "sixlabors.fonts/1.0.1", "hashPath": "sixlabors.fonts.1.0.1.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-hk1E7U3RSlxrBVo6Gb6OjeM52fChpFYH+SZvyT/M2vzSGlzAaKE33hc3V/Pvnjcnn1opT8/Z+0QfqdM5HsIaeA==", "path": "sixlabors.imagesharp/2.1.10", "hashPath": "sixlabors.imagesharp.2.1.10.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-Thhbe1peAmtSBFaV/ohtykXiZSOkx59Da44hvtWfIMFofDA3M3LaVyjstACf2rKGn4dEDR2cUpRAZ0Xs/zB+7Q==", "path": "system.identitymodel.tokens.jwt/7.1.2", "hashPath": "system.identitymodel.tokens.jwt.7.1.2.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CoCRHFym33aUSf/NtWSVSZa99dkd0Hm7OCZUxORBjRB16LNhIEOf8THPqzIYlvKM0nNDAPTRBa1FxEECrgaxxA==", "path": "system.security.cryptography.pkcs/8.0.1", "hashPath": "system.security.cryptography.pkcs.8.0.1.nupkg.sha512"}, "System.Security.Cryptography.Xml/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-aDM/wm0ZGEZ6ZYJLzgqjp2FZdHbDHh6/OmpGfb7AdZ105zYmPn/83JRU2xLIbwgoNz9U1SLUTJN0v5th3qmvjA==", "path": "system.security.cryptography.xml/8.0.2", "hashPath": "system.security.cryptography.xml.8.0.2.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "Yitter.IdGenerator/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-F4nOJ7Geq41vgNWX9E6/vkxRzFInACGpDp4Kad2mA2WIKhEwgPyE9FpulBAuEmDByrfHHz6mOII3IIeLJAh91g==", "path": "yitter.idgenerator/1.0.14", "hashPath": "yitter.idgenerator.1.0.14.nupkg.sha512"}, "ZString/2.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-XE8a+11nZg0LRaf+RGABEWaHIf8yGd5w8v/Ra1iWxMBmAVzwuKbW7G21mS0U7w7sh1lYcgckInWGgnz4qyET8A==", "path": "zstring/2.6.0", "hashPath": "zstring.2.6.0.nupkg.sha512"}, "Traceability.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Traceability.ErrorCount/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Traceability.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}