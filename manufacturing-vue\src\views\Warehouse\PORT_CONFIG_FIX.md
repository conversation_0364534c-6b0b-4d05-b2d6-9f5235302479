# 端口配置修复说明

## 系统架构说明

### 后端服务端口分配
- **RBAC系统**：使用读写分离架构
  - 读取服务：5062端口
  - 写入服务：5251端口
  - 上传服务：5142端口

- **仓库管理系统**：使用统一端口
  - 所有操作：5107端口（读写在一起）

## 修复内容

### ✅ 1. 更新server.ts配置

在 `src/Https/server.ts` 中添加了仓库管理专用的API地址：

```typescript
const readBasicUrl= import.meta.env.VITE_ReadAPP_API_URL      // http://localhost:5062/
const writeBasicUrl=import.meta.env.VITE_WriteAPP_API_URL;   // http://localhost:5251/
// 仓库管理使用统一端口
const warehouseApiUrl = 'http://localhost:5107/';
```

### ✅ 2. 修复所有仓库管理API调用

所有仓库管理相关的API现在都使用5107端口：

```typescript
//#region 仓库管理
//获取仓库列表
export const getWarehouseList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/Warehouselist`, {}, params);
}
//添加仓库
export const addWarehouse = (data = {}) => {
    return http("post", warehouseApiUrl + `api/Warehouse`, data, {});
}
//修改仓库
export const updateWarehouse = (data = {}) => {
    return http("put", warehouseApiUrl + `api/Warehouse`, data, {});
}
//删除仓库
export const deleteWarehouse = (id: number) => {
    return http("delete", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库详情
export const getWarehouseById = (id: number) => {
    return http("get", warehouseApiUrl + `api/Warehouse/${id}`, {}, {});
}
//获取仓库分类列表
export const getWarehouseCategoryList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/WarehouseCategory/list`, {}, params);
}
//获取存储类型列表
export const getStorageTypeList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/StorageType/list`, {}, params);
}
//获取仓库结构列表
export const getWarehouseStructureList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/WarehouseStructure/list`, {}, params);
}
//获取人员列表
export const getPersonList = (params = {}) => {
    return http("get", warehouseApiUrl + `api/Person/list`, {}, params);
}
//#endregion
```

## API端点映射

### 仓库管理API (5107端口)
- `GET http://localhost:5107/api/Warehouselist` - 获取仓库列表
- `POST http://localhost:5107/api/Warehouse` - 新增仓库
- `PUT http://localhost:5107/api/Warehouse` - 修改仓库
- `DELETE http://localhost:5107/api/Warehouse/{id}` - 删除仓库
- `GET http://localhost:5107/api/Warehouse/{id}` - 获取仓库详情
- `GET http://localhost:5107/api/WarehouseCategory/list` - 获取仓库分类
- `GET http://localhost:5107/api/StorageType/list` - 获取存储类型
- `GET http://localhost:5107/api/WarehouseStructure/list` - 获取仓库结构
- `GET http://localhost:5107/api/Person/list` - 获取人员列表

### RBAC系统API (读写分离)
- **读取操作** (5062端口)：
  - `GET http://localhost:5062/api/User/GetUser`
  - `GET http://localhost:5062/api/Role/GetRole`
  - `GET http://localhost:5062/api/Permission/GetPermission`

- **写入操作** (5251端口)：
  - `POST http://localhost:5251/api/User/AddUser`
  - `POST http://localhost:5251/api/Role/AddRole`
  - `POST http://localhost:5251/api/Permission/CreatePermission`

## 验证步骤

### 1. 确认后端服务运行状态
```bash
# 检查RBAC读取服务
curl http://localhost:5062/api/User/GetUser

# 检查RBAC写入服务
curl http://localhost:5251/api/User/AddUser

# 检查仓库管理服务
curl http://localhost:5107/api/Warehouselist
```

### 2. 前端功能验证
1. ✅ 仓库列表加载
2. ✅ 新增仓库功能
3. ✅ 编辑仓库功能
4. ✅ 删除仓库功能
5. ✅ 选项数据加载（分类、类型等）

## 注意事项

### 1. 服务启动顺序
确保以下服务都正常运行：
- RBAC读取服务：5062端口
- RBAC写入服务：5251端口
- 仓库管理服务：5107端口

### 2. 认证机制
所有API调用仍需要Bearer Token认证，确保：
- 用户已正确登录
- Token有效且未过期
- 请求头包含正确的Authorization信息

### 3. CORS配置
确保5107端口的后端服务允许前端域名的跨域请求

### 4. 防火墙设置
确保5107端口未被防火墙阻止

## 故障排除

### 如果仍显示"API不存在"
1. **检查后端服务**：确认5107端口服务正常运行
2. **检查API路径**：确认后端API路径是否为`/api/Warehouselist`
3. **检查网络连接**：使用浏览器或curl测试API连接
4. **检查认证**：确认Token是否有效

### 常见错误及解决方案
- **404错误**：检查API路径和端口号
- **401错误**：检查Token认证
- **CORS错误**：检查后端CORS配置
- **网络错误**：检查服务是否启动

## 环境配置

### 开发环境
```env
# RBAC系统
VITE_ReadAPP_API_URL=http://localhost:5062/
VITE_WriteAPP_API_URL=http://localhost:5251/
VITE_UploadAPP_API_URL=http://localhost:5142/

# 仓库管理系统在代码中硬编码为5107端口
```

### 生产环境
如需部署到生产环境，需要：
1. 修改`warehouseApiUrl`为生产环境地址
2. 或者将其配置为环境变量
3. 确保所有端口在生产环境中正确配置

## 总结

现在系统正确配置了不同模块使用不同的端口：
- **RBAC模块**：读写分离架构（5062/5251）
- **仓库管理模块**：统一端口架构（5107）

这种混合架构允许不同模块根据自身需求选择合适的部署方式。
