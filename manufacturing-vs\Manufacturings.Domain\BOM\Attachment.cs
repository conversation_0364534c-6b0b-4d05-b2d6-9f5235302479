using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.BOM
{
    /// <summary>
    /// 附件表
    /// </summary>
    public class Attachment : BaseEntity
    {
        /// <summary>
        /// 附件文件名
        /// </summary>
        public string Filename { get; set; } = string.Empty;

        /// <summary>
        /// 附件路径
        /// </summary>
        public string Filepath { get; set; } = string.Empty;

        /// <summary>
        /// 附件大小
        /// </summary>
        public long Filesize { get; set; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime Uploadtime { get; set; }

        /// <summary>
        /// 业务类型
        /// </summary>
        public string Entitytype { get; set; } = string.Empty;

        /// <summary>
        /// 业务ID
        /// </summary>
        public long Entityid { get; set; }
    }
}
