2025-06-25 09:29:46.6343 INFO 用户及其角色添加成功！
2025-06-25 09:30:04.2460 INFO 用户及其角色添加成功！
2025-06-25 09:30:44.3616 INFO 用户及其角色添加成功！
2025-06-25 09:30:47.7217 INFO 用户及其角色添加成功！
2025-06-25 09:30:50.2700 INFO 用户及其角色添加成功！
2025-06-25 09:30:56.7936 INFO 用户及其角色添加成功！
2025-06-25 09:31:01.0450 INFO 用户及其角色添加成功！
2025-06-25 09:40:02.4163 INFO 开始处理创建权限请求，权限名称：溯源管理
2025-06-25 09:40:02.9098 INFO 创建权限成功，权限ID：5，权限名称：溯源管理
2025-06-25 09:54:31.2497 INFO 角色及权限修改成功！
2025-06-25 09:56:11.3470 INFO 开始处理更新权限请求，权限ID：5，权限名称：溯源管理
2025-06-25 09:56:11.7511 WARN 更新权限失败：权限名称已存在，权限ID：5，权限名称：溯源管理
2025-06-25 09:56:14.3665 INFO 开始处理更新权限请求，权限ID：5，权限名称：溯源管理1
2025-06-25 09:56:14.4616 INFO 更新权限成功，权限ID：5，权限名称：溯源管理1
2025-06-25 09:56:19.5785 INFO 开始处理更新权限请求，权限ID：5，权限名称：溯源管理
2025-06-25 09:56:19.5982 INFO 更新权限成功，权限ID：5，权限名称：溯源管理
2025-06-25 09:59:52.8003 INFO 用户及其角色添加成功！
2025-06-25 09:59:56.8915 INFO 用户及其角色添加成功！
2025-06-25 10:01:18.3924 INFO 角色及权限修改成功！
2025-06-25 10:01:23.1510 INFO 角色及权限修改成功！
2025-06-25 10:01:33.6567 INFO 角色及权限修改成功！
2025-06-25 11:03:04.8412 INFO 角色及权限修改成功！
2025-06-25 11:03:04.8874 INFO 角色及权限修改成功！
2025-06-25 11:03:05.0179 INFO 角色及权限修改成功！
2025-06-25 11:03:13.9119 INFO 角色及权限修改成功！
2025-06-25 11:03:17.4498 INFO 角色及权限修改成功！
2025-06-25 11:03:17.9867 INFO 角色及权限修改成功！
2025-06-25 11:03:18.5409 INFO 角色及权限修改成功！
2025-06-25 11:03:30.5938 INFO 角色及权限修改成功！
2025-06-25 11:19:25.5028 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:19:25.9820 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:19:26.9557 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:20:10.5232 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:21:00.4219 INFO 角色及权限修改成功！
2025-06-25 11:21:01.7720 INFO 角色及权限修改成功！
2025-06-25 11:21:12.8100 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:22:02.2601 INFO 角色及权限修改成功！
2025-06-25 11:22:05.6197 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:22:16.5784 ERROR 用户状态修改时时出现异常:Missing type map configuration or unsupported mapping.

Mapping types:
UpdateUserStateCommand -> UserModel
Traceability.API.Write.Application.Command.User.UpdateUserStateCommand -> Traceability.Domain.RBAC.UserModel
2025-06-25 11:24:30.1715 INFO 用户状态修改成功！
2025-06-25 11:24:33.1699 INFO 用户状态修改成功！
2025-06-25 11:24:33.7574 INFO 用户状态修改成功！
2025-06-25 11:34:13.4451 INFO 用户及其角色添加成功！
2025-06-25 11:34:18.6342 INFO 用户状态修改成功！
2025-06-25 11:34:35.8763 INFO 用户及其角色添加成功！
2025-06-25 11:34:49.3906 INFO 角色及权限添加成功！
2025-06-25 11:34:59.2007 INFO 角色及权限修改成功！
2025-06-25 11:35:02.9405 INFO 角色及权限删除成功！
2025-06-25 11:35:24.2328 INFO 开始处理创建权限请求，权限名称：测试权限
2025-06-25 11:35:24.2729 INFO 创建权限成功，权限ID：6，权限名称：测试权限
2025-06-25 11:43:56.8235 INFO 用户及其角色删除成功！
2025-06-25 11:48:32.5875 INFO 开始处理创建权限请求，权限名称：123
2025-06-25 11:48:33.1292 INFO 创建权限成功，权限ID：7，权限名称：123
2025-06-25 11:48:36.4464 INFO 开始处理删除权限请求，权限ID：7
2025-06-25 11:48:36.4880 INFO 删除权限成功，权限ID：7
2025-06-25 19:24:33.9730 INFO 用户状态修改成功！
2025-06-25 19:24:41.3804 INFO 用户状态修改成功！
