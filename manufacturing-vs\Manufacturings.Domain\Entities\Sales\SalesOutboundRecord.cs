using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Warehouses;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售出库记录表
    /// </summary>
    public class SalesOutboundRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 出库单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 出库订单主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string OutboundOrderSubject { get; set; } = string.Empty;

        /// <summary>
        /// 出库日期
        /// </summary>
        [Required]
        public DateTime OutboundDate { get; set; }

        /// <summary>
        /// 出库类型（销售出库、调拨出库、其他出库等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string OutboundType { get; set; } = string.Empty;

        /// <summary>
        /// 客户名称
        /// </summary>
        [Required]
        [StringLength(200)]
        public string CustomerName { get; set; } = string.Empty;

        /// <summary>
        /// 出库总量
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalOutboundQuantity { get; set; }

        /// <summary>
        /// 出库金额
        /// </summary>
        [Required]
        [Column(TypeName = "decimal(18,2)")]
        public decimal OutboundAmount { get; set; }

        /// <summary>
        /// 出库人员ID
        /// </summary>
        public long? OutboundPersonId { get; set; }

        /// <summary>
        /// 出库仓库ID
        /// </summary>
        public long? OutboundWarehouseId { get; set; }

        /// <summary>
        /// 出库状态（待出库、出库中、已完成、已取消等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string OutboundStatus { get; set; } = "待出库";

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 