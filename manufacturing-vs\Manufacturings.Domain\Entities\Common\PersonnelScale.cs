using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Common
{
    /// <summary>
    /// 人员规模表
    /// </summary>
    public class PersonnelScale : BaseEntity
    {
        /// <summary>
        /// 规模名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ScaleName { get; set; } = string.Empty;

        /// <summary>
        /// 规模编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string ScaleCode { get; set; } = string.Empty;

        /// <summary>
        /// 人员数量范围
        /// </summary>
        [StringLength(100)]
        public string? PersonnelRange { get; set; }

        /// <summary>
        /// 规模描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 