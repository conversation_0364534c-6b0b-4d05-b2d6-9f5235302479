using Manufacturings.Domain;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Three
{
    /// <summary>
    /// 审批流程实体
    /// </summary>
    [Table("ApprovalProcess")]
    public class ApprovalProcess : BaseEntity
    {
        /// <summary>
        /// 所属表单
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AssociatedForm { get; set; } = string.Empty;

        /// <summary>
        /// 审批流程名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ProcessName { get; set; } = string.Empty;

        /// <summary>
        /// 审批说明
        /// </summary>
        [StringLength(500)]
        public string ApprovalDescription { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 修改人
        /// </summary>
        [StringLength(50)]
        public string UpdateName { get; set; } = string.Empty;
    }
}

