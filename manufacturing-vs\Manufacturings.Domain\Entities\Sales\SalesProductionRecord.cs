using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售生产记录表
    /// </summary>
    public class SalesProductionRecord : BaseEntity
    {
        /// <summary>
        /// 销售订单ID
        /// </summary>
        [Required]
        public long SalesOrderId { get; set; }

        /// <summary>
        /// 生产单号
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProductionOrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 生产主题
        /// </summary>
        [Required]
        [StringLength(200)]
        public string ProductionSubject { get; set; } = string.Empty;

        /// <summary>
        /// 生产类型（内部生产、委外生产、其他等）
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ProductionType { get; set; } = string.Empty;

        /// <summary>
        /// 单据日期
        /// </summary>
        [Required]
        public DateTime DocumentDate { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public long? PersonInChargeId { get; set; }

        /// <summary>
        /// 所在部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 生产状态（未开始、进行中、已完成、已暂停、已取消等）
        /// </summary>
        [Required]
        [StringLength(50)]
        public string ProductionStatus { get; set; } = "未开始";

        /// <summary>
        /// 计划开始日期
        /// </summary>
        public DateTime? PlannedStartDate { get; set; }

        /// <summary>
        /// 计划完成日期
        /// </summary>
        public DateTime? PlannedEndDate { get; set; }

        /// <summary>
        /// 实际开始日期
        /// </summary>
        public DateTime? ActualStartDate { get; set; }

        /// <summary>
        /// 实际完成日期
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// 生产数量
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal ProductionQuantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 