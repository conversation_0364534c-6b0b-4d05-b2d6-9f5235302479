using Manufacturings.Domain;
using Manufacturings.Domain.Entities.Common;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 生产计划表
    /// </summary>
    [Table("ProductionPlan")]
    public class ProductionPlan : BaseEntity
    {
        /// <summary>
        /// 计划单号
        /// </summary>
        [StringLength(50)]
        public string Plancode { get; set; }

        /// <summary>
        /// 计划主题
        /// </summary>
        [StringLength(200)]
        public string Plantheme { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? Plandate { get; set; }

        /// <summary>
        /// 关联销售订单ID
        /// </summary>
        public long? SalesOrderId { get; set; }

        /// <summary>
        /// 关联项目Id
        /// </summary>
        public long? ProjectId { get; set; }

        /// <summary>
        /// 是否绑定销售订单
        /// </summary>
        public bool Isbound { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string Remark { get; set; }

    }
}


