<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Traceability.API.Read</name>
    </assembly>
    <members>
        <member name="P:Traceability.API.Read.Application.Command.Breeding.BreedingOneQueryCommand.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Enterprise.EnterpriseQueryCommand.ModuleType">
            <summary>
            模块类型
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Permission.GetPermissionCommand.PermissionsName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Permission.GetPermissionCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Permission.GetPermissionCommand.PageSize">
            <summary>
            页容量
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Application.Command.Product.ProductQueryCommand">
            <summary>
            产品查询命令
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.PageIndex">
            <summary>
            页码，从1开始
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.PageSize">
            <summary>
            每页记录数
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.GoodsId">
            <summary>
            商品名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.ShelfLife">
            <summary>
            保质期(天)
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.ProductionLicenseNo">
            <summary>
            生产许可证号
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.ManufacturerName">
            <summary>
            生产企业
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.CreateName">
            <summary>
            创建人姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.StartTime">
            <summary>
            创建时间(开始)
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.EndTime">
            <summary>
            创建时间（结束）
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Product.ProductQueryCommand.ModifierName">
            <summary>
            修改人姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Role.RoleQueryCommand.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Role.RoleQueryCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Role.RoleQueryCommand.PageSize">
            <summary>
            页容量
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.Slaughter.SlaughterOneQueryCommand.ProductId">
            <summary>
            产品ID
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Application.Command.User.LoginUserCommand">
            <summary>
            
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.LoginUserCommand.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.LoginUserCommand.PassWord">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.RefreshTokenQueryCommand.AccessToken">
            <summary>
            授权token
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.RefreshTokenQueryCommand.RefreshToken">
            <summary>
            刷新token
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.UserQueryCommand.NickName">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.UserQueryCommand.PageIndex">
            <summary>
            页码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Application.Command.User.UserQueryCommand.PageSize">
            <summary>
            页容量
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Breeding.BreedingOneQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Breeding.BreedingOneQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Enterprise.EnterpriseQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Enterprise.EnterpriseQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Healthcare.HealthcareQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Healthcare.HealthcareQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">接口</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Permission.GetMenuPermissionCommandHandler.GetCascade(System.Int64,System.Collections.Generic.List{Traceability.Domain.RBAC.PermissionModel})">
            <summary>
            递归获取菜单
            </summary>
            <param name="parentId">父级编号</param>
            <param name="permissions">权限信息</param>
            <returns>返回级联程序集合</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Processing.ProcessingQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Processing.ProcessingQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Processing.ProductionMaterialQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Processing.ProductionMaterialQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Product.GoodsQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Product.GoodsQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Product.ProductOneQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Product.ProductOneQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Product.ProductQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Product.ProductQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Role.RoleAllQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Role.RoleAllQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Role.RoleQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Role.RoleQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.Slaughter.SlaughterOneQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.Slaughter.SlaughterOneQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.ExportCommandHandler._userRepository">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.ExportCommandHandler._userRoleRepository">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.ExportCommandHandler._roleRepository">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.ExportCommandHandler.mapper">
            <summary>
            映射
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.ExportCommandHandler._logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.ExportCommandHandler.#ctor(AutoMapper.IMapper,Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserRoleModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.RoleModel},Microsoft.Extensions.Logging.ILogger{Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler})">
            <summary>
            构造方法
            </summary>
            <param name="mapper">映射</param>
            <param name="userRepository">用户仓储</param>
            <param name="userRoleRepository">用户角色仓储</param>
            <param name="roleRepository">角色仓储</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.ExportCommandHandler.Handle(Traceability.API.Read.Application.Command.User.ExportCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.userRepository">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.userRoleRepository">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.mapper">
            <summary>
            映射
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.roleRepository">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler._tokenServices">
            <summary>
            Token服务类
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler._logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserRoleModel},AutoMapper.IMapper,Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.RoleModel},Traceability.API.Read.Common.TokenServices,Microsoft.Extensions.Logging.ILogger{Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler})">
            <summary>
            构造方法
            </summary>
            <param name="userRepository">用户仓储</param>
            <param name="userRoleRepository">用户角色仓储</param>
            <param name="mapper">映射</param>
            <param name="roleRepository">角色仓储</param>
            <param name="tokenServices">角色仓储</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.LoginUserCommandHandler.Handle(Traceability.API.Read.Application.Command.User.LoginUserCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler.userRepository">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler.userRoleRepository">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler.mapper">
            <summary>
            映射
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler.roleRepository">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler._tokenServices">
            <summary>
            Token服务类
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler._logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler.#ctor(Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserRoleModel},AutoMapper.IMapper,Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.RoleModel},Traceability.API.Read.Common.TokenServices,Microsoft.Extensions.Logging.ILogger{Traceability.API.Read.Application.Handler.User.RefreshTokenQueryCommandHandler})">
            <summary>
            构造方法
            </summary>
            <param name="userRepository">用户仓储</param>
            <param name="userRoleRepository">用户角色仓储</param>
            <param name="mapper">映射</param>
            <param name="roleRepository">角色仓储</param>
            <param name="tokenServices">角色仓储</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler._userRepository">
            <summary>
            用户仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler._userRoleRepository">
            <summary>
            用户角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler._roleRepository">
            <summary>
            角色仓储
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler.mapper">
            <summary>
            映射
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler._logger">
            <summary>
            日志服务
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler.#ctor(AutoMapper.IMapper,Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.UserRoleModel},Traceability.Infrastructure.IBaseRepository{Traceability.Domain.RBAC.RoleModel},Microsoft.Extensions.Logging.ILogger{Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler})">
            <summary>
            构造方法
            </summary>
            <param name="mapper">映射</param>
            <param name="userRepository">用户仓储</param>
            <param name="userRoleRepository">用户角色仓储</param>
            <param name="roleRepository">角色仓储</param>
            <param name="logger">日志服务</param>
        </member>
        <member name="M:Traceability.API.Read.Application.Handler.User.UserQueryCommandHandler.Handle(Traceability.API.Read.Application.Command.User.UserQueryCommand,System.Threading.CancellationToken)">
            <summary>
            处理
            </summary>
            <param name="request">请求</param>
            <param name="cancellationToken">取消</param>
            <returns>返回任务</returns>
        </member>
        <member name="F:Traceability.API.Read.Common.IdentifyService._contextAccessor">
            <summary>
            HttpContextAccessor
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Common.IdentifyService.#ctor(Microsoft.AspNetCore.Http.IHttpContextAccessor)">
            <summary>
            构造方法
            </summary>
            <param name="contextAccessor">HttpContextAccessor</param>
        </member>
        <member name="P:Traceability.API.Read.Common.IdentifyService.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.IdentifyService.UserName">
            <summary>
            用户姓名
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Common.IIdentifyService">
            <summary>
            定义身份服务的相关方法接口
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Common.JwtSettings">
            <summary>
            JWT配置类
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.JwtSettings.SecretKey">
            <summary>
            私钥
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.JwtSettings.Issuer">
            <summary>
            发布者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.JwtSettings.Audience">
            <summary>
            接收者信息
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.JwtSettings.AccessTokenExpirationMinutes">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.JwtSettings.RefreshExpireTime">
            <summary>
            访问令牌过期时间（分钟）
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Common.MappingProfile">
            <summary>
            AutoMapper映射配置
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Common.RefreshToken">
            <summary>
            续期的Token
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.RefreshToken.Token">
            <summary>
            生成一个唯一Id
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.RefreshToken.Expires">
            <summary>
            续期Token的过期时间
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.RefreshToken.Created">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Common.RefreshToken.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Common.TokenServices">
            <summary>
            封装Token帮助类
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Common.TokenServices.GenerateJwtToken(Traceability.API.Read.Dto.RBAC.LoginUserDto)">
            <summary>
            生成JWT令牌
            </summary>
            <param name="user"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Common.TokenServices.GenRefreshToken(System.Int64)">
            <summary>
            生成刷新Token
            </summary>
            <param name="userId"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Common.TokenServices.GetPrincipalFromExpiredToken(System.String)">
            <summary>
             解析token
            </summary>
            <param name="token"></param>
            <returns></returns>
            <exception cref="T:Microsoft.IdentityModel.Tokens.SecurityTokenException"></exception>
        </member>
        <member name="T:Traceability.API.Read.Controllers.BreedingController">
            <summary>
            养殖信息控制器
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.BreedingController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.BreedingController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.BreedingController.GetBredding(Traceability.API.Read.Application.Command.Breeding.BreedingOneQueryCommand)">
            <summary>
            获取对应产品的养殖信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.BreedingController.GetHealthcare(Traceability.API.Read.Application.Command.Healthcare.HealthcareQueryCommand)">
            <summary>
            获取对应养殖的保健信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.EnterpriseController">
            <summary>
            企业公共控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.EnterpriseController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.EnterpriseController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.EnterpriseController.GetEnterprise(Traceability.API.Read.Application.Command.Enterprise.EnterpriseQueryCommand)">
            <summary>
            获取对应的企业信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.HealthCheckController">
            <summary>
            API控制器-健康检查
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.HealthCheckController.Check">
            <summary>
            检测
            </summary>
            <returns>返回信息</returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.ProcessingController">
            <summary>
            深加工控制器-（读）
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.ProcessingController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProcessingController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProcessingController.GetProcessing(Traceability.API.Read.Application.Command.Processing.ProcessingQueryCommand)">
            <summary>
            获取对应产品的深加工信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProcessingController.GetProductionMaterial(Traceability.API.Read.Application.Command.Processing.ProductionMaterialQueryCommand)">
            <summary>
            获取对应深加工信息的供应商信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.ProductController">
            <summary>
            溯源控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.ProductController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProductController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProductController.GetGoods">
            <summary>
            获取商品列表
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProductController.GetProduct(Traceability.API.Read.Application.Command.Product.ProductQueryCommand)">
            <summary>
            获取产品列表
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.ProductController.GetOneProduct(Traceability.API.Read.Application.Command.Product.ProductOneQueryCommand)">
            <summary>
            获取单个产品信息
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.RBAC.LoginController">
            <summary>
            登录授权控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.RBAC.LoginController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.LoginController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.LoginController.Login(Traceability.API.Read.Application.Command.User.LoginUserCommand)">
            <summary>
            登录
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.LoginController.Refresh(Traceability.API.Read.Application.Command.User.RefreshTokenQueryCommand)">
            <summary>
            刷新Token
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.LoginController.Export">
            <summary>
            导出用户信息
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.RBAC.PermissionController">
            <summary>
            权限控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.RBAC.PermissionController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.PermissionController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.PermissionController.GetPermission(Traceability.API.Read.Application.Command.Permission.GetPermissionCommand)">
            <summary>
            获取权限列表
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.PermissionController.GetAllPermission">
            <summary>
            获取权限
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.PermissionController.GetCascadeItem(Traceability.API.Read.Application.Command.Permission.GetMenuPermissionCommand)">
            <summary>
            动态菜单权限
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.RBAC.RoleController">
            <summary>
            角色控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.RBAC.RoleController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.RoleController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.RoleController.GetRole(Traceability.API.Read.Application.Command.Role.RoleQueryCommand)">
            <summary>
            获取角色及角色权限列表
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.RoleController.GetAllRole">
            <summary>
            获取所有角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.RBAC.UserController">
            <summary>
            用户控制器-读
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.RBAC.UserController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.UserController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.RBAC.UserController.GetUser(Traceability.API.Read.Application.Command.User.UserQueryCommand)">
            <summary>
            获取用户及用户角色
            </summary>
            <param name="command"></param>
            <returns></returns>
        </member>
        <member name="T:Traceability.API.Read.Controllers.SlaughterController">
            <summary>
            屠宰控制器-（读）
            </summary>
        </member>
        <member name="F:Traceability.API.Read.Controllers.SlaughterController.mediator">
            <summary>
            中介者
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Controllers.SlaughterController.#ctor(MediatR.IMediator)">
            <summary>
            构造方法
            </summary>
            <param name="mediator">中介者</param>
        </member>
        <member name="M:Traceability.API.Read.Controllers.SlaughterController.GetSlaughter(Traceability.API.Read.Application.Command.Slaughter.SlaughterOneQueryCommand)">
            <summary>
            获取对应产品的屠宰信息
            </summary>
            <param name="command">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.Id">
            <summary>
            主键标识
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.GoodId">
            <summary>
            商品Id
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.GoodName">
            <summary>
            商品名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ProductionBatch">
            <summary>
            生产批次
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ShelfLife">
            <summary>
            保质期(天)
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ProductionLicenseNo">
            <summary>
            生产许可证号
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ManufacturerName">
            <summary>
            生产企业
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.InspectionReports">
            <summary>
            产品质检报告
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ProductImage">
            <summary>
            封面图片
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ProductId">
            <summary>
            关联产品ID
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.Species">
            <summary>
            品种
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.Source">
            <summary>
            来源
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.BreedingDays">
            <summary>
            饲养天数
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.FeedBrand">
            <summary>
            饲料品牌
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ResponsiblePerson">
            <summary>
            饲养负责人
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.OutboundDate">
            <summary>
            出栏日期(年月日)
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.QuarantineCertificates">
            <summary>
            动物检疫证明
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.CompletionItems">
            <summary>
            完成项数量
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.CreateName">
            <summary>
            创建人名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ModifierName">
            <summary>
            修改人名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.Product.ProductDto.ModifyTime">
            <summary>
            修改时间
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Dto.RBAC.FileResultDto">
            <summary>
            文件导出Dto
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.FileResultDto.FileContent">
            <summary>
            文件内容（字节数组）
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.FileResultDto.ContentType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.FileResultDto.FileName">
            <summary>
            文件名
            </summary>
        </member>
        <member name="T:Traceability.API.Read.Dto.RBAC.LoginUserDto">
            <summary>
            登录用户Dto
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.Id">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.UserId">
            <summary>
            用户Id
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.RoleId">
            <summary>
            角色Id
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.LoginUserDto.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.RoleDto.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.RoleDto.RoleState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.RoleDto.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.RoleDto.PermissionName">
            <summary>
            权限名称
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.RoleDto.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.TokenResponseDto.AccessToken">
            <summary>
            授权token
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.TokenResponseDto.RefreshToken">
            <summary>
            刷新token
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.UserState">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.RoleId">
            <summary>
            角色编号
            </summary>
        </member>
        <member name="P:Traceability.API.Read.Dto.RBAC.UserDto.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="M:Traceability.API.Read.Extensions.ServiceCollectionExtensions.AddDBContextAccessor``1(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.Boolean)">
            <summary>
            注册EFCore上下文
            </summary>
            <typeparam name="T"></typeparam>
            <param name="services"></param>
            <param name="connectionString">连接字符串</param>
            <param name="enablerSqlLog">是否启用SQL日志</param>
            <returns></returns>
        </member>
    </members>
</doc>
