using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Inventory
{
    /// <summary>
    /// 物品类别表
    /// </summary>
    public class ItemCategory : BaseEntity
    {
        /// <summary>
        /// 类别名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 类别编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string CategoryCode { get; set; } = string.Empty;

        /// <summary>
        /// 类别描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        // 导航属性
        /// <summary>
        /// 该类别下的供应商物品列表
        /// </summary>
        public virtual ICollection<SupplierItem> SupplierItems { get; set; } = new List<SupplierItem>();
    }
} 