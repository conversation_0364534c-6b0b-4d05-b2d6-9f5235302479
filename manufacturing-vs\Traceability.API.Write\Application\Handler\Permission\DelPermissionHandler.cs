﻿using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using Traceability.API.Write.Application.Command.Permission;
using Traceability.API.Write.Common;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;
using Yitter.IdGenerator;

namespace Traceability.API.Write.Application.Handler.Permission
{
    public class DelPermissionHandler : IRequestHandler<DelPermissionCommand, APIResult<object>>
    {
        private readonly IBaseRepository<PermissionModel> permissionRepository;
        private readonly IMapper mapper;
        private readonly ILogger<DelPermissionHandler> _logger;
        private readonly IIdentifyService identifyService;

        public DelPermissionHandler(
            IBaseRepository<PermissionModel> permissionRepository, 
            IMapper mapper,
            ILogger<DelPermissionHandler> logger,IIdentifyService identifyService)
        {
            this.permissionRepository = permissionRepository;
            this.mapper = mapper;
            this._logger = logger;
            this.identifyService = identifyService;
        }

        public async Task<APIResult<object>> Handle(DelPermissionCommand request, CancellationToken cancellationToken)
        {
            _logger.LogInformation($"开始处理删除权限请求，权限ID：{request.Id}");
            APIResult<object> result = new APIResult<object>();
            
            try
            {
                var model = mapper.Map<PermissionModel>(request);
                model.IsDeleted = true;
                model.ModifyTime = DateTime.Now;
                model.ModifierId = Convert.ToInt64(identifyService.UserId);
                await permissionRepository.UpdateAsync(model);
                
                result.Code = ResultCode.Success;
                result.Message = "删除成功";
                result.Data = 1;
                
                _logger.LogInformation($"删除权限成功，权限ID：{model.Id}");
                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除权限过程中出现异常：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "删除权限失败";
                return await Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
