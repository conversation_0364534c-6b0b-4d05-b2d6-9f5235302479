using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 内部生产表
    /// </summary>
    public class InternalProduction : BaseEntity
    {
        /// <summary>
        /// 生产主题
        /// </summary>
        public string? ProductionSubject { get; set; }

        /// <summary>
        /// 生产类型
        /// </summary>
        public string? ProductionType { get; set; }

        /// <summary>
        /// 生产单号
        /// </summary>
        public string? ProductionOrderNo { get; set; }

        /// <summary>
        /// 销售订单
        /// </summary>
        public long? SalesOrderNoId { get; set; }

        /// <summary>
        /// 单据日期
        /// </summary>
        public DateTime? DocumentDate { get; set; }

        /// <summary>
        /// 关联项目
        /// </summary>
        public string? AssociatedProject { get; set; }

        /// <summary>
        /// 所在部门
        /// </summary>
        public string? InitiatingDepartment { get; set; }

        /// <summary>
        /// 负责人
        /// </summary>
        public string? ResponsiblePerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 生产部门
        /// </summary>
        public string? ProductionDepartment { get; set; }

        /// <summary>
        /// 生产人员
        /// </summary>
        public string? ProductionPersonnel { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
}
