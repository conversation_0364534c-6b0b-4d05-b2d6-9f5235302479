﻿using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Traceability.API.Read.Application.Command.User;
using Traceability.API.Read.Common;
using Traceability.API.Read.Dto.RBAC;
using Traceability.Domain.RBAC;
using Traceability.ErrorCount;
using Traceability.Infrastructure;

namespace Traceability.API.Read.Application.Handler.User
{
    public class RefreshTokenQueryCommandHandler : IRequestHandler<RefreshTokenQueryCommand, APIResult<TokenResponseDto>>
    {
        /// <summary>
        /// 用户仓储
        /// </summary>
        private readonly IBaseRepository<UserModel> userRepository;
        /// <summary>
        /// 用户角色仓储
        /// </summary>
        private readonly IBaseRepository<UserRoleModel> userRoleRepository;
        /// <summary>
        /// 映射
        /// </summary>
        private readonly IMapper mapper;
        /// <summary>
        /// 角色仓储
        /// </summary>
        private readonly IBaseRepository<RoleModel> roleRepository;
        /// <summary>
        /// Token服务类
        /// </summary>
        private readonly TokenServices _tokenServices;
        /// <summary>
        /// 日志服务
        /// </summary>
        private readonly ILogger<RefreshTokenQueryCommandHandler> _logger;
        /// <summary>
        /// 构造方法
        /// </summary>
        /// <param name="userRepository">用户仓储</param>
        /// <param name="userRoleRepository">用户角色仓储</param>
        /// <param name="mapper">映射</param>
        /// <param name="roleRepository">角色仓储</param>
        /// <param name="tokenServices">角色仓储</param>
        /// <param name="logger">日志服务</param>
        public RefreshTokenQueryCommandHandler(
            IBaseRepository<UserModel> userRepository, 
            IBaseRepository<UserRoleModel> userRoleRepository, 
            IMapper mapper, 
            IBaseRepository<RoleModel> roleRepository, 
            TokenServices tokenServices,
            ILogger<RefreshTokenQueryCommandHandler> logger)
        {
            this.userRepository = userRepository;
            this.userRoleRepository = userRoleRepository;
            this.mapper = mapper;
            this.roleRepository = roleRepository;
            _tokenServices = tokenServices;
            _logger = logger;
        }
        public Task<APIResult<TokenResponseDto>> Handle(RefreshTokenQueryCommand request, CancellationToken cancellationToken)
        {
            APIResult<TokenResponseDto> result = new APIResult<TokenResponseDto>();
            
            try
            {
                //判断参数是否为空
                if (string.IsNullOrEmpty(request.AccessToken) || string.IsNullOrEmpty(request.RefreshToken))
                {
                    _logger.LogWarning("刷新Token失败：Token参数为空");
                    result.Code = ResultCode.Fail;
                    result.Message = "Token为空";
                    return Task.FromResult(result);
                }

                //// 从过期的accesstoken中获取principal（提取负载中的用户信息）
                var principal = _tokenServices.GetPrincipalFromExpiredToken(request.AccessToken);
                if (principal == null)
                {
                    _logger.LogWarning("刷新Token失败：无效的Token");
                    result.Code = ResultCode.Fail;
                    result.Message = "无效的Token";
                    return Task.FromResult(result);
                }

                var userId = Convert.ToInt64(principal.FindFirst("UserId").Value);
                var userName = principal.FindFirst("Nickname").Value;
                _logger.LogInformation($"刷新Token请求用户信息：用户ID={userId}，用户名={userName}");

                // 验证refreshtoken是否有效
                var storedRefreshToken = JsonConvert.DeserializeObject<RefreshToken>(request.RefreshToken);
                //验证是否有续期token  并且续期token是否过期 
                if (storedRefreshToken == null || storedRefreshToken.Expires < DateTime.Now)
                {
                    _logger.LogWarning($"刷新Token失败：刷新Token已过期，用户ID={userId}");
                    result.Code = ResultCode.Fail;
                    result.Message = "刷新Token已过期";
                    return Task.FromResult(result);
                }

                // 获取用户信息
                var user = userRepository.GetAll().AsNoTracking().FirstOrDefault(x=>x.Id==userId);

                if (user == null)
                {
                    _logger.LogWarning($"刷新Token失败：用户不存在，用户ID={userId}");
                    result.Code = ResultCode.Fail;
                    result.Message = "用户不存在";
                    return Task.FromResult(result);
                }

                var userDto = mapper.Map<LoginUserDto>(user);
                //获取角色ID
                var roleId = userRoleRepository.GetAll().Where(x => x.UserId == user.Id).Select(x => x.RoleId).ToList();
                userDto.RoleId = string.Join(",", roleId);
                userDto.RoleName = string.Join(",", roleRepository.GetAll().Where(x => roleId.Contains(x.Id)).Select(x => x.RoleName));

                

                // 生成新的token
                var newAccessToken = _tokenServices.GenerateJwtToken(userDto);
                //生成新的刷新Token
                var newRefreshToken = _tokenServices.GenRefreshToken(userId);

                result.Code = ResultCode.Success;
                result.Message = "Token刷新成功";
                result.Data = new TokenResponseDto
                {
                    AccessToken = newAccessToken,
                    RefreshToken = newRefreshToken,
                };
                
                _logger.LogInformation($"Token刷新成功，用户ID={userId}，用户名={userName}");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"刷新Token过程中出现异常：{ex.Message}");
                result.Code = ResultCode.Fail;
                result.Message = "刷新Token失败，服务器异常";
                return Task.FromResult(result);
            }
            finally
            {
                // 可以在这里添加必要的清理代码
            }
        }
    }
}
