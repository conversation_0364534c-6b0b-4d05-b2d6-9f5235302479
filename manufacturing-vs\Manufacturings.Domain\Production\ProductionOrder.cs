using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Production
{
    /// <summary>
    /// 生产工单表
    /// </summary>
    public class ProductionOrder : BaseEntity
    {
        /// <summary>
        /// 工单编号
        /// </summary>
        public string? order_number { get; set; }

        /// <summary>
        /// 关联生产单号
        /// </summary>
        public string? Related_Rproduction_order { get; set; }

        /// <summary>
        /// 工单名称
        /// </summary>
        public string? Production_name { get; set; }

        /// <summary>
        /// 关联项目名称
        /// </summary>
        public string? related_project { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? notes { get; set; }

        /// <summary>
        /// 销售订单
        /// </summary>
        public string? sales_order { get; set; }

        /// <summary>
        /// 工单日期
        /// </summary>
        public DateTime? order_date { get; set; }

        /// <summary>
        /// 所属部门
        /// </summary>
        public long? departmentId { get; set; }
    }
}
