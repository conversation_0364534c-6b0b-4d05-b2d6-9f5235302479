using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Inbound
{
    /// <summary>
    /// 其他入库记录表
    /// </summary>
    public class OtherInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public int InboundRecordId { get; set; }

        /// <summary>
        /// 入库原因
        /// </summary>
        [StringLength(500)]
        public string? InboundReason { get; set; }

        /// <summary>
        /// 来源类型
        /// </summary>
        [StringLength(100)]
        public string? SourceType { get; set; }

        /// <summary>
        /// 来源描述
        /// </summary>
        [StringLength(500)]
        public string? SourceDescription { get; set; }

        /// <summary>
        /// 特殊处理要求
        /// </summary>
        [StringLength(500)]
        public string? SpecialHandlingRequirements { get; set; }

        /// <summary>
        /// 其他备注
        /// </summary>
        [StringLength(500)]
        public string? OtherRemarks { get; set; }
    }
} 