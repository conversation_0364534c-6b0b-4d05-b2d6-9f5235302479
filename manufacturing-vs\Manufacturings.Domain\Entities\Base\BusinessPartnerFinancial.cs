using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 通用业务伙伴财务信息表
    /// </summary>
    public class BusinessPartnerFinancial : BaseEntity
    {
        /// <summary>
        /// 业务伙伴ID
        /// </summary>
        [Required]
        public long BusinessPartnerId { get; set; }

        /// <summary>
        /// 业务伙伴类型（客户/供应商）
        /// </summary>
        [Required]
        public BusinessPartnerType BusinessPartnerType { get; set; }

        /// <summary>
        /// 开户银行
        /// </summary>
        [StringLength(100)]
        public string? BankName { get; set; }

        /// <summary>
        /// 银行账号
        /// </summary>
        [StringLength(50)]
        public string? BankAccountNumber { get; set; }

        /// <summary>
        /// 开户名
        /// </summary>
        [StringLength(100)]
        public string? AccountHolderName { get; set; }

        /// <summary>
        /// 银行支行
        /// </summary>
        [StringLength(100)]
        public string? BankBranch { get; set; }

        /// <summary>
        /// 税号
        /// </summary>
        [StringLength(50)]
        public string? TaxNumber { get; set; }

        /// <summary>
        /// 信用额度
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? CreditLimit { get; set; }

        /// <summary>
        /// 已用信用额度
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? UsedCreditLimit { get; set; }

        /// <summary>
        /// 可用信用额度
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? AvailableCreditLimit { get; set; }

        /// <summary>
        /// 付款条件（天数）
        /// </summary>
        public int? PaymentTerms { get; set; }

        /// <summary>
        /// 信用等级（A/B/C/D）
        /// </summary>
        [StringLength(1)]
        public string? CreditRating { get; set; }

        /// <summary>
        /// 是否允许赊销
        /// </summary>
        public bool AllowCredit { get; set; } = true;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500)]
        public string? Remarks { get; set; }
    }
} 