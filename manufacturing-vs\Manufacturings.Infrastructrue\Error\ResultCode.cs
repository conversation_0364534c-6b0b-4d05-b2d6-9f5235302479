﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Manufacturings.Infrastructrue.Error
{
    /// <summary>
    /// 返回码
    /// </summary>
    public enum ResultCode
    {

        [Description("成功")]
        Success = 200,
        [Description("失败")]
        Fail = 500,
        //[Description("登录成功")]
        //LoginSuccess = 100,
        //[Description("登录失败")]
        //LoginFail = 101,
        //[Description("查询成功")]
        //SearchSuccess = 200,
        //[Description("查询失败")]
        //SearchFail = 201,
        //[Description("添加成功")]
        //AddSuccess = 300,
        //[Description("添加失败")]
        //AddFail = 301,
        //[Description("更新成功")]
        //UpdateSuccess = 400,
        //[Description("更新失败")]
        //UpdateFail = 401,
        //[Description("删除成功")]
        //DeleteSuccess = 500,
        //[Description("删除失败")]
        //DeleteFail = 501,
    }
}
