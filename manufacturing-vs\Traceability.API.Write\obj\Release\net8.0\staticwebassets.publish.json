{"Version": 1, "Hash": "Lajf56tcbl7lNu/ppBhP6HU7b740JVt5XujwAz+s+HU=", "Source": "Traceability.API.Write", "BasePath": "_content/Traceability.API.Write", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Publish", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Traceability.API.Write\\wwwroot", "Source": "Traceability.API.Write", "ContentRoot": "D:\\物联网代码\\专高六\\溯源项目\\Traceability\\Traceability.API.Write\\wwwroot\\", "BasePath": "_content/Traceability.API.Write", "Pattern": "**"}], "Assets": []}