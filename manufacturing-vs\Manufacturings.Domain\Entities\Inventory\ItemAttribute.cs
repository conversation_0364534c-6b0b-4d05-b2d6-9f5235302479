using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Inventory
{
    /// <summary>
    /// 物品属性表
    /// </summary>
    public class ItemAttribute : BaseEntity
    {
        /// <summary>
        /// 属性名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string AttributeName { get; set; } = string.Empty;

        /// <summary>
        /// 属性编码
        /// </summary>
        [Required]
        [StringLength(20)]
        public string AttributeCode { get; set; } = string.Empty;

        /// <summary>
        /// 属性描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        // 导航属性
        /// <summary>
        /// 该属性下的供应商物品列表
        /// </summary>
        public virtual ICollection<SupplierItem> SupplierItems { get; set; } = new List<SupplierItem>();
    }
} 