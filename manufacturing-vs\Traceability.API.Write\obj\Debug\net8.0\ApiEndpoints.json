[{"ContainingType": "Traceability.API.Write.Controllers.RBAC.PermissionController", "Method": "CreatePermission", "RelativePath": "api/Permission/CreatePermission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.Permission.CreatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.PermissionController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Permission/Handle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.Permission.DelPermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.PermissionController", "Method": "UpdatePermission", "RelativePath": "api/Permission/UpdatePermission", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Traceability.API.Write.Application.Command.Permission.UpdatePermissionCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.RoleController", "Method": "AddRole", "RelativePath": "api/Role/AddRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.Role.CreateRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.RoleController", "Method": "DeleteRole", "RelativePath": "api/Role/DeleteRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.Role.DeleteRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.RoleController", "Method": "UpdateRole", "RelativePath": "api/Role/UpdateRole", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.Role.UpdateRoleCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.RoleController", "Method": "UpdateRoleState", "RelativePath": "api/Role/UpdateRoleState", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.Role.UpdateRoleStateCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.UserController", "Method": "AddUser", "RelativePath": "api/User/AddUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.User.CreateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.UserController", "Method": "DeleteUser", "RelativePath": "api/User/DeleteUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.User.DeleteUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.UserController", "Method": "UpdateUser", "RelativePath": "api/User/UpdateUser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.User.UpdateUserCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Traceability.API.Write.Controllers.RBAC.UserController", "Method": "UpdateUserState", "RelativePath": "api/User/UpdateUserState", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "command", "Type": "Traceability.API.Write.Application.Command.User.UpdateUserStateCommand", "IsRequired": true}], "ReturnTypes": [{"Type": "Traceability.ErrorCount.APIResult`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]