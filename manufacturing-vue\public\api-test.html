<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { background-color: #fff3cd; border-color: #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>仓库管理API测试</h1>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>确保后端服务运行在 http://localhost:5107</li>
            <li>确保数据库脚本已执行</li>
            <li>点击下面的按钮测试各个API</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>API测试</h2>
        <button onclick="testAPI('WarehouseCategory')">测试仓库分类API</button>
        <button onclick="testAPI('StorageType')">测试存储类型API</button>
        <button onclick="testAPI('WarehouseStructure')">测试仓库结构API</button>
        <button onclick="testAPI('Person')">测试人员API</button>
        <button onclick="testAllAPIs()">测试所有API</button>
    </div>

    <div id="results"></div>

    <script>
        const baseUrl = 'http://localhost:5107/api';
        
        async function testAPI(endpoint) {
            const resultDiv = document.getElementById('results');
            const testSection = document.createElement('div');
            testSection.className = 'test-section loading';
            testSection.innerHTML = `
                <h3>测试 ${endpoint} API</h3>
                <div class="status">正在测试...</div>
            `;
            resultDiv.appendChild(testSection);

            try {
                const url = `${baseUrl}/${endpoint}/list?pageSize=10`;
                console.log(`测试URL: ${url}`);
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    testSection.className = 'test-section success';
                    testSection.innerHTML = `
                        <h3>✅ ${endpoint} API - 成功</h3>
                        <div class="status">状态码: ${response.status}</div>
                        <div class="status">数据条数: ${data.data?.pageData?.length || data.data?.length || 0}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    testSection.className = 'test-section error';
                    testSection.innerHTML = `
                        <h3>❌ ${endpoint} API - 失败</h3>
                        <div class="status">状态码: ${response.status}</div>
                        <div class="status">错误信息: ${response.statusText}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                testSection.className = 'test-section error';
                testSection.innerHTML = `
                    <h3>❌ ${endpoint} API - 网络错误</h3>
                    <div class="status">错误: ${error.message}</div>
                    <div>请检查:</div>
                    <ul>
                        <li>后端服务是否运行在 http://localhost:5107</li>
                        <li>控制器是否正确部署</li>
                        <li>数据库连接是否正常</li>
                    </ul>
                `;
            }
        }

        async function testAllAPIs() {
            document.getElementById('results').innerHTML = '';
            const apis = ['WarehouseCategory', 'StorageType', 'WarehouseStructure', 'Person'];
            
            for (const api of apis) {
                await testAPI(api);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
        }

        // 页面加载时显示说明
        window.onload = function() {
            console.log('API测试页面已加载');
            console.log('请确保后端服务运行在 http://localhost:5107');
        };
    </script>
</body>
</html>
