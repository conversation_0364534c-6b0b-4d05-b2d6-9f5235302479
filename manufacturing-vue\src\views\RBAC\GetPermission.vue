<template>
    <div class="rbac-container">
    <el-container>
      <el-header class="search-header">
        <el-form :inline="true" :model="query" class="search-form">
        <el-form-item label="权限名称">
          <el-input v-model="query.PermissionsName" placeholder="请输入权限名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" :icon="Search">查询</el-button>
          <el-button type="success" @click="open(null)" :icon="Plus">新增</el-button>
        </el-form-item>
      </el-form>
      </el-header>
      <el-main class="data-main">
           <el-table :data="tableData" class="data-table" :border="true" :stripe="true">
              <el-table-column type="index" label="权限编号" width="120px" align="center"/>
              <el-table-column prop="permissionName" label="权限名称" />
              <el-table-column prop="permissionUrl" label="权限URL" />
              <el-table-column prop="orderNo" label="权限序号" width="100px" align="center"/>
              <el-table-column label="操作" width="180px" align="center">
                <template v-slot="scoped">
                  <el-button link type="danger" size="small" @click="deletePermissions(scoped.row)" :icon="Delete">删除</el-button>
                  <el-button link type="primary" size="small" @click="open(scoped.row)" :icon="Edit">修改</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- 分页 -->
            <el-pagination
              class="pagination"
              v-model:current-page="page.PageIndex"
              v-model:page-size="page.PageSize"
              :page-sizes="[3, 5, 8]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              prev-text="上一页"
              next-text="下一页"
              :total="page.totalCount"
            />
            <!-- 对话框 -->
            <el-dialog v-model="logic.isopen" :title="logic.title" width="500" destroy-on-close center class="custom-dialog">
              <el-form
                  ref="ruleFormRef"
                  class="custom-form"
                  :model="ruleForm"
                  :rules="rules"
                  label-width="120"
                >
                  <el-form-item label="权限名称" prop="permissionName">
                    <el-input v-model="ruleForm.permissionName" placeholder="请输入权限名称" />
                  </el-form-item>
                  <el-form-item label="权限URL" prop="permissionUrl">
                    <el-input v-model="ruleForm.permissionUrl" placeholder="请输入权限URL" />
                  </el-form-item>
                  <el-form-item label="权限序号" prop="orderNo">
                    <el-input v-model="ruleForm.orderNo" placeholder="请输入权限序号" />
                  </el-form-item>

                   <el-form-item label="父级菜单" prop="parentId">
                      <!-- <el-select v-model="ruleForm.parentId" placeholder="请选择父级菜单">
                        <el-option label="暂无" :value="0" />
                        <el-option v-for="item in tableData" :key="item.id" :label="item.permissionName" :value="item.id" />
                      </el-select> -->
                      <el-cascader v-model="ruleForm.parentId" :options="permissions" :props="props1" style="width: 500px;" clearable />
                    </el-form-item>
                  <el-form-item class="form-buttons">
                    <el-button type="primary" @click="submitForm(ruleFormRef)" :icon="Check">
                      保存
                    </el-button>
                    <el-button @click="logic.isopen=false" :icon="Close">返回</el-button>
                  </el-form-item>
                </el-form>
            </el-dialog>
      </el-main>
    </el-container>
  </div>
 
</template>

<script lang="ts" setup>
import { addPermission,updatePermission,deletePermission,getPermission ,getMenu} from '@/Https/server';
import { onMounted, ref,reactive, watch } from 'vue';
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Search, Plus, Edit, Delete, Check, Close } from '@element-plus/icons-vue'
import { useCounterStore } from '@/stores/counter';
const store = useCounterStore();

onMounted(()=>{
  
    GetPermissionList();
})
//查询条件
const query =reactive({
    PermissionsName:''
})
const page =reactive({
    PageIndex:1,
    PageSize:3,
    totalCount:0,
    pageCount:0
})
//查询方法
const onSubmit = () => {
  GetPermissionList();
}

//侦听器
watch(page,()=>
{
  GetPermissionList();

},{deep:true})
//加载权限
const tableData = ref<any>([]);
const GetPermissionList=()=>{
    getPermission({PermissionsName:query.PermissionsName,PageIndex:page.PageIndex,PageSize:page.PageSize})
    .then((res:any)=>{
        tableData.value=res.data.pageData;
        page.totalCount = res.data.totalCount;
        page.pageCount =res.data.pageCount;
    })
}

const props1 = {
  checkStrictly: true,
  expandTrigger: 'hover' as const,
  emitPath:false
}
//加载级联下拉
const permissions =ref([])
const GetCascadeItem=()=>{
  getMenu({RoleId:null}).then((res:any)=>{
    
    permissions.value =res.data;
    console.log(permissions.value);
  })
}

//#region  增删改
//逻辑参数
const logic = reactive({
   isopen:false,
   isAdd:false,
   title:'',
})
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  "id": 0,
  "permissionName": "",
  "permissionUrl": "",
  "orderNo": '',
  "parentId": 0,
  "createId": 0,
  "createTime": ""
})
//数据验证
const rules = reactive<FormRules>({
  permissionName: [
    { required: true, message: '权限名称不能为空', trigger: 'blur' },
  ],
   permissionUrl: [
    { required: true, message: '权限URL不能为空', trigger: 'blur' },
  ],
   orderNo: [
    { required: true, message: '权限序号不能为空', trigger: 'blur' },
  ]
})
//提交
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if(logic.isAdd==true)
      {
         addPermission(ruleForm)
          .then((res:any)=>{
            if(res.code==200){
              logic.isopen =false;
              GetPermissionList();
              store.getMenus();
              ElMessage.success(res.message);
            }else{
              ElMessage.error(res.message);
            }
          })
      }
      else
      {
        updatePermission(ruleForm)
        .then((res:any)=>{
          if(res.code==200){
            logic.isopen =false;
            GetPermissionList();
            store.getMenus();
            ElMessage.success(res.message);
          }else{
            ElMessage.error(res.message);
          }
        })
      }
      
    } else {
      console.log('error submit!', fields)
    }
  })
}
//打开方法
const open =(row:any)=>{
  logic.isopen=true;
  GetCascadeItem();
  if(row==null)
  {
      logic.isAdd =true;
      logic.title="新增权限"
      ruleForm.id =0;
      ruleForm.orderNo ='';
      ruleForm.parentId =0;
      ruleForm.permissionName ='';
      ruleForm.permissionUrl ='';
  }
  else
  {
      logic.isAdd =false;
      logic.title="修改权限"
      ruleForm.id =row.id;
      ruleForm.orderNo =row.orderNo;
      ruleForm.permissionName =row.permissionName;
      ruleForm.permissionUrl =row.permissionUrl;
      ruleForm.parentId =row.parentId;
      ruleForm.createId = row.createId;
      ruleForm.createTime =row.createTime;
      if(row.parentId==0)
      {
          ruleForm.parentId =0;
      }
  }
}
//删除
const deletePermissions=(row:any)=>{
   ElMessageBox.confirm(
    '确认删除吗?',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
        deletePermission(row).then((res:any)=>{
          if(res.code==200)
          {
            GetPermissionList();
            store.getMenus();
            ElMessage.success(res.message);
          }
          else
          {
            ElMessage.error(res.message);
          }
        })
    })
  
}
//#endregion
</script>

<style>
.rbac-container {
  padding: 15px;
  background-color: #f5f7fa;
  min-height: 100%;
}
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.search-header {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 15px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.data-main {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  min-height: 500px;
}

.data-table {
  margin-bottom: 15px;
}

.custom-dialog .el-dialog__header {
  background-color: #f5f7fa;
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.custom-form {
  padding: 20px 0;
}

.form-buttons {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

/* 维持Element Plus输入框和选择框的宽度 */
.demo-form-inline .el-input {
  --el-input-width: 220px;
}

.demo-form-inline .el-select {
  --el-select-width: 220px;
}
</style>