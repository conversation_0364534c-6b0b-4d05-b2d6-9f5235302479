﻿namespace Traceability.API.Write.Common
{
    /// <summary>
    /// 续期的Token
    /// </summary>
    public class RefreshToken
    {
        /// <summary>
        /// 生成一个唯一Id
        /// </summary>
        public string Token { get; set; }
        /// <summary>
        /// 续期Token的过期时间
        /// </summary>
        public DateTime Expires { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime Created { get; set; }
        /// <summary>
        /// 用户Id
        /// </summary>
        public long UserId { get; set; }

    }
}
