
using Manufacturings.Domain.Entities.BusinessPartners;
using Manufacturings.Domain.Entities.Common;
using Manufacturings.Domain.Entities.Inbound;
using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Manufacturings.Domain.Entities.Sales
{
    /// <summary>
    /// 销售退货入库记录表
    /// </summary>
    public class SalesReturnInboundRecord : BaseEntity
    {
        /// <summary>
        /// 入库记录ID
        /// </summary>
        [Required]
        public long InboundRecordId { get; set; }

        /// <summary>
        /// 销售单号
        /// </summary>
        [StringLength(50)]
        public string? SalesOrderNumber { get; set; }

        /// <summary>
        /// 销售日期
        /// </summary>
        public DateTime? SalesDate { get; set; }

        /// <summary>
        /// 客户ID
        /// </summary>
        public long? CustomerId { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        [StringLength(50)]
        public string? ContactPerson { get; set; }

        /// <summary>
        /// 联系方式
        /// </summary>
        [StringLength(100)]
        public string? ContactMethod { get; set; }

        /// <summary>
        /// 客户经理ID
        /// </summary>
        public long? AccountManagerId { get; set; }

        /// <summary>
        /// 所属部门ID
        /// </summary>
        public long? DepartmentId { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [StringLength(20)]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 退货原因
        /// </summary>
        [StringLength(500)]
        public string? ReturnReason { get; set; }
    }
} 