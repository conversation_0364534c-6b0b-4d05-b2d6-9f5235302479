﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Traceability.Domain.RBAC
{
    /// <summary>
    /// 操作记录
    /// </summary>
    public class OperationRecord: BaseEntity
    {
        /// <summary>
        /// 操作名称
        /// </summary>
        public string OperationName { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        public string OperationMessage { get; set; }
        /// <summary>
        /// 操作模块 - BOM、物品等等
        /// </summary>
        public string OperationType { get; set; }
    }
}
