{"format": 1, "restore": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.API.Read\\Traceability.API.Read.csproj": {}}, "projects": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.API.Read\\Traceability.API.Read.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.API.Read\\Traceability.API.Read.csproj", "projectName": "Traceability.API.Read", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.API.Read\\Traceability.API.Read.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.API.Read\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\Traceability.ErrorCount.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\Traceability.ErrorCount.csproj"}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\Traceability.Infrastructure.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\Traceability.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[13.0.1, )"}, "IGeekFan.AspNetCore.Knife4jUI": {"target": "Package", "version": "[0.0.16, )"}, "MD5": {"target": "Package", "version": "[2.2.5, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.17, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.3, )"}, "NLog": {"target": "Package", "version": "[5.2.8, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[5.3.8, )"}, "NPOI": {"target": "Package", "version": "[2.7.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "Yitter.IdGenerator": {"target": "Package", "version": "[1.0.14, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\Traceability.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\Traceability.Domain.csproj", "projectName": "Traceability.Domain", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\Traceability.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\Traceability.ErrorCount.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\Traceability.ErrorCount.csproj", "projectName": "Traceability.ErrorCount", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\Traceability.ErrorCount.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.ErrorCount\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\Traceability.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\Traceability.Infrastructure.csproj", "projectName": "Traceability.Infrastructure", "projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\Traceability.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Infrastructrue\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\Traceability.Domain.csproj": {"projectPath": "D:\\实训一\\项目\\EMS\\manufacturing-vs\\Traceability.Domain\\Traceability.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.3, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}}}