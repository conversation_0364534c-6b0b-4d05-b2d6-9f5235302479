import { ref, computed, reactive } from 'vue'
import { defineStore } from 'pinia'
import { getMenu } from '@/Https/server';
import { useRouter } from 'vue-router';

export const useCounterStore = defineStore('counter', () => {
  const router = useRouter();
  const count = ref(0)
  const doubleCount = computed(() => count.value * 2)
  function increment() {
    count.value++
  }
  const userInfo=reactive({
    Nickname:'',
    UserName:'',
    PassWord:'',
    roleName:'',
    roleId:'',
  })
  //菜单Model
  const menus =ref<any>([]);
  //动态加载菜单
  const getMenus=()=>{
    getMenu({RoleId:userInfo.roleId}).then((res:any)=>{
        menus.value =res.data;
        return menus.value;
    })
  }
  
  // 清除标签页状态的方法
  const clearTabsState = () => {
    tabs.value = [];
    activeTab.value = '';
  }

  // 定义标签页状态
  interface TabItem {
    title: string;   // 标签页标题
    name: string;    // 标签页名称/路由路径
    closable: boolean; // 是否可关闭
    params?: any;    // 可能包含的额外参数
    type?: string;   // 标签页类型，区分是列表页还是详情页
  }

  const activeTab = ref('');
  const tabs = ref<TabItem[]>([]);

  // 添加标签页
  const addTab = (menuItem: any) => {
    // 适配不同的字段名
    const tabName = menuItem.url || menuItem.path;
    const tabTitle = menuItem.label || menuItem.title;
    const isClosable = menuItem.closable !== false; // 默认为true，除非明确设为false
    const params = menuItem.params || null;
    
    // 判断标签页类型
    let tabType = 'list'; // 默认为列表页
    
    // 如果是详情页类型的标签（包含id参数或特定标记）
    if (params && (params.id || params.isDetail)) {
      tabType = 'detail';
      
      // 确保保存原始路径信息
      if (!params.originalPath) {
        params.originalPath = tabName;
      }
    }
    
    // 检查是否已存在该标签页
    let existingTab;
    if (params && params.id) {
      // 如果有ID参数，把ID作为标签页的唯一标识
      existingTab = tabs.value.find(tab => 
        tab.name === tabName && 
        tab.params && 
        tab.params.id === params.id
      );
      
      // 如果找不到匹配的标签页，则添加新标签页
      if (!existingTab) {
        // 对于详情页等，生成包含ID的唯一标签页名称
        const uniqueName = `${tabName}-${params.id}`;
        tabs.value.push({
          title: `${tabTitle}`,
          name: uniqueName,
          closable: isClosable,
          params,
          type: tabType
        });
        activeTab.value = uniqueName;
        return;
      }
    } else {
      // 普通页面，检查标题和路径，避免重复添加同名标签
      // 特别检查"溯源产品管理"和"溯源管理"这两个特殊标签
      existingTab = tabs.value.find(tab => 
        tab.name === tabName || 
        tab.title === tabTitle ||
        (tabTitle === '溯源产品管理' && (tab.title === '溯源管理' || tab.title === '溯源产品管理')) ||
        (tabTitle === '溯源管理' && (tab.title === '溯源产品管理' || tab.title === '溯源管理'))
      );
    }
    
    if (existingTab) {
      // 更新已存在标签页的参数
      if (params) existingTab.params = params;
      activeTab.value = existingTab.name;
      return;
    }
    
    // 添加新标签页
    tabs.value.push({
      title: tabTitle,
      name: tabName,
      closable: isClosable,
      params,
      type: tabType
    });
    
    activeTab.value = tabName;
  }
  
  // 移除标签页
  const removeTab = (targetName: string) => {
    const activeName = activeTab.value;
    
    // 首先获取要关闭的标签页
    const tabToClose = tabs.value.find(tab => tab.name === targetName);
    
    // 检查是否是详情页标签
    const isDetailTab = tabToClose && tabToClose.type === 'detail';
    
    // 如果关闭的是当前激活的标签页，需要激活其他标签页
    if (activeName === targetName) {
      // 如果关闭的是详情页，尝试找到溯源管理页面并激活
      if (isDetailTab) {
        // 查找溯源管理标签页，优先匹配路径，然后匹配标题
        const managementTab = tabs.value.find(tab => 
          tab.name === '/GetProduct' || 
          tab.name === 'GetProduct' ||
          tab.title === '溯源产品管理' || 
          tab.title === '溯源管理'
        );
        
        if (managementTab) {
          // 找到了溯源管理页面，激活它
          activeTab.value = managementTab.name;
          // 确保导航到正确的路由
          const path = managementTab.name.startsWith('/') 
            ? managementTab.name 
            : `/${managementTab.name}`;
          
          router.push({ path });
          
          // 更新标签页数组，移除被关闭的标签页
          tabs.value = tabs.value.filter(tab => tab.name !== targetName);
          return;
        }
      }
      
      // 如果不是详情页或没找到溯源管理页面，使用正常的处理逻辑
      const items = tabs.value;
      let nextActiveIndex = -1;
      
      items.forEach((item, index) => {
        if (item.name === targetName) {
          nextActiveIndex = index === 0 ? (items.length > 1 ? 1 : -1) : index - 1;
        }
      });
      
      if (nextActiveIndex >= 0) {
        activeTab.value = items[nextActiveIndex].name;
        const path = items[nextActiveIndex].name.startsWith('/') 
          ? items[nextActiveIndex].name 
          : `/${items[nextActiveIndex].name}`;
        
        router.push({ path });
      } else {
        // 没有其他标签页可以激活时，显示空白页面
        activeTab.value = '';
        // 不导航到任何路由，保持当前状态但内容为空
      }
    }
    
    // 更新标签页数组，移除被关闭的标签页
    tabs.value = tabs.value.filter(tab => tab.name !== targetName);
  }

  // 添加标签页点击事件处理方法
  const handleTabClick = (tabName: string) => {
    // 设置当前激活的标签页
    activeTab.value = tabName;
    
    // 获取被点击的标签页
    const clickedTab = tabs.value.find(item => item.name === tabName);
    
    if (clickedTab) {
      // 如果是详情页面类型的标签（名称中包含 '-' 且有ID参数）
      if (clickedTab.type === 'detail' && clickedTab.params) {
        // 提取原始路径和参数
        let path;
        
        // 优先使用保存的原始路径
        if (clickedTab.params.originalPath) {
          path = clickedTab.params.originalPath;
        } 
        // 对于使用唯一名称的详情页标签，需要提取原始路径
        else if (tabName.includes('-') && clickedTab.params.id) {
          // 从名称中提取原始路径，格式为 "/path-id"
          path = tabName.split('-')[0];
        } else {
          // 如果标签页有指定的路径参数，使用它
          path = clickedTab.params.path || tabName;
        }
        
        console.log('Navigating to detail page:', path, clickedTab.params);
        
        // 导航到详情页，带上必要的参数
        router.push({
          path: path,
          query: clickedTab.params
        });
      } else {
        // 如果是普通页面或没有参数，直接使用标签页名称作为路由路径
        console.log('Navigating to regular page:', clickedTab.name);
        router.push(clickedTab.name);
      }
    }
  }

  return { 
    count, doubleCount, increment, 
    userInfo, getMenus, menus,
    activeTab, tabs, addTab, removeTab, handleTabClick, clearTabsState
  }
},{
  persist: true
})
