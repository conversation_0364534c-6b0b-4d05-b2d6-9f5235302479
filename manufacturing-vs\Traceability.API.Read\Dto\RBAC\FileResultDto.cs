﻿namespace Traceability.API.Read.Dto.RBAC
{
    /// <summary>
    /// 文件导出Dto
    /// </summary>
    public class FileResultDto
    {
        /// <summary>
        /// 文件内容（字节数组）
        /// </summary>
        public byte[] FileContent { get; set; }
        /// <summary>
        /// 文件类型
        /// </summary>
        public string ContentType { get; set; }
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }
    }
}
