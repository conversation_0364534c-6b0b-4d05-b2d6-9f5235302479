using Manufacturings.Domain;
using System.ComponentModel.DataAnnotations;

namespace Manufacturings.Domain.Entities.Base
{
    /// <summary>
    /// 业务伙伴类别表（合并客户和供应商类别）
    /// </summary>
    public class BusinessPartnerCategory : BaseEntity
    {
        /// <summary>
        /// 类别名称
        /// </summary>
        [Required]
        [StringLength(50)]
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 类别描述
        /// </summary>
        [StringLength(200)]
        public string? Description { get; set; }

        /// <summary>
        /// 伙伴类型（Customer/Supplier/Both）
        /// </summary>
        [Required]
        [StringLength(20)]
        public string PartnerType { get; set; } = "Both";

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
} 